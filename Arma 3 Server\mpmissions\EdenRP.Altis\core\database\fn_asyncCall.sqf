/*
	EdenRP Altis Life - Async Database Call Function
	Author: EdenRP Development Team
	Description: Handles asynchronous database calls with error handling
	Version: 1.0.0
	
	Parameters:
		0: INT - Query ID (0 for immediate, 1+ for queued)
		1: STRING - SQL Query
		2: BOOL - Return single result (optional, default false)
	
	Returns:
		ARRAY - Query results or empty array on error
*/

params [
	["_queryId", 0, [0]],
	["_query", "", [""]],
	["_singleResult", false, [false]]
];

// Validate parameters
if (_query isEqualTo "") exitWith {
	diag_log format ["EDRP Database Error: Empty query provided"];
	[]
};

// Security check - prevent dangerous operations
private _dangerousKeywords = ["DROP", "DELETE", "TRUNCATE", "ALTER", "CREATE", "GRANT", "REVOKE"];
private _upperQuery = toUpper _query;
{
	if (_upperQuery find _x >= 0) exitWith {
		diag_log format ["EDRP Database Error: Dangerous keyword '%1' detected in query", _x];
		_query = "";
	};
} forEach _dangerousKeywords;

if (_query isEqualTo "") exitWith {[]};

// Prepare the database call
private _result = [];
private _startTime = diag_tickTime;

try {
	// Make the database call
	_result = "extDB3" callExtension format ["0:%1:%2", _queryId, _query];
	
	// Parse the result
	if (_result isEqualType "") then {
		_result = parseSimpleArray _result;
	};
	
	// Handle single result requests
	if (_singleResult && {count _result > 0}) then {
		_result = _result select 0;
	};
	
	// Log slow queries (over 1 second)
	private _executionTime = diag_tickTime - _startTime;
	if (_executionTime > 1.0) then {
		diag_log format ["EDRP Database Warning: Slow query detected (%.3fs): %1", _executionTime, _query];
	};
	
} catch {
	diag_log format ["EDRP Database Error: Exception in query execution: %1", _exception];
	_result = [];
};

// Return results
_result
