/*
	EdenRP Altis Life - Briefing System
	Author: EdenRP Development Team
	Description: Handles faction-specific briefings and information display
	Version: 1.0.0
*/

// Main briefing function
EDRP_fnc_briefingSystem = {
	diag_log "EdenRP: Initializing briefing system...";
	
	// Clear existing briefing
	player removeDiarySubject "EdenRP";
	
	// Create main briefing subject
	player createDiarySubject ["EdenRP", "EdenRP Information"];
	
	// Add faction-specific briefings
	switch (playerSide) do {
		case west: {
			call EDRP_fnc_policeBriefing;
		};
		case independent: {
			call EDRP_fnc_medicalBriefing;
		};
		case civilian: {
			call EDRP_fnc_civilianBriefing;
		};
	};
	
	// Add general server information
	call EDRP_fnc_serverBriefing;
	
	// Add rules and guidelines
	call EDRP_fnc_rulesBriefing;
	
	diag_log "EdenRP: Briefing system initialized";
};

// Police briefing
EDRP_fnc_policeBriefing = {
	// Police rank information
	private _rankInfo = "<font color='#0080FF' size='16'>POLICE RANK STRUCTURE</font><br/><br/>";
	{
		_x params ["_name", "_level", "_salary"];
		_rankInfo = _rankInfo + format ["<font color='#FFFFFF'>%1. %2 - $%3/hour</font><br/>", _level, _name, _salary];
	} forEach EDRP_police_ranks;
	
	player createDiaryRecord ["EdenRP", ["Police Ranks", _rankInfo]];
	
	// Police equipment
	private _equipInfo = "<font color='#0080FF' size='16'>POLICE EQUIPMENT</font><br/><br/>";
	_equipInfo = _equipInfo + "<font color='#FFFFFF'>Equipment access is based on your rank:</font><br/><br/>";
	_equipInfo = _equipInfo + "<font color='#FFFF00'>Cadet (Rank 1):</font><br/>";
	_equipInfo = _equipInfo + "• P07 Pistol<br/>";
	_equipInfo = _equipInfo + "• Basic Police Uniform<br/>";
	_equipInfo = _equipInfo + "• Police Vehicle Access<br/><br/>";
	_equipInfo = _equipInfo + "<font color='#FFFF00'>Officer+ (Rank 2+):</font><br/>";
	_equipInfo = _equipInfo + "• SMG Access<br/>";
	_equipInfo = _equipInfo + "• Advanced Vehicles<br/><br/>";
	_equipInfo = _equipInfo + "<font color='#FFFF00'>Sergeant+ (Rank 5+):</font><br/>";
	_equipInfo = _equipInfo + "• Rifle Access<br/>";
	_equipInfo = _equipInfo + "• Helicopter Access<br/>";
	
	player createDiaryRecord ["EdenRP", ["Police Equipment", _equipInfo]];
	
	// Police procedures
	private _procInfo = "<font color='#0080FF' size='16'>POLICE PROCEDURES</font><br/><br/>";
	_procInfo = _procInfo + "<font color='#FFFF00'>Arrest Procedures:</font><br/>";
	_procInfo = _procInfo + "1. Restrain the suspect<br/>";
	_procInfo = _procInfo + "2. Search for illegal items<br/>";
	_procInfo = _procInfo + "3. Issue tickets or process arrest<br/>";
	_procInfo = _procInfo + "4. Transport to jail if necessary<br/><br/>";
	_procInfo = _procInfo + "<font color='#FFFF00'>Traffic Stops:</font><br/>";
	_procInfo = _procInfo + "1. Signal the vehicle to stop<br/>";
	_procInfo = _procInfo + "2. Approach safely<br/>";
	_procInfo = _procInfo + "3. Request license and registration<br/>";
	_procInfo = _procInfo + "4. Issue citation if needed<br/><br/>";
	_procInfo = _procInfo + "<font color='#FFFF00'>Use of Force:</font><br/>";
	_procInfo = _procInfo + "• Use minimum force necessary<br/>";
	_procInfo = _procInfo + "• Lethal force only when threatened<br/>";
	_procInfo = _procInfo + "• Always attempt de-escalation first<br/>";
	
	player createDiaryRecord ["EdenRP", ["Police Procedures", _procInfo]];
	
	// Police locations
	private _locInfo = "<font color='#0080FF' size='16'>POLICE LOCATIONS</font><br/><br/>";
	{
		_x params ["_name", "_pos", "_code"];
		_locInfo = _locInfo + format ["<font color='#FFFFFF'>%1</font><br/>", _name];
	} forEach EDRP_police_spawns;
	
	player createDiaryRecord ["EdenRP", ["Police Locations", _locInfo]];
};

// Medical briefing
EDRP_fnc_medicalBriefing = {
	// Medical rank information
	private _rankInfo = "<font color='#FF8000' size='16'>MEDICAL RANK STRUCTURE</font><br/><br/>";
	{
		_x params ["_name", "_level", "_salary"];
		_rankInfo = _rankInfo + format ["<font color='#FFFFFF'>%1. %2 - $%3/hour</font><br/>", _level, _name, _salary];
	} forEach EDRP_medical_ranks;
	
	player createDiaryRecord ["EdenRP", ["Medical Ranks", _rankInfo]];
	
	// Medical procedures
	private _procInfo = "<font color='#FF8000' size='16'>MEDICAL PROCEDURES</font><br/><br/>";
	_procInfo = _procInfo + "<font color='#FFFF00'>Emergency Response:</font><br/>";
	_procInfo = _procInfo + "1. Respond to emergency calls<br/>";
	_procInfo = _procInfo + "2. Assess patient condition<br/>";
	_procInfo = _procInfo + "3. Provide appropriate treatment<br/>";
	_procInfo = _procInfo + "4. Transport to hospital if needed<br/><br/>";
	_procInfo = _procInfo + "<font color='#FFFF00'>Hospital Operations:</font><br/>";
	_procInfo = _procInfo + "1. Treat incoming patients<br/>";
	_procInfo = _procInfo + "2. Perform medical procedures<br/>";
	_procInfo = _procInfo + "3. Maintain medical records<br/>";
	_procInfo = _procInfo + "4. Coordinate with other departments<br/><br/>";
	_procInfo = _procInfo + "<font color='#FFFF00'>Equipment Usage:</font><br/>";
	_procInfo = _procInfo + "• First Aid Kits for minor injuries<br/>";
	_procInfo = _procInfo + "• Medikit for serious injuries<br/>";
	_procInfo = _procInfo + "• Defibrillator for cardiac arrest<br/>";
	
	player createDiaryRecord ["EdenRP", ["Medical Procedures", _procInfo]];
	
	// Medical locations
	private _locInfo = "<font color='#FF8000' size='16'>MEDICAL LOCATIONS</font><br/><br/>";
	{
		_x params ["_name", "_pos", "_code"];
		_locInfo = _locInfo + format ["<font color='#FFFFFF'>%1</font><br/>", _name];
	} forEach EDRP_medical_spawns;
	
	player createDiaryRecord ["EdenRP", ["Medical Locations", _locInfo]];
};

// Civilian briefing
EDRP_fnc_civilianBriefing = {
	// Job information
	private _jobInfo = "<font color='#00FF00' size='16'>CIVILIAN OCCUPATIONS</font><br/><br/>";
	{
		_x params ["_name", "_code", "_desc"];
		_jobInfo = _jobInfo + format ["<font color='#FFFF00'>%1:</font><br/>%2<br/><br/>", _name, _desc];
	} forEach EDRP_civilian_occupations;
	
	player createDiaryRecord ["EdenRP", ["Civilian Jobs", _jobInfo]];
	
	// Legal activities
	private _legalInfo = "<font color='#00FF00' size='16'>LEGAL ACTIVITIES</font><br/><br/>";
	_legalInfo = _legalInfo + "<font color='#FFFF00'>Farming:</font><br/>";
	_legalInfo = _legalInfo + "• Grow and harvest crops<br/>";
	_legalInfo = _legalInfo + "• Process raw materials<br/>";
	_legalInfo = _legalInfo + "• Sell at markets<br/><br/>";
	_legalInfo = _legalInfo + "<font color='#FFFF00'>Mining:</font><br/>";
	_legalInfo = _legalInfo + "• Extract valuable resources<br/>";
	_legalInfo = _legalInfo + "• Process ores into ingots<br/>";
	_legalInfo = _legalInfo + "• Trade with dealers<br/><br/>";
	_legalInfo = _legalInfo + "<font color='#FFFF00'>Fishing:</font><br/>";
	_legalInfo = _legalInfo + "• Catch fish at designated areas<br/>";
	_legalInfo = _legalInfo + "• Process catch for market<br/>";
	_legalInfo = _legalInfo + "• Sell to fish markets<br/>";
	
	player createDiaryRecord ["EdenRP", ["Legal Activities", _legalInfo]];
	
	// Illegal activities warning
	private _illegalInfo = "<font color='#FF0000' size='16'>ILLEGAL ACTIVITIES</font><br/><br/>";
	_illegalInfo = _illegalInfo + "<font color='#FFFF00'>WARNING:</font> Illegal activities carry significant risks!<br/><br/>";
	_illegalInfo = _illegalInfo + "<font color='#FF8000'>Drug Operations:</font><br/>";
	_illegalInfo = _illegalInfo + "• High profit but high risk<br/>";
	_illegalInfo = _illegalInfo + "• Police actively patrol drug areas<br/>";
	_illegalInfo = _illegalInfo + "• Severe penalties if caught<br/><br/>";
	_illegalInfo = _illegalInfo + "<font color='#FF8000'>Robbery:</font><br/>";
	_illegalInfo = _illegalInfo + "• Banks and stores can be robbed<br/>";
	_illegalInfo = _illegalInfo + "• Requires planning and coordination<br/>";
	_illegalInfo = _illegalInfo + "• Heavy police response expected<br/>";
	
	player createDiaryRecord ["EdenRP", ["Illegal Activities", _illegalInfo]];
	
	// Gang information
	private _gangInfo = "<font color='#800080' size='16'>GANG SYSTEM</font><br/><br/>";
	_gangInfo = _gangInfo + "<font color='#FFFF00'>Gang Benefits:</font><br/>";
	_gangInfo = _gangInfo + "• Shared resources and vehicles<br/>";
	_gangInfo = _gangInfo + "• Territory control<br/>";
	_gangInfo = _gangInfo + "• Group activities and missions<br/>";
	_gangInfo = _gangInfo + "• Private communication channels<br/><br/>";
	_gangInfo = _gangInfo + "<font color='#FFFF00'>Gang Responsibilities:</font><br/>";
	_gangInfo = _gangInfo + "• Follow gang rules and hierarchy<br/>";
	_gangInfo = _gangInfo + "• Contribute to gang activities<br/>";
	_gangInfo = _gangInfo + "• Defend gang territory<br/>";
	_gangInfo = _gangInfo + "• Maintain gang reputation<br/>";
	
	player createDiaryRecord ["EdenRP", ["Gang System", _gangInfo]];
};

// Server information briefing
EDRP_fnc_serverBriefing = {
	private _serverInfo = "<font color='#FFFFFF' size='16'>EDENRP SERVER INFORMATION</font><br/><br/>";
	_serverInfo = _serverInfo + "<font color='#FFFF00'>Server Features:</font><br/>";
	_serverInfo = _serverInfo + "• Dynamic economy system<br/>";
	_serverInfo = _serverInfo + "• Skill-based progression<br/>";
	_serverInfo = _serverInfo + "• Realistic survival mechanics<br/>";
	_serverInfo = _serverInfo + "• Advanced housing system<br/>";
	_serverInfo = _serverInfo + "• Comprehensive vehicle system<br/>";
	_serverInfo = _serverInfo + "• Gang territory control<br/>";
	_serverInfo = _serverInfo + "• Regular server events<br/><br/>";
	_serverInfo = _serverInfo + "<font color='#FFFF00'>Key Bindings:</font><br/>";
	_serverInfo = _serverInfo + "• Y - Main interaction menu<br/>";
	_serverInfo = _serverInfo + "• T - Vehicle interaction<br/>";
	_serverInfo = _serverInfo + "• U - Unlock/lock vehicle<br/>";
	_serverInfo = _serverInfo + "• F - Interact with objects<br/>";
	_serverInfo = _serverInfo + "• H - Holster/unholster weapon<br/>";
	_serverInfo = _serverInfo + "• Shift+P - Phone<br/>";
	_serverInfo = _serverInfo + "• Shift+L - Flashlight<br/>";
	
	player createDiaryRecord ["EdenRP", ["Server Information", _serverInfo]];
};

// Rules briefing
EDRP_fnc_rulesBriefing = {
	private _rulesInfo = "<font color='#FF0000' size='16'>SERVER RULES</font><br/><br/>";
	_rulesInfo = _rulesInfo + "<font color='#FFFF00'>General Rules:</font><br/>";
	_rulesInfo = _rulesInfo + "1. No Random Death Match (RDM)<br/>";
	_rulesInfo = _rulesInfo + "2. No Vehicle Death Match (VDM)<br/>";
	_rulesInfo = _rulesInfo + "3. No exploiting or cheating<br/>";
	_rulesInfo = _rulesInfo + "4. Respect all players and staff<br/>";
	_rulesInfo = _rulesInfo + "5. Stay in character at all times<br/>";
	_rulesInfo = _rulesInfo + "6. No metagaming or powergaming<br/>";
	_rulesInfo = _rulesInfo + "7. Follow New Life Rule (NLR)<br/><br/>";
	_rulesInfo = _rulesInfo + "<font color='#FFFF00'>Roleplay Rules:</font><br/>";
	_rulesInfo = _rulesInfo + "• Initiate before engaging in combat<br/>";
	_rulesInfo = _rulesInfo + "• Value your life and others<br/>";
	_rulesInfo = _rulesInfo + "• Maintain realistic character behavior<br/>";
	_rulesInfo = _rulesInfo + "• Use appropriate communication channels<br/>";
	_rulesInfo = _rulesInfo + "• Follow faction-specific guidelines<br/><br/>";
	_rulesInfo = _rulesInfo + "<font color='#FF8000'>Violations result in warnings, kicks, or bans!</font>";
	
	player createDiaryRecord ["EdenRP", ["Server Rules", _rulesInfo]];
	
	// Contact information
	private _contactInfo = "<font color='#00FFFF' size='16'>CONTACT INFORMATION</font><br/><br/>";
	_contactInfo = _contactInfo + "<font color='#FFFF00'>Discord Server:</font><br/>";
	_contactInfo = _contactInfo + "Join our Discord for updates and support<br/><br/>";
	_contactInfo = _contactInfo + "<font color='#FFFF00'>Website:</font><br/>";
	_contactInfo = _contactInfo + "Visit our website for forums and guides<br/><br/>";
	_contactInfo = _contactInfo + "<font color='#FFFF00'>Support:</font><br/>";
	_contactInfo = _contactInfo + "Contact staff in-game or through Discord<br/>";
	_contactInfo = _contactInfo + "Use /report command for urgent issues<br/>";
	
	player createDiaryRecord ["EdenRP", ["Contact & Support", _contactInfo]];
};

diag_log "EdenRP: Briefing system functions loaded";
