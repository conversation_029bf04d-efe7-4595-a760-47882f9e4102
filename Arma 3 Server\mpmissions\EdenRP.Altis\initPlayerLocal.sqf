/*
	EdenRP Altis Life - Player Local Initialization Script
	Author: EdenRP Development Team
	Description: Client-side player initialization for EdenRP Altis Life
	Version: 1.0.0
*/

params ["_player", "_didJIP"];

// Only run for players with interface
if (!hasInterface) exitWith {};

// Wait for player to be ready
waitUntil {!isNull player && isPlayer player};

// Player initialization message
diag_log format ["EdenRP: Initializing player %1 (JIP: %2)", name player, _didJIP];

// Initialize player variables
player setVariable ["EDRP_player_initialized", false, true];
player setVariable ["EDRP_player_uid", getPlayerUID player, true];
player setVariable ["EDRP_player_name", name player, true];
player setVariable ["EDRP_player_side", playerSide, true];
player setVariable ["EDRP_player_spawn_time", time, true];

// Initialize player stats
player setVariable ["EDRP_player_cash", 0, true];
player setVariable ["EDRP_player_bank", 5000, true];
player setVariable ["EDRP_player_level", 1, true];
player setVariable ["EDRP_player_xp", 0, true];
player setVariable ["EDRP_player_playtime", 0, true];

// Initialize player status
player setVariable ["EDRP_player_wanted", false, true];
player setVariable ["EDRP_player_bounty", 0, true];
player setVariable ["EDRP_player_arrested", false, true];
player setVariable ["EDRP_player_restrained", false, true];
player setVariable ["EDRP_player_escorted", false, true];
player setVariable ["EDRP_player_unconscious", false, true];
player setVariable ["EDRP_player_bleeding", false, true];

// Initialize player needs
player setVariable ["EDRP_player_hunger", 100, true];
player setVariable ["EDRP_player_thirst", 100, true];
player setVariable ["EDRP_player_fatigue", 0, true];
player setVariable ["EDRP_player_health", 100, true];

// Initialize player inventory
player setVariable ["EDRP_player_inventory", [], true];
player setVariable ["EDRP_player_licenses", [], true];
player setVariable ["EDRP_player_gear", [], true];

// Initialize player properties
player setVariable ["EDRP_player_vehicles", [], true];
player setVariable ["EDRP_player_houses", [], true];
player setVariable ["EDRP_player_keys", [], true];

// Initialize player gang
player setVariable ["EDRP_player_gang", "", true];
player setVariable ["EDRP_player_gang_rank", 0, true];

// Initialize player job
player setVariable ["EDRP_player_job", "civilian", true];
player setVariable ["EDRP_player_rank", 1, true];

// Initialize player communication
player setVariable ["EDRP_player_phone", "", true];
player setVariable ["EDRP_player_radio", 0, true];

// Disable damage initially
player allowDamage false;

// Remove default gear
removeAllWeapons player;
removeAllItems player;
removeAllAssignedItems player;
removeUniform player;
removeVest player;
removeBackpack player;
removeHeadgear player;
removeGoggles player;

// Wait for server to be ready
waitUntil {!isNil "EDRP_server_isReady" && EDRP_server_isReady};

// Load player data from server
[getPlayerUID player] remoteExec ["EDRP_fnc_loadPlayerData", 2];

// Wait for player data to load
waitUntil {!isNil "EDRP_player_data_loaded" && EDRP_player_data_loaded};

// Initialize faction-specific systems
switch (playerSide) do {
	case west: { // Police
		player setVariable ["EDRP_player_job", "police", true];
		player setVariable ["EDRP_player_department", "patrol", true];
		player setVariable ["EDRP_player_badge", 0, true];
		
		// Load police gear
		[] call EDRP_fnc_loadPoliceGear;
		
		// Setup police actions
		[] call EDRP_fnc_setupPoliceActions;
		
		// Join police radio channel
		[player, "police"] call EDRP_fnc_joinRadioChannel;
	};
	
	case independent: { // Medical
		player setVariable ["EDRP_player_job", "medical", true];
		player setVariable ["EDRP_player_certification", "emt", true];
		player setVariable ["EDRP_player_hospital", "kavala", true];
		
		// Load medical gear
		[] call EDRP_fnc_loadMedicalGear;
		
		// Setup medical actions
		[] call EDRP_fnc_setupMedicalActions;
		
		// Join medical radio channel
		[player, "medical"] call EDRP_fnc_joinRadioChannel;
	};
	
	case civilian: { // Civilian
		player setVariable ["EDRP_player_job", "civilian", true];
		player setVariable ["EDRP_player_occupation", "unemployed", true];
		
		// Load civilian gear
		[] call EDRP_fnc_loadCivilianGear;
		
		// Setup civilian actions
		[] call EDRP_fnc_setupCivilianActions;
	};
};

// Setup player event handlers
player addEventHandler ["Killed", {
	params ["_unit", "_killer", "_instigator", "_useEffects"];
	
	// Handle player death
	[_unit, _killer, _instigator] call EDRP_fnc_handlePlayerDeath;
}];

player addEventHandler ["Respawn", {
	params ["_unit", "_corpse"];
	
	// Handle player respawn
	[_unit, _corpse] call EDRP_fnc_handlePlayerRespawn;
}];

player addEventHandler ["InventoryOpened", {
	params ["_unit", "_container"];
	
	// Handle inventory access
	[_unit, _container] call EDRP_fnc_handleInventoryAccess;
}];

player addEventHandler ["GetInMan", {
	params ["_unit", "_role", "_vehicle", "_turret"];
	
	// Handle vehicle entry
	[_unit, _role, _vehicle, _turret] call EDRP_fnc_handleVehicleEntry;
}];

player addEventHandler ["GetOutMan", {
	params ["_unit", "_role", "_vehicle", "_turret"];
	
	// Handle vehicle exit
	[_unit, _role, _vehicle, _turret] call EDRP_fnc_handleVehicleExit;
}];

// Setup key bindings
[] call EDRP_fnc_setupKeyBindings;

// Setup UI elements
[] call EDRP_fnc_setupHUD;
[] call EDRP_fnc_setupInventoryUI;
[] call EDRP_fnc_setupPhoneUI;
[] call EDRP_fnc_setupMapUI;

// Start player monitoring loops
[] spawn EDRP_fnc_playerNeedsLoop;
[] spawn EDRP_fnc_playerSyncLoop;
[] spawn EDRP_fnc_playerInteractionLoop;

// Enable damage after initialization
player allowDamage true;

// Show spawn selection if new player
if (isNil {player getVariable "EDRP_player_first_spawn"}) then {
	[] call EDRP_fnc_showSpawnSelection;
	player setVariable ["EDRP_player_first_spawn", false, true];
} else {
	// Spawn at last location
	[] call EDRP_fnc_spawnAtLastLocation;
};

// Show welcome message
if (_didJIP) then {
	["Welcome back to EdenRP!", "You have joined an ongoing session."] call EDRP_fnc_showNotification;
} else {
	["Welcome to EdenRP!", "Enjoy your roleplay experience!"] call EDRP_fnc_showNotification;
};

// Show intro sequence for new players
if (isNil {player getVariable "EDRP_player_intro_seen"}) then {
	[] spawn {
		sleep 5;
		[] call EDRP_fnc_showIntroSequence;
		player setVariable ["EDRP_player_intro_seen", true, true];
	};
};

// Show briefing
[] spawn {
	sleep 2;
	[] call EDRP_fnc_showBriefing;
};

// Mark player as initialized
player setVariable ["EDRP_player_initialized", true, true];

// Notify server that player is ready
[getPlayerUID player, "player_ready"] remoteExec ["EDRP_fnc_playerEvent", 2];

// Start playtime tracking
[] spawn {
	_startTime = time;
	while {alive player} do {
		sleep 60; // Update every minute
		_currentPlaytime = player getVariable ["EDRP_player_playtime", 0];
		player setVariable ["EDRP_player_playtime", _currentPlaytime + 1, true];
	};
};

// Setup admin tools if player is admin
_playerUID = getPlayerUID player;
_adminLevel = 0;
{
	if ((_x select 0) isEqualTo _playerUID) then {
		_adminLevel = _x select 1;
	};
} forEach EDRP_admin_list;

if (_adminLevel > 0) then {
	player setVariable ["EDRP_player_admin_level", _adminLevel, true];
	[] call EDRP_fnc_setupAdminTools;
	[format ["Welcome back, Administrator! (Level %1)", _adminLevel]] call EDRP_fnc_hint;
};

diag_log format ["EdenRP: Player %1 initialization complete", name player];
