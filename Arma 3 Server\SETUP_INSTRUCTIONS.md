# EdenRP Altis Life Server Setup Instructions

## Overview
This document provides comprehensive setup instructions for the EdenRP Altis Life server, a complete rebuild of the Olympus Altis Life framework with 10-25% modifications for originality while maintaining the same gameplay experience.

## Prerequisites

### Software Requirements
- **Arma 3 Dedicated Server** (Latest version)
- **MySQL Server** (8.0 or higher recommended)
- **extDB3** (Latest version)
- **Windows Server 2019/2022** or **Linux** (Ubuntu 20.04+ recommended)

### Hardware Requirements
- **CPU**: Intel i7-8700K or AMD Ryzen 7 2700X (minimum)
- **RAM**: 16GB DDR4 (32GB recommended for 120 players)
- **Storage**: 100GB SSD (for OS, Arma 3, and database)
- **Network**: 1Gbps connection with low latency

## Database Setup

### 1. Install MySQL Server
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server

# Windows
# Download and install MySQL Community Server from official website
```

### 2. Create Database and User
```sql
-- Connect to MySQL as root
mysql -u root -p

-- Create database
CREATE DATABASE edenrp_altis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user
CREATE USER 'edenrp_user'@'localhost' IDENTIFIED BY 'secure_password_123';

-- Grant privileges
GRANT ALL PRIVILEGES ON edenrp_altis.* TO 'edenrp_user'@'localhost';
FLUSH PRIVILEGES;

-- Exit MySQL
EXIT;
```

### 3. Import Database Schema
```bash
# Navigate to the database directory
cd "Arma 3 Server/database"

# Import the schema
mysql -u edenrp_user -p edenrp_altis < edenrp_schema.sql
```

## Server Configuration

### 1. extDB3 Setup
1. Download extDB3 from the official repository
2. Extract `@extDB3` folder to your Arma 3 server directory
3. Copy `extdb3.dll` (Windows) or `extdb3.so` (Linux) to the server root
4. Configure `extdb3-conf.ini`:

```ini
[Database]
Type = MySQL
Name = edenrp_altis
Username = edenrp_user
Password = secure_password_123
IP = 127.0.0.1
Port = 3306
```

### 2. Server.cfg Configuration
```cpp
// Basic Settings
hostname = "EdenRP Altis Life | Roleplay Server | 120 Slots";
password = "";
passwordAdmin = "your_admin_password";
maxPlayers = 120;

// Performance Settings
MinBandwidth = 131072;
MaxBandwidth = 10000000000;
MaxMsgSend = 256;
MaxSizeGuaranteed = 512;
MaxSizeNonguaranteed = 256;
MinErrorToSend = 0.001;
MinErrorToSendNear = 0.01;
MaxCustomFileSize = 0;

// Mission Settings
class Missions {
    class EdenRP {
        template = "EdenRP.Altis";
        difficulty = "Custom";
    };
};

// Slot Distribution
// BLUFOR (Police): 20 slots
// OPFOR (Medical): 15 slots  
// Independent (Civilian): 85 slots
```

### 3. Basic.cfg Configuration
```cpp
MaxMsgSend = 128;
MaxSizeGuaranteed = 512;
MaxSizeNonguaranteed = 256;
MinBandwidth = 131072;
MaxBandwidth = 10000000000;
MinErrorToSend = 0.001;
MinErrorToSendNear = 0.01;
MaxCustomFileSize = 0;
class sockets { maxPacketSize = 1400; };
adapter = -1;
3D_Performance = 1;
Resolution_W = 0;
Resolution_H = 0;
Resolution_Bpp = 32;
terrainGrid = 25;
viewDistance = 1600;
preferredObjectViewDistance = 800;
```

## Mission File Setup

### 1. Mission Structure
Ensure the following directory structure exists:
```
EdenRP.Altis/
├── admin/
├── civilian/
├── communication/
├── core/
├── dialog/
├── gangs/
├── housing/
├── medical/
├── police/
├── progression/
├── robbery/
├── security/
├── shops/
├── testing/
├── vehicles/
├── description.ext
├── Functions.h
├── init.sqf
└── mission.sqm
```

### 2. Key Configuration Files

#### description.ext
- Configure respawn settings
- Set up dialog classes
- Define faction templates
- Configure sound and music

#### mission.sqm
- Set up spawn points for all factions
- Configure map markers
- Place essential objects (ATMs, shops, etc.)

#### init.sqf
- Initialize core systems
- Load player data
- Setup event handlers

## Startup Procedure

### 1. Database Verification
```sql
-- Verify tables exist
USE edenrp_altis;
SHOW TABLES;

-- Check table structure
DESCRIBE players;
DESCRIBE vehicles;
DESCRIBE houses;
DESCRIBE gangs;
```

### 2. Server Launch
```batch
@echo off
title EdenRP Altis Life Server

cd /d "C:\Arma3Server"

echo Starting EdenRP Server...
echo Server Name: EdenRP Altis Life
echo Max Players: 120
echo.

arma3server_x64.exe ^
-port=2302 ^
-config=server.cfg ^
-cfg=basic.cfg ^
-profiles=ServerProfile ^
-name=server ^
-filePatching ^
-mod=@extDB3 ^
-servermod=@extDB3 ^
-world=Altis ^
-autoinit

pause
```

### 3. Linux Launch Script
```bash
#!/bin/bash
cd /opt/arma3server

./arma3server \
-port=2302 \
-config=server.cfg \
-cfg=basic.cfg \
-profiles=ServerProfile \
-name=server \
-filePatching \
-mod=@extDB3 \
-servermod=@extDB3 \
-world=Altis \
-autoinit
```

## Post-Launch Configuration

### 1. Admin Setup
1. Connect to the server
2. Add your Steam64 ID to the admin list in the database:
```sql
INSERT INTO admins (uid, name, level) VALUES ('your_steam64_id', 'YourName', 5);
```

### 2. Economy Configuration
- Adjust starting money in `fn_serverCore.sqf`
- Configure market prices in `fn_shopSystem.sqf`
- Set job payouts in civilian job files

### 3. Faction Balance
- Adjust police equipment levels
- Configure medical equipment access
- Balance civilian job progression

## Testing and Quality Assurance

### 1. System Testing
Run the built-in test suite:
1. Connect as admin (level 3+)
2. Use the "Run All Tests" action
3. Review test results for any failures

### 2. Performance Testing
- Monitor server FPS (should stay above 30)
- Check memory usage (should not exceed 8GB)
- Test with increasing player counts

### 3. Load Testing
- Test with 50, 75, 100, and 120 players
- Monitor database performance
- Check for memory leaks

## Maintenance

### 1. Regular Backups
```bash
# Database backup
mysqldump -u edenrp_user -p edenrp_altis > backup_$(date +%Y%m%d_%H%M%S).sql

# Mission file backup
tar -czf mission_backup_$(date +%Y%m%d_%H%M%S).tar.gz EdenRP.Altis/
```

### 2. Log Monitoring
Monitor these log files:
- `arma3server.log` - Server performance and errors
- `extdb3.log` - Database connection issues
- `admin_logs` table - Admin actions
- `player_logs` table - Player activities

### 3. Performance Optimization
- Clean up abandoned vehicles regularly
- Optimize database queries
- Monitor and clean log files
- Update Arma 3 server regularly

## Troubleshooting

### Common Issues

#### Database Connection Failed
1. Check MySQL service is running
2. Verify credentials in `extdb3-conf.ini`
3. Check firewall settings
4. Test connection manually

#### Players Can't Connect
1. Check server.cfg port settings
2. Verify firewall rules (ports 2302-2306)
3. Check mission file integrity
4. Review server logs for errors

#### Poor Performance
1. Check server hardware utilization
2. Optimize database queries
3. Reduce view distance settings
4. Clean up unused objects

#### Faction Imbalance
1. Adjust slot distribution in mission.sqm
2. Review faction equipment balance
3. Monitor player statistics
4. Adjust progression rates

## Security Considerations

### 1. Server Security
- Use strong admin passwords
- Regularly update server software
- Monitor for suspicious activity
- Implement proper firewall rules

### 2. Anti-Cheat Configuration
- Enable built-in security system
- Configure violation thresholds
- Set up admin notifications
- Regular security audits

### 3. Database Security
- Use strong database passwords
- Limit database user privileges
- Regular security updates
- Monitor database access logs

## Support and Updates

### 1. Version Control
- Keep backups of all configuration changes
- Document all modifications
- Test updates in development environment
- Maintain rollback procedures

### 2. Community Management
- Set up Discord/forums for community
- Train admin staff properly
- Establish clear rules and procedures
- Regular community events

### 3. Monitoring
- Set up server monitoring tools
- Configure alerting for issues
- Regular performance reviews
- Player feedback collection

## Conclusion

This setup guide provides a comprehensive foundation for running the EdenRP Altis Life server. Regular maintenance, monitoring, and community engagement are key to a successful roleplay server.

For additional support or questions, refer to the EdenRP development team documentation or community forums.

---
**EdenRP Development Team**  
Version 1.0.0 - Initial Release
