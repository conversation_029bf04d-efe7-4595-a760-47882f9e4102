-- --------------------------------------------------------
-- EdenRP Database Setup Script
-- Author: EdenRP Development Team
-- Description: Complete database setup for EdenRP server
-- Version: 1.0.0
-- --------------------------------------------------------

-- Create database user for EdenRP
CREATE USER IF NOT EXISTS 'edenrp_user'@'localhost' IDENTIFIED BY 'edenrp_secure_password_2024';
CREATE USER IF NOT EXISTS 'edenrp_user'@'%' IDENTIFIED BY 'edenrp_secure_password_2024';

-- Grant necessary privileges
GRANT SELECT, INSERT, UPDATE, DELETE ON edenrp.* TO 'edenrp_user'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON edenrp.* TO 'edenrp_user'@'%';

-- Grant procedure execution privileges
GRANT EXECUTE ON edenrp.* TO 'edenrp_user'@'localhost';
GRANT EXECUTE ON edenrp.* TO 'edenrp_user'@'%';

-- Flush privileges to apply changes
FLUSH PRIVILEGES;

-- Use the EdenRP database
USE edenrp;

-- --------------------------------------------------------
-- Create additional indexes for performance
-- --------------------------------------------------------

-- Player-related indexes
CREATE INDEX IF NOT EXISTS `idx_players_name_search` ON `edrp_players` (`name`(10));
CREATE INDEX IF NOT EXISTS `idx_players_faction_rank` ON `edrp_players` (`police_rank`, `medical_rank`);
CREATE INDEX IF NOT EXISTS `idx_players_donator` ON `edrp_players` (`donator_level`);

-- Vehicle-related indexes
CREATE INDEX IF NOT EXISTS `idx_vehicles_classname` ON `edrp_vehicles` (`classname`);
CREATE INDEX IF NOT EXISTS `idx_vehicles_status` ON `edrp_vehicles` (`alive`, `active`, `impounded`);
CREATE INDEX IF NOT EXISTS `idx_vehicles_insurance` ON `edrp_vehicles` (`insured`, `insurance_expires`);

-- Gang-related indexes
CREATE INDEX IF NOT EXISTS `idx_gangs_status` ON `edrp_gangs` (`active`, `verified`);
CREATE INDEX IF NOT EXISTS `idx_gang_members_rank` ON `edrp_gang_members` (`rank`);

-- House-related indexes
CREATE INDEX IF NOT EXISTS `idx_houses_status` ON `edrp_houses` (`owned`, `for_sale`, `locked`);
CREATE INDEX IF NOT EXISTS `idx_houses_type` ON `edrp_houses` (`house_type`);

-- Transaction indexes
CREATE INDEX IF NOT EXISTS `idx_transactions_amount` ON `edrp_transactions` (`amount`);
CREATE INDEX IF NOT EXISTS `idx_transactions_type_date` ON `edrp_transactions` (`transaction_type`, `timestamp`);

-- Communication indexes
CREATE INDEX IF NOT EXISTS `idx_messages_unread` ON `edrp_phone_messages` (`recipient_id`, `read_status`);

-- --------------------------------------------------------
-- Create views for common queries
-- --------------------------------------------------------

-- Active players view
CREATE OR REPLACE VIEW `view_active_players` AS
SELECT 
    p.uid,
    p.name,
    p.playerid,
    p.cash,
    p.bank_account,
    p.police_rank,
    p.medical_rank,
    p.admin_level,
    p.last_side,
    p.last_active,
    TIMESTAMPDIFF(MINUTE, p.last_active, NOW()) as minutes_offline
FROM edrp_players p
WHERE p.last_active > DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY p.last_active DESC;

-- Gang overview view
CREATE OR REPLACE VIEW `view_gang_overview` AS
SELECT 
    g.id,
    g.name,
    g.tag,
    g.leader_id,
    g.bank_balance,
    g.level,
    g.total_kills,
    g.total_deaths,
    g.territory_controlled,
    COUNT(gm.id) as member_count,
    g.created_date
FROM edrp_gangs g
LEFT JOIN edrp_gang_members gm ON g.id = gm.gang_id
WHERE g.active = 1
GROUP BY g.id
ORDER BY g.level DESC, g.total_kills DESC;

-- Vehicle ownership view
CREATE OR REPLACE VIEW `view_vehicle_ownership` AS
SELECT 
    v.id,
    v.owner_id,
    p.name as owner_name,
    v.classname,
    v.vehicle_type,
    v.custom_name,
    v.license_plate,
    v.alive,
    v.active,
    v.impounded,
    v.insured,
    v.purchased_date
FROM edrp_vehicles v
LEFT JOIN edrp_players p ON v.owner_id = p.playerid
WHERE v.alive = 1
ORDER BY v.purchased_date DESC;

-- Market summary view
CREATE OR REPLACE VIEW `view_market_summary` AS
SELECT 
    item_name,
    base_price,
    current_price,
    ROUND(((current_price - base_price) / base_price) * 100, 2) as price_change_percent,
    demand_level,
    supply_level,
    last_updated
FROM edrp_market_data
ORDER BY price_change_percent DESC;

-- --------------------------------------------------------
-- Create triggers for data integrity
-- --------------------------------------------------------

-- Trigger to update gang member count when members are added/removed
DELIMITER //

CREATE TRIGGER `update_gang_activity` 
AFTER INSERT ON `edrp_gang_members`
FOR EACH ROW
BEGIN
    UPDATE edrp_gangs 
    SET last_active = NOW() 
    WHERE id = NEW.gang_id;
END//

CREATE TRIGGER `update_gang_activity_delete` 
AFTER DELETE ON `edrp_gang_members`
FOR EACH ROW
BEGIN
    UPDATE edrp_gangs 
    SET last_active = NOW() 
    WHERE id = OLD.gang_id;
END//

-- Trigger to log important player changes
CREATE TRIGGER `log_player_rank_changes` 
AFTER UPDATE ON `edrp_players`
FOR EACH ROW
BEGIN
    IF OLD.police_rank != NEW.police_rank THEN
        INSERT INTO edrp_server_logs (log_type, player_id, action, details)
        VALUES ('admin', NEW.playerid, 'rank_change', 
                CONCAT('Police rank changed from ', OLD.police_rank, ' to ', NEW.police_rank));
    END IF;
    
    IF OLD.medical_rank != NEW.medical_rank THEN
        INSERT INTO edrp_server_logs (log_type, player_id, action, details)
        VALUES ('admin', NEW.playerid, 'rank_change', 
                CONCAT('Medical rank changed from ', OLD.medical_rank, ' to ', NEW.medical_rank));
    END IF;
    
    IF OLD.admin_level != NEW.admin_level THEN
        INSERT INTO edrp_server_logs (log_type, player_id, action, details)
        VALUES ('admin', NEW.playerid, 'admin_change', 
                CONCAT('Admin level changed from ', OLD.admin_level, ' to ', NEW.admin_level));
    END IF;
END//

-- Trigger to automatically expire wanted entries
CREATE TRIGGER `check_wanted_expiry` 
BEFORE SELECT ON `edrp_wanted_list`
FOR EACH ROW
BEGIN
    UPDATE edrp_wanted_list 
    SET active = 0 
    WHERE expires_date IS NOT NULL 
    AND expires_date < NOW() 
    AND active = 1;
END//

DELIMITER ;

-- --------------------------------------------------------
-- Create events for automated maintenance
-- --------------------------------------------------------

-- Enable event scheduler
SET GLOBAL event_scheduler = ON;

-- Event to clean up old logs
CREATE EVENT IF NOT EXISTS `cleanup_old_logs`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanupOldLogs(30);

-- Event to update market prices
CREATE EVENT IF NOT EXISTS `update_market_prices`
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
  CALL UpdateMarketPrices();

-- Event to reset vehicle activity on restart
CREATE EVENT IF NOT EXISTS `reset_vehicle_activity`
ON SCHEDULE EVERY 4 HOUR
STARTS CURRENT_TIMESTAMP
DO
  CALL ResetVehicleActivity();

-- --------------------------------------------------------
-- Insert sample data for testing (optional)
-- --------------------------------------------------------

-- Sample admin player
INSERT IGNORE INTO `edrp_players` 
(`name`, `playerid`, `cash`, `bank_account`, `admin_level`, `police_rank`, `medical_rank`) 
VALUES 
('EdenRP Admin', '*****************', 100000, 1000000, '5', '10', '10');

-- Sample gang
INSERT IGNORE INTO `edrp_gangs` 
(`name`, `tag`, `leader_id`, `bank_balance`, `level`) 
VALUES 
('The Syndicate', 'SYN', '*****************', 500000, 5);

-- Sample territories
INSERT IGNORE INTO `edrp_territories` 
(`territory_name`, `territory_type`, `position_data`, `max_capture_points`, `income_rate`) 
VALUES 
('Drug Lab Alpha', 'drug', '{"x": 14000, "y": 16000, "z": 0}', 100, 5000),
('Copper Mine', 'resource', '{"x": 16000, "y": 12000, "z": 0}', 100, 2500),
('Gang Hideout', 'gang_base', '{"x": 18000, "y": 14000, "z": 0}', 100, 1000);

-- --------------------------------------------------------
-- Final setup confirmation
-- --------------------------------------------------------

SELECT 'EdenRP Database Setup Complete!' as Status;
SELECT COUNT(*) as Total_Tables FROM information_schema.tables WHERE table_schema = 'edenrp';
SELECT COUNT(*) as Total_Views FROM information_schema.views WHERE table_schema = 'edenrp';
SELECT COUNT(*) as Total_Procedures FROM information_schema.routines WHERE routine_schema = 'edenrp' AND routine_type = 'PROCEDURE';
SELECT COUNT(*) as Total_Events FROM information_schema.events WHERE event_schema = 'edenrp';
