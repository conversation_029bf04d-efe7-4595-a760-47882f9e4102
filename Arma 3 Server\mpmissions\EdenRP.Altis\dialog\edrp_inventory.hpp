/*
	EdenRP Altis Life - Inventory System
	Author: EdenRP Development Team
	Description: Player inventory management interface
	Version: 1.0.0
*/

class EDRP_InventoryMenu: EDRP_MenuBase {
	idd = 3500;
	onLoad = "[] spawn EDRP_fnc_updateInventory;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "EdenRP - Inventory Management";
		};
		class Header: BaseHeader {
			text = "Manage Your Items and Equipment";
		};
		
		// Inventory sections
		class VirtualInventoryBackground: EDRP_RscText {
			idc = -1;
			x = 0.21;
			y = 0.26;
			w = 0.28;
			h = 0.52;
			colorBackground[] = {0.05, 0.05, 0.05, 0.9};
		};
		
		class PhysicalInventoryBackground: EDRP_RscText {
			idc = -1;
			x = 0.51;
			y = 0.26;
			w = 0.28;
			h = 0.52;
			colorBackground[] = {0.05, 0.05, 0.05, 0.9};
		};
		
		// Weight display backgrounds
		class VirtualWeightBackground: EDRP_RscText {
			idc = -1;
			x = 0.21;
			y = 0.78;
			w = 0.28;
			h = 0.03;
			colorBackground[] = {0.1, 0.1, 0.1, 0.8};
		};
		
		class PhysicalWeightBackground: EDRP_RscText {
			idc = -1;
			x = 0.51;
			y = 0.78;
			w = 0.28;
			h = 0.03;
			colorBackground[] = {0.1, 0.1, 0.1, 0.8};
		};
	};
	
	class controls: controlsBase {
		// Virtual inventory section
		class VirtualInventoryTitle: EDRP_RscText {
			idc = -1;
			text = "Virtual Inventory";
			x = 0.21;
			y = 0.24;
			w = 0.28;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			sizeEx = 0.03;
			font = "RobotoCondensedBold";
		};
		
		class VirtualInventoryList: EDRP_RscListBox {
			idc = 3501;
			x = 0.22;
			y = 0.27;
			w = 0.26;
			h = 0.45;
			sizeEx = 0.03;
			onLBSelChanged = "[3501] call EDRP_fnc_inventorySelectionChanged;";
			onLBDblClick = "[3501, 'use'] call EDRP_fnc_inventoryDoubleClick;";
		};
		
		class VirtualWeightText: EDRP_RscText {
			idc = 3502;
			text = "Weight: 0 / 100 kg";
			x = 0.22;
			y = 0.78;
			w = 0.26;
			h = 0.03;
			style = 2;
			sizeEx = 0.025;
		};
		
		// Physical inventory section
		class PhysicalInventoryTitle: EDRP_RscText {
			idc = -1;
			text = "Physical Inventory";
			x = 0.51;
			y = 0.24;
			w = 0.28;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			sizeEx = 0.03;
			font = "RobotoCondensedBold";
		};
		
		class PhysicalInventoryList: EDRP_RscListBox {
			idc = 3503;
			x = 0.52;
			y = 0.27;
			w = 0.26;
			h = 0.45;
			sizeEx = 0.03;
			onLBSelChanged = "[3503] call EDRP_fnc_inventorySelectionChanged;";
			onLBDblClick = "[3503, 'use'] call EDRP_fnc_inventoryDoubleClick;";
		};
		
		class PhysicalWeightText: EDRP_RscText {
			idc = 3504;
			text = "Weight: 0 / 50 kg";
			x = 0.52;
			y = 0.78;
			w = 0.26;
			h = 0.03;
			style = 2;
			sizeEx = 0.025;
		};
		
		// Action buttons
		class UseItemButton: EDRP_RscButtonMenu {
			idc = 3510;
			text = "Use Item";
			x = 0.21;
			y = 0.82;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_useSelectedItem;";
		};
		
		class DropItemButton: EDRP_RscButtonMenu {
			idc = 3511;
			text = "Drop Item";
			x = 0.30;
			y = 0.82;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_dropSelectedItem;";
		};
		
		class GiveItemButton: EDRP_RscButtonMenu {
			idc = 3512;
			text = "Give Item";
			x = 0.39;
			y = 0.82;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_giveSelectedItem;";
		};
		
		class MoveItemButton: EDRP_RscButtonMenu {
			idc = 3513;
			text = "Move Item";
			x = 0.48;
			y = 0.82;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			onButtonClick = "[] call EDRP_fnc_moveSelectedItem;";
		};
		
		class SellItemButton: EDRP_RscButtonMenu {
			idc = 3514;
			text = "Sell Item";
			x = 0.57;
			y = 0.82;
			w = 0.08;
			h = 0.04;
			colorBackground[] = {0.8, 0.6, 0.2, 0.8};
			onButtonClick = "[] call EDRP_fnc_sellSelectedItem;";
		};
		
		class ProcessItemButton: EDRP_RscButtonMenu {
			idc = 3515;
			text = "Process";
			x = 0.66;
			y = 0.82;
			w = 0.08;
			h = 0.04;
			colorBackground[] = {0.6, 0.2, 0.8, 0.8};
			onButtonClick = "[] call EDRP_fnc_processSelectedItem;";
		};
		
		// Quantity input
		class QuantityEdit: EDRP_RscEdit {
			idc = 3520;
			text = "1";
			x = 0.21;
			y = 0.87;
			w = 0.08;
			h = 0.03;
		};
		
		class QuantityLabel: EDRP_RscText {
			idc = -1;
			text = "Quantity:";
			x = 0.21;
			y = 0.85;
			w = 0.08;
			h = 0.02;
			sizeEx = 0.025;
		};
		
		// Player selection for giving items
		class NearbyPlayersCombo: EDRP_RscCombo {
			idc = 3521;
			x = 0.30;
			y = 0.87;
			w = 0.15;
			h = 0.03;
		};
		
		class NearbyPlayersLabel: EDRP_RscText {
			idc = -1;
			text = "Nearby Players:";
			x = 0.30;
			y = 0.85;
			w = 0.15;
			h = 0.02;
			sizeEx = 0.025;
		};
		
		// Item information display
		class ItemInfoText: EDRP_RscStructuredText {
			idc = 3530;
			text = "Select an item to view information";
			x = 0.48;
			y = 0.85;
			w = 0.31;
			h = 0.05;
			size = 0.025;
		};
		
		// Close button
		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 3500; [] call EDRP_fnc_closeInventory;";
		};
	};
};

// Item transfer dialog
class EDRP_ItemTransferDialog: EDRP_MenuBaseCompact {
	idd = 3600;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Transfer Item";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class ItemNameText: EDRP_RscText {
			idc = 3601;
			text = "";
			x = 0.32;
			y = 0.28;
			w = 0.36;
			h = 0.03;
			style = 2;
			sizeEx = 0.035;
			font = "RobotoCondensedBold";
		};
		
		class QuantitySlider: EDRP_RscProgress {
			idc = 3602;
			x = 0.32;
			y = 0.35;
			w = 0.36;
			h = 0.02;
		};
		
		class QuantityEdit: EDRP_RscEdit {
			idc = 3603;
			text = "1";
			x = 0.32;
			y = 0.4;
			w = 0.15;
			h = 0.04;
		};
		
		class MaxQuantityText: EDRP_RscText {
			idc = 3604;
			text = "Max: 0";
			x = 0.50;
			y = 0.4;
			w = 0.18;
			h = 0.04;
		};
		
		class QuantityLabel: EDRP_RscText {
			idc = -1;
			text = "Quantity to transfer:";
			x = 0.32;
			y = 0.32;
			w = 0.36;
			h = 0.03;
		};
		
		class TransferButton: BaseButtonOK {
			text = "Transfer";
			onButtonClick = "[] call EDRP_fnc_confirmItemTransfer; closeDialog 3600;";
		};
		
		class ButtonClose: BaseButtonClose {};
	};
};

// Item details dialog
class EDRP_ItemDetailsDialog: EDRP_MenuBaseCompact {
	idd = 3700;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Item Details";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class ItemIcon: EDRP_RscPicture {
			idc = 3701;
			x = 0.32;
			y = 0.28;
			w = 0.08;
			h = 0.08;
		};
		
		class ItemNameText: EDRP_RscText {
			idc = 3702;
			text = "";
			x = 0.42;
			y = 0.28;
			w = 0.26;
			h = 0.03;
			sizeEx = 0.035;
			font = "RobotoCondensedBold";
		};
		
		class ItemDescriptionText: EDRP_RscStructuredText {
			idc = 3703;
			text = "";
			x = 0.32;
			y = 0.38;
			w = 0.36;
			h = 0.25;
			size = 0.03;
		};
		
		class ItemStatsText: EDRP_RscStructuredText {
			idc = 3704;
			text = "";
			x = 0.42;
			y = 0.31;
			w = 0.26;
			h = 0.06;
			size = 0.025;
		};
		
		class ButtonClose: BaseButtonClose {};
	};
};
