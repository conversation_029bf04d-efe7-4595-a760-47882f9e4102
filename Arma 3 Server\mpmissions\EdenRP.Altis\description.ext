author="EdenRP Development Team";
onLoadName = "EdenRP Altis Life";
onLoadMission = "An immersive roleplay experience developed by the EdenRP Team.";
overviewText = "Experience the ultimate Altis Life roleplay server with enhanced features and balanced gameplay.";
overviewTextLocked = "Experience the ultimate Altis Life roleplay server with enhanced features and balanced gameplay.";
overviewPicture = "images\edenrp_logo.paa";
loadScreen = "images\edenrp_loading.paa";
OnLoadIntroTime = 0;
OnLoadMissionTime = 0;
allowFunctionsLog = 1;
allowFunctionsRecompile = 0;
briefing = 0;

joinUnassigned = 1;
respawn = BASE;
disabledAI = 1;
disableChannels[]={0,1,2};
enableDebugConsole = 1;
respawnDialog = 0;
scriptedPlayer = 1;
forceRotorLibSimulation = 0;

class Header {
	gameType = RPG;
	minPlayers = 1;
	maxPlayers = 120;
};

respawndelay = 1;
wreckLimit = 5;
wreckRemovalMinTime = 90;
wreckRemovalMaxTime = 480;
corpseLimit = 100;
corpseRemovalMinTime = 900;
corpseRemovalMaxTime = 1800;
unsafeCVL = 1;

showHUD[] = {1,1,0,1,0,0,1,1,1};

#include "dialog\MasterHandler.hpp"

class RscTitles {
	#include "dialog\ui.hpp"
	#include "dialog\progress.hpp"
	#include "dialog\hud_nameTags.hpp"
};

class CfgFunctions {
	#include "Functions.h"
};

class CfgRemoteExec {
	#include "CfgRE.hpp"
};

// EdenRP Configuration Files
#include "Config_Gather.hpp"
#include "Config_Icons.hpp"
#include "Config_Process.hpp"
#include "Config_Vehicles.hpp"
#include "Config_Shops.hpp"
#include "Config_Licenses.hpp"
#include "Config_Items.hpp"

class CfgSounds {
	sounds[] = {};

	class breathingHeavy {
		name = "breathingHeavy";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person0\P0_choke_02.wss", 2, 1};
		titles[] = {};
	};

	class breathingLabored {
		name = "breathingLabored";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person2\P2_choke_05.wss", 2, 1};
		titles[] = {};
	};

	class breathingWeak {
		name = "breathingWeak";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\P06\Soundbreathinjured_Max_2.wss", 2, 1};
		titles[] = {};
	};

	class breathingCritical {
		name = "breathingCritical";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person3\P3_choke_02.wss", 2, 1};
		titles[] = {};
	};

	class breathingFinal {
		name = "breathingFinal";
		sound[] = {"@A3\Sounds_f\characters\human-sfx\Person2\P2_choke_04.wss", 2, 1};
		titles[] = {};
	};

	class police_siren_wail {
		name = "police_siren_wail";
		sound[] = {"\sounds\police_wail.ogg", 5, 1};
		titles[] = {};
	};

	class police_siren_yelp {
		name = "police_siren_yelp";
		sound[] = {"\sounds\police_yelp.ogg", 5, 1};
		titles[] = {};
	};

	class police_horn {
		name = "police_horn";
		sound[] = {"\sounds\police_horn.ogg", 5, 1};
		titles[] = {};
	};

	class medical_siren_wail {
		name = "medical_siren_wail";
		sound[] = {"\sounds\medical_wail.ogg", 1.8, 1};
		titles[] = {};
	};

	class medical_siren_yelp {
		name = "medical_siren_yelp";
		sound[] = {"\sounds\medical_yelp.ogg", 1.8, 1};
		titles[] = {};
	};

	class medical_horn {
		name = "medical_horn";
		sound[] = {"\sounds\medical_horn.ogg", 1.6, 1};
		titles[] = {};
	};

	class flashbang_explosion {
		name = "flashbang_explosion";
		sound[] = {"\sounds\flashbang.ogg", 1.1, 1};
		titles[] = {};
	};

	class vehicle_alarm {
		name = "vehicle_alarm";
		sound[] = {"\sounds\vehicle_alarm.ogg", 1.5, 1};
		titles[] = {};
	};

	class handcuff_sound {
		name = "handcuff_sound";
		sound[] = {"\sounds\handcuffs.ogg", 0.9, 0.95};
		titles[] = {};
	};

	class citation_print {
		name="citation_print";
		sound[] = {"\sounds\citation.ogg", 0.5, 1};
		titles[] = {0, " "};
	};

	class lock_pick {
		name = "lock_pick";
		sound[] = {"\sounds\lockpick.ogg", 0.8, 1};
		titles[] = {};
	};

	class vehicle_lock {
		name = "vehicle_lock";
		sound[] = {"\sounds\car_lock.ogg", 0.8, 1};
		titles[] = {};
	};

	class bank_security_alarm {
		name = "bank_security_alarm";
		sound[] = {"\sounds\bank_alarm.ogg", 1.2, 1};
		titles[] = {};
	};

	class taser_shock {
		name = "taser_shock";
		sound[] = {"\sounds\taser.ogg", 1.5, 1};
		titles[] = {};
	};

	class missile_lock_warning {
		name = "missile_lock_warning";
		sound[] = {"\sounds\missile_locking.ogg", 1.0, 1};
		titles[] = {};
	};

	class missile_locked_warning {
		name = "missile_locked_warning";
		sound[] = {"\sounds\missile_locked.ogg", 1.0, 1};
		titles[] = {};
	};

	class air_horn {
		name = "air_horn";
		sound[] = {"\sounds\air_horn.ogg", 2, 1};
		titles[] = {};
	};

	class emergency_horn {
		name = "emergency_horn";
		sound[] = {"\sounds\emergency_horn.ogg", 2, 1};
		titles[] = {};
	};

	class notification_sound {
		name = "notification_sound";
		sound[] = {"\sounds\notification.ogg", 2, 1};
		titles[] = {};
	};

	class truck_horn {
		name = "truck_horn";
		sound[] = {"\sounds\truck_horn.ogg", 2, 1};
		titles[] = {};
	};

	class boat_horn {
		name = "boat_horn";
		sound[] = {"\sounds\boat_horn.ogg", 2, 1};
		titles[] = {};
	};

	class helicopter_horn {
		name = "helicopter_horn";
		sound[] = {"\sounds\heli_horn.ogg", 2, 1};
		titles[] = {};
	};

	class plane_horn {
		name = "plane_horn";
		sound[] = {"\sounds\plane_horn.ogg", 2, 1};
		titles[] = {};
	};

	class radio_beep {
		name = "radio_beep";
		sound[] = {"\sounds\radio_beep.ogg", 1, 1};
		titles[] = {};
	};

	class phone_ring {
		name = "phone_ring";
		sound[] = {"\sounds\phone_ring.ogg", 1, 1};
		titles[] = {};
	};

	class text_message {
		name = "text_message";
		sound[] = {"\sounds\text_notification.ogg", 0.8, 1};
		titles[] = {};
	};

	class cash_register {
		name = "cash_register";
		sound[] = {"\sounds\cash_register.ogg", 1, 1};
		titles[] = {};
	};

	class drill_sound {
		name = "drill_sound";
		sound[] = {"\sounds\drill.ogg", 1.5, 1};
		titles[] = {};
	};

	class explosion_small {
		name = "explosion_small";
		sound[] = {"\sounds\small_explosion.ogg", 2, 1};
		titles[] = {};
	};

	class door_breach {
		name = "door_breach";
		sound[] = {"\sounds\door_breach.ogg", 1.8, 1};
		titles[] = {};
	};

	class safe_crack {
		name = "safe_crack";
		sound[] = {"\sounds\safe_crack.ogg", 1.2, 1};
		titles[] = {};
	};

	class mining_sound {
		name = "mining_sound";
		sound[] = {"\sounds\mining.ogg", 1, 1};
		titles[] = {};
	};

	class chopping_wood {
		name = "chopping_wood";
		sound[] = {"\sounds\wood_chop.ogg", 1, 1};
		titles[] = {};
	};

	class fishing_splash {
		name = "fishing_splash";
		sound[] = {"\sounds\fishing.ogg", 0.8, 1};
		titles[] = {};
	};

	class processing_machine {
		name = "processing_machine";
		sound[] = {"\sounds\processing.ogg", 1, 1};
		titles[] = {};
	};

	class atm_beep {
		name = "atm_beep";
		sound[] = {"\sounds\atm.ogg", 0.6, 1};
		titles[] = {};
	};

	class garage_door {
		name = "garage_door";
		sound[] = {"\sounds\garage.ogg", 1.2, 1};
		titles[] = {};
	};

	class house_door {
		name = "house_door";
		sound[] = {"\sounds\house_door.ogg", 0.8, 1};
		titles[] = {};
	};

	class inventory_move {
		name = "inventory_move";
		sound[] = {"\sounds\inventory.ogg", 0.4, 1};
		titles[] = {};
	};

	class level_up {
		name = "level_up";
		sound[] = {"\sounds\level_up.ogg", 1, 1};
		titles[] = {};
	};

	class achievement_unlock {
		name = "achievement_unlock";
		sound[] = {"\sounds\achievement.ogg", 1, 1};
		titles[] = {};
	};
};

#include "Config_Gather.hpp"
#include "Config_Icons.hpp"
#include "Config_Skins.hpp"
#include "Config_Titles.hpp"
#include "Config_physWeights.hpp"
