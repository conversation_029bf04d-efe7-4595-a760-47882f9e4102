/*
	EdenRP Altis Life - House Storage System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: House inventory and storage management
	Version: 1.0.0
*/

// Open house inventory
EDRP_fnc_openHouseInventory = {
	params [["_house", objNull, [obj<PERSON><PERSON>]]];
	
	if (isNull _house || !(_house isKindOf "House_F")) exitWith {
		["Invalid house"] call EDRP_fnc_hint;
		false
	};
	
	// Check access
	if !([_house] call EDRP_fnc_hasHouseAccess) exitWith {
		["You don't have access to this house"] call EDRP_fnc_hint;
		false
	};
	
	// Check if house is locked
	if (_house getVariable ["locked", true]) exitWith {
		["House storage is locked"] call EDRP_fnc_hint;
		false
	};
	
	// Store current house
	EDRP_current_storage_house = _house;
	
	// Create inventory dialog
	createDialog "EDRP_HouseInventoryDialog";
	
	// Update inventory display
	[] call EDRP_fnc_updateHouseInventory;
	
	// Set player variable for tracking
	player setVariable ["inHouseInventory", [true, _house getVariable ["house_id", -1]], true];
	
	true
};

// Update house inventory display
EDRP_fnc_updateHouseInventory = {
	private _display = findDisplay 4500;
	if (isNull _display || isNil "EDRP_current_storage_house") exitWith {};
	
	private _house = EDRP_current_storage_house;
	
	// Update title
	private _titleCtrl = _display displayCtrl 4501;
	_titleCtrl ctrlSetText format["House Storage - %1", _house getVariable ["house_id", -1]];
	
	// Get storage data
	private _virtualInventory = _house getVariable ["virtual_inventory", [[], 0]];
	private _physicalInventory = _house getVariable ["physical_inventory", [[], 0]];
	private _virtualCapacity = _house getVariable ["virtual_storage_capacity", 100];
	private _physicalCapacity = _house getVariable ["physical_storage_capacity", 100];
	
	// Update virtual storage list
	private _virtualList = _display displayCtrl 4502;
	lbClear _virtualList;
	
	{
		_x params ["_item", "_quantity"];
		private _itemName = [_item] call EDRP_fnc_getItemName;
		private _entry = format ["%1 x%2", _itemName, _quantity];
		
		_virtualList lbAdd _entry;
		_virtualList lbSetData [_forEachIndex, _item];
		_virtualList lbSetValue [_forEachIndex, _quantity];
	} forEach (_virtualInventory select 0);
	
	// Update physical storage list
	private _physicalList = _display displayCtrl 4503;
	lbClear _physicalList;
	
	{
		_x params ["_item", "_quantity"];
		private _itemName = [_item] call EDRP_fnc_getItemName;
		private _entry = format ["%1 x%2", _itemName, _quantity];
		
		_physicalList lbAdd _entry;
		_physicalList lbSetData [_forEachIndex, _item];
		_physicalList lbSetValue [_forEachIndex, _quantity];
	} forEach (_physicalInventory select 0);
	
	// Update capacity displays
	private _virtualCapacityCtrl = _display displayCtrl 4504;
	_virtualCapacityCtrl ctrlSetText format["Virtual: %1/%2", (_virtualInventory select 1), _virtualCapacity];
	
	private _physicalCapacityCtrl = _display displayCtrl 4505;
	_physicalCapacityCtrl ctrlSetText format["Physical: %1/%2", (_physicalInventory select 1), _physicalCapacity];
	
	// Update player inventory list
	private _playerList = _display displayCtrl 4506;
	lbClear _playerList;
	
	{
		_x params ["_item", "_quantity"];
		private _itemName = [_item] call EDRP_fnc_getItemName;
		private _entry = format ["%1 x%2", _itemName, _quantity];
		
		_playerList lbAdd _entry;
		_playerList lbSetData [_forEachIndex, _item];
		_playerList lbSetValue [_forEachIndex, _quantity];
	} forEach EDRP_player_inventory;
};

// Store item in house
EDRP_fnc_storeItemInHouse = {
	params [
		["_item", "", [""]],
		["_quantity", 1, [0]],
		["_storageType", "virtual", [""]]
	];
	
	if (_item == "" || _quantity <= 0) exitWith {
		["Invalid item or quantity"] call EDRP_fnc_hint;
		false
	};
	
	if (isNil "EDRP_current_storage_house") exitWith {
		["No house storage open"] call EDRP_fnc_hint;
		false
	};
	
	private _house = EDRP_current_storage_house;
	
	// Check if player has the item
	private _playerQuantity = [_item] call EDRP_fnc_getItemQuantity;
	if (_playerQuantity < _quantity) exitWith {
		["You don't have enough of this item"] call EDRP_fnc_hint;
		false
	};
	
	// Get storage data
	private _storageVar = if (_storageType == "virtual") then { "virtual_inventory" } else { "physical_inventory" };
	private _capacityVar = if (_storageType == "virtual") then { "virtual_storage_capacity" } else { "physical_storage_capacity" };
	
	private _inventory = _house getVariable [_storageVar, [[], 0]];
	private _capacity = _house getVariable [_capacityVar, 100];
	
	// Calculate item weight/space
	private _itemWeight = [_item] call EDRP_fnc_getItemWeight;
	private _totalWeight = _itemWeight * _quantity;
	
	// Check capacity
	if ((_inventory select 1) + _totalWeight > _capacity) exitWith {
		["Not enough storage space"] call EDRP_fnc_hint;
		false
	};
	
	// Remove from player
	if !([_item, _quantity] call EDRP_fnc_removeItem) exitWith {
		["Failed to remove item from inventory"] call EDRP_fnc_hint;
		false
	};
	
	// Add to house storage
	private _items = _inventory select 0;
	private _currentWeight = _inventory select 1;
	
	// Check if item already exists in storage
	private _itemIndex = -1;
	{
		if ((_x select 0) == _item) exitWith {
			_itemIndex = _forEachIndex;
		};
	} forEach _items;
	
	if (_itemIndex >= 0) then {
		// Update existing item
		private _existingItem = _items select _itemIndex;
		_existingItem set [1, (_existingItem select 1) + _quantity];
		_items set [_itemIndex, _existingItem];
	} else {
		// Add new item
		_items pushBack [_item, _quantity];
	};
	
	// Update storage
	_inventory set [0, _items];
	_inventory set [1, _currentWeight + _totalWeight];
	_house setVariable [_storageVar, _inventory, true];
	
	// Send update to server
	[_house, _storageType, _inventory] remoteExec ["EDRP_fnc_updateHouseStorage", 2];
	
	// Update display
	[] call EDRP_fnc_updateHouseInventory;
	
	[format ["Stored %1 x%2 in house %3 storage", [_item] call EDRP_fnc_getItemName, _quantity, _storageType], "success"] call EDRP_fnc_hint;
	
	true
};

// Take item from house
EDRP_fnc_takeItemFromHouse = {
	params [
		["_item", "", [""]],
		["_quantity", 1, [0]],
		["_storageType", "virtual", [""]]
	];
	
	if (_item == "" || _quantity <= 0) exitWith {
		["Invalid item or quantity"] call EDRP_fnc_hint;
		false
	};
	
	if (isNil "EDRP_current_storage_house") exitWith {
		["No house storage open"] call EDRP_fnc_hint;
		false
	};
	
	private _house = EDRP_current_storage_house;
	
	// Get storage data
	private _storageVar = if (_storageType == "virtual") then { "virtual_inventory" } else { "physical_inventory" };
	private _inventory = _house getVariable [_storageVar, [[], 0]];
	private _items = _inventory select 0;
	
	// Find item in storage
	private _itemIndex = -1;
	private _availableQuantity = 0;
	{
		if ((_x select 0) == _item) exitWith {
			_itemIndex = _forEachIndex;
			_availableQuantity = _x select 1;
		};
	} forEach _items;
	
	if (_itemIndex < 0) exitWith {
		["Item not found in storage"] call EDRP_fnc_hint;
		false
	};
	
	if (_availableQuantity < _quantity) exitWith {
		["Not enough of this item in storage"] call EDRP_fnc_hint;
		false
	};
	
	// Check player inventory space
	if !([_item, _quantity] call EDRP_fnc_canAddItem) exitWith {
		["Not enough inventory space"] call EDRP_fnc_hint;
		false
	};
	
	// Add to player inventory
	if !([_item, _quantity] call EDRP_fnc_addItem) exitWith {
		["Failed to add item to inventory"] call EDRP_fnc_hint;
		false
	};
	
	// Remove from house storage
	private _itemWeight = [_item] call EDRP_fnc_getItemWeight;
	private _totalWeight = _itemWeight * _quantity;
	
	if (_availableQuantity == _quantity) then {
		// Remove item completely
		_items deleteAt _itemIndex;
	} else {
		// Reduce quantity
		private _storageItem = _items select _itemIndex;
		_storageItem set [1, _availableQuantity - _quantity];
		_items set [_itemIndex, _storageItem];
	};
	
	// Update storage
	_inventory set [0, _items];
	_inventory set [1, (_inventory select 1) - _totalWeight];
	_house setVariable [_storageVar, _inventory, true];
	
	// Send update to server
	[_house, _storageType, _inventory] remoteExec ["EDRP_fnc_updateHouseStorage", 2];
	
	// Update display
	[] call EDRP_fnc_updateHouseInventory;
	
	[format ["Took %1 x%2 from house %3 storage", [_item] call EDRP_fnc_getItemName, _quantity, _storageType], "success"] call EDRP_fnc_hint;
	
	true
};

// Add house storage actions
EDRP_fnc_addHouseStorageActions = {
	// Open storage action
	player addAction [
		"<t color='#00FFFF'>Open Storage</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_openHouseInventory;
		},
		[],
		3,
		true,
		true,
		"",
		"cursorTarget isKindOf 'House_F' && cursorTarget distance player < 10 && [cursorTarget] call EDRP_fnc_hasHouseAccess && !(cursorTarget getVariable ['locked', true])"
	];
};

// Initialize house storage system
if (hasInterface) then {
	// Add storage actions
	[] call EDRP_fnc_addHouseStorageActions;
	
	// Initialize storage variables
	EDRP_current_storage_house = objNull;
};
