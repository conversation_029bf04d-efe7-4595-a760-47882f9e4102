# EdenRP Altis Life Deployment Guide

## Pre-Deployment Checklist

### 1. System Requirements Verification
- [ ] Server hardware meets minimum requirements
- [ ] Operating system is up to date
- [ ] Required software is installed (MySQL, Arma 3 Server)
- [ ] Network configuration is correct
- [ ] Firewall rules are configured

### 2. File Preparation
- [ ] All mission files are present and correct
- [ ] Database schema is ready for import
- [ ] Configuration files are customized
- [ ] Backup procedures are in place
- [ ] Admin accounts are prepared

### 3. Security Setup
- [ ] Strong passwords are configured
- [ ] Admin UIDs are added to security system
- [ ] Anti-cheat system is configured
- [ ] Database security is implemented
- [ ] Server access is restricted

## Deployment Steps

### Phase 1: Infrastructure Setup

#### 1.1 Server Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y mysql-server wget curl unzip screen

# Create arma3 user
sudo useradd -m -s /bin/bash arma3
sudo usermod -aG sudo arma3
```

#### 1.2 Arma 3 Server Installation
```bash
# Switch to arma3 user
sudo su - arma3

# Create directories
mkdir -p ~/arma3server
cd ~/arma3server

# Download SteamCMD
wget https://steamcdn-a.akamaihd.net/client/installer/steamcmd_linux.tar.gz
tar -xvzf steamcmd_linux.tar.gz

# Install Arma 3 Server
./steamcmd.sh +login anonymous +force_install_dir ~/arma3server +app_update 233780 validate +quit
```

#### 1.3 Database Setup
```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p << EOF
CREATE DATABASE edenrp_altis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'edenrp_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON edenrp_altis.* TO 'edenrp_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
EOF
```

### Phase 2: Mission Deployment

#### 2.1 File Transfer
```bash
# Create mission directory
mkdir -p ~/arma3server/mpmissions

# Upload mission files (use SCP, SFTP, or similar)
scp -r EdenRP.Altis/ user@server:~/arma3server/mpmissions/

# Set correct permissions
chmod -R 755 ~/arma3server/mpmissions/EdenRP.Altis/
```

#### 2.2 Database Schema Import
```bash
# Import database schema
mysql -u edenrp_user -p edenrp_altis < ~/arma3server/database/edenrp_schema.sql

# Verify tables were created
mysql -u edenrp_user -p -e "USE edenrp_altis; SHOW TABLES;"
```

#### 2.3 extDB3 Configuration
```bash
# Download and install extDB3
cd ~/arma3server
wget https://github.com/AsYetUntitled/extDB3/releases/latest/download/extDB3.zip
unzip extDB3.zip

# Configure extDB3
cat > extdb3-conf.ini << EOF
[Database]
Type = MySQL
Name = edenrp_altis
Username = edenrp_user
Password = your_secure_password
IP = 127.0.0.1
Port = 3306
EOF
```

### Phase 3: Server Configuration

#### 3.1 Server Configuration Files
```bash
# Create server.cfg
cat > ~/arma3server/server.cfg << 'EOF'
hostname = "EdenRP Altis Life | Roleplay Server | 120 Slots";
password = "";
passwordAdmin = "your_admin_password";
maxPlayers = 120;
persistent = 1;
disableVoN = 0;
vonCodecQuality = 10;
kickDuplicate = 1;
verifySignatures = 2;
allowedFilePatching = 1;
requiredSecureId = 2;

MinBandwidth = 131072;
MaxBandwidth = 10000000000;
MaxMsgSend = 256;
MaxSizeGuaranteed = 512;
MaxSizeNonguaranteed = 256;
MinErrorToSend = 0.001;
MinErrorToSendNear = 0.01;
MaxCustomFileSize = 0;

class Missions {
    class EdenRP {
        template = "EdenRP.Altis";
        difficulty = "Custom";
    };
};
EOF

# Create basic.cfg
cat > ~/arma3server/basic.cfg << 'EOF'
MaxMsgSend = 128;
MaxSizeGuaranteed = 512;
MaxSizeNonguaranteed = 256;
MinBandwidth = 131072;
MaxBandwidth = 10000000000;
MinErrorToSend = 0.001;
MinErrorToSendNear = 0.01;
MaxCustomFileSize = 0;
class sockets { maxPacketSize = 1400; };
adapter = -1;
3D_Performance = 1;
Resolution_W = 0;
Resolution_H = 0;
Resolution_Bpp = 32;
terrainGrid = 25;
viewDistance = 1600;
preferredObjectViewDistance = 800;
EOF
```

#### 3.2 Startup Script
```bash
# Create startup script
cat > ~/arma3server/start_server.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/arma3server

# Kill existing server processes
pkill -f arma3server

# Wait for processes to terminate
sleep 5

# Start server in screen session
screen -dmS arma3server ./arma3server_x64 \
-port=2302 \
-config=server.cfg \
-cfg=basic.cfg \
-profiles=ServerProfile \
-name=server \
-filePatching \
-mod=@extDB3 \
-servermod=@extDB3 \
-world=Altis \
-autoinit

echo "EdenRP Server started in screen session 'arma3server'"
echo "Use 'screen -r arma3server' to attach to the session"
EOF

# Make script executable
chmod +x ~/arma3server/start_server.sh
```

### Phase 4: Testing and Validation

#### 4.1 Initial Server Test
```bash
# Start the server
~/arma3server/start_server.sh

# Check if server is running
ps aux | grep arma3server

# Check server logs
tail -f ~/arma3server/ServerProfile/server.log
```

#### 4.2 Database Connection Test
```bash
# Test database connection
mysql -u edenrp_user -p -e "USE edenrp_altis; SELECT COUNT(*) FROM players;"

# Check extDB3 logs
tail -f ~/arma3server/logs/extdb3.log
```

#### 4.3 Network Connectivity Test
```bash
# Test server ports
netstat -tulpn | grep :2302

# Test external connectivity (from another machine)
telnet your_server_ip 2302
```

### Phase 5: Production Deployment

#### 5.1 Service Configuration
```bash
# Create systemd service
sudo cat > /etc/systemd/system/edenrp.service << 'EOF'
[Unit]
Description=EdenRP Altis Life Server
After=network.target mysql.service

[Service]
Type=forking
User=arma3
Group=arma3
WorkingDirectory=/home/<USER>/arma3server
ExecStart=/home/<USER>/arma3server/start_server.sh
ExecStop=/usr/bin/pkill -f arma3server
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable edenrp.service
sudo systemctl start edenrp.service
```

#### 5.2 Monitoring Setup
```bash
# Create monitoring script
cat > ~/arma3server/monitor.sh << 'EOF'
#!/bin/bash
LOG_FILE="/home/<USER>/arma3server/logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check if server is running
if ! pgrep -f "arma3server" > /dev/null; then
    echo "[$DATE] Server not running, attempting restart..." >> $LOG_FILE
    systemctl restart edenrp.service
    sleep 30
fi

# Check database connection
if ! mysql -u edenrp_user -p'your_secure_password' -e "USE edenrp_altis; SELECT 1;" > /dev/null 2>&1; then
    echo "[$DATE] Database connection failed!" >> $LOG_FILE
fi

# Check disk space
DISK_USAGE=$(df /home/<USER>'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "[$DATE] Disk usage high: ${DISK_USAGE}%" >> $LOG_FILE
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{printf "%.2f", $3*100/$2}')
if (( $(echo "$MEM_USAGE > 90" | bc -l) )); then
    echo "[$DATE] Memory usage high: ${MEM_USAGE}%" >> $LOG_FILE
fi
EOF

# Make script executable
chmod +x ~/arma3server/monitor.sh

# Add to crontab (run every 5 minutes)
(crontab -l 2>/dev/null; echo "*/5 * * * * /home/<USER>/arma3server/monitor.sh") | crontab -
```

#### 5.3 Backup Configuration
```bash
# Create backup script
cat > ~/arma3server/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date '+%Y%m%d_%H%M%S')

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u edenrp_user -p'your_secure_password' edenrp_altis > $BACKUP_DIR/database_$DATE.sql

# Mission files backup
tar -czf $BACKUP_DIR/mission_$DATE.tar.gz -C /home/<USER>/arma3server/mpmissions EdenRP.Altis

# Configuration backup
tar -czf $BACKUP_DIR/config_$DATE.tar.gz -C /home/<USER>/arma3server server.cfg basic.cfg extdb3-conf.ini

# Clean old backups (keep last 7 days)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

# Make script executable
chmod +x ~/arma3server/backup.sh

# Add to crontab (daily at 3 AM)
(crontab -l 2>/dev/null; echo "0 3 * * * /home/<USER>/arma3server/backup.sh") | crontab -
```

## Post-Deployment Tasks

### 1. Admin Setup
```sql
-- Add initial admin accounts
INSERT INTO admins (uid, name, level) VALUES 
('your_steam64_id', 'YourName', 5),
('admin2_steam64_id', 'Admin2Name', 4);
```

### 2. Performance Tuning
- Monitor server performance for first 24 hours
- Adjust view distances based on player count
- Optimize database queries if needed
- Fine-tune cleanup intervals

### 3. Community Setup
- Configure Discord bot integration
- Set up community forums
- Create player guides and rules
- Establish admin procedures

## Maintenance Procedures

### Daily Tasks
- Check server status and logs
- Monitor player count and performance
- Review admin logs for issues
- Backup critical data

### Weekly Tasks
- Update server software if needed
- Clean up old log files
- Review and optimize database
- Test backup restoration procedures

### Monthly Tasks
- Full system security audit
- Performance optimization review
- Community feedback analysis
- Hardware utilization assessment

## Troubleshooting Guide

### Server Won't Start
1. Check configuration file syntax
2. Verify file permissions
3. Check available disk space
4. Review system logs

### Database Connection Issues
1. Verify MySQL service status
2. Check credentials and permissions
3. Test network connectivity
4. Review extDB3 logs

### Performance Problems
1. Monitor CPU and memory usage
2. Check database query performance
3. Review player count vs. hardware
4. Optimize server settings

### Player Connection Issues
1. Verify firewall settings
2. Check server capacity
3. Review mission file integrity
4. Test from different networks

## Security Hardening

### Server Security
- Regular security updates
- Firewall configuration
- SSH key authentication
- Fail2ban installation

### Application Security
- Strong admin passwords
- Regular permission audits
- Anti-cheat monitoring
- Suspicious activity alerts

### Database Security
- Encrypted connections
- Regular password changes
- Access logging
- Backup encryption

---

**Deployment Complete**

Your EdenRP Altis Life server should now be fully deployed and operational. Monitor the server closely for the first few days and make adjustments as needed based on player feedback and performance metrics.

For ongoing support and updates, maintain regular communication with the development team and community.
