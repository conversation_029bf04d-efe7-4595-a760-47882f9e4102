/*
	EdenRP Altis Life - System Initialization
	Author: EdenRP Development Team
	Description: Core client system initialization and setup
	Version: 1.0.0
*/

// Prevent multiple initialization
if (!isNil "EDRP_system_initialized") exitWith {};

diag_log "EdenRP: Starting client system initialization...";

// Core system variables
EDRP_system_initialized = false;
EDRP_client_ready = false;
EDRP_player_data_loaded = false;
EDRP_session_completed = false;
EDRP_loading_progress = 0;
EDRP_loading_status = "Initializing EdenRP Client...";

// Player state variables
EDRP_player_cash = 0;
EDRP_player_bank = 5000;
EDRP_player_level = 1;
EDRP_player_xp = 0;
EDRP_player_playtime = 0;

// Player status variables
EDRP_player_wanted = false;
EDRP_player_bounty = 0;
EDRP_player_arrested = false;
EDRP_player_restrained = false;
EDRP_player_escorted = false;
EDRP_player_unconscious = false;
EDRP_player_bleeding = false;

// Player needs variables
EDRP_player_hunger = 100;
EDRP_player_thirst = 100;
EDRP_player_fatigue = 0;
EDRP_player_health = 100;

// Player inventory variables
EDRP_player_inventory = [];
EDRP_player_licenses = [];
EDRP_player_gear = [];

// Player property variables
EDRP_player_vehicles = [];
EDRP_player_houses = [];
EDRP_player_keys = [];

// Player gang variables
EDRP_player_gang = "";
EDRP_player_gang_rank = 0;

// Player job variables
EDRP_player_job = "civilian";
EDRP_player_rank = 1;

// Player communication variables
EDRP_player_phone = "";
EDRP_player_radio = 0;

// UI state variables
EDRP_ui_inventory_open = false;
EDRP_ui_phone_open = false;
EDRP_ui_map_open = false;
EDRP_ui_menu_open = false;

// Interaction variables
EDRP_interaction_target = objNull;
EDRP_interaction_distance = 5;
EDRP_interaction_cooldown = false;

// Vehicle variables
EDRP_current_vehicle = objNull;
EDRP_vehicle_locked = false;
EDRP_vehicle_engine = false;

// Security variables
EDRP_script_cooldowns = createHashMap;
EDRP_last_action_time = 0;
EDRP_action_spam_threshold = 0.5;

// Performance variables
EDRP_fps_monitor = true;
EDRP_fps_threshold = 30;
EDRP_memory_monitor = true;

// Network variables
EDRP_network_timeout = 30;
EDRP_sync_interval = 300; // 5 minutes
EDRP_last_sync = 0;

// Event handler variables
EDRP_event_handlers = [];
EDRP_key_handlers = [];
EDRP_display_handlers = [];

// Animation variables
EDRP_current_animation = "";
EDRP_animation_interrupt = false;

// Sound variables
EDRP_sound_enabled = true;
EDRP_sound_volume = 1.0;
EDRP_music_enabled = true;
EDRP_music_volume = 0.5;

// Graphics variables
EDRP_graphics_quality = "auto";
EDRP_view_distance = 2000;
EDRP_object_distance = 1500;

// Faction-specific variables
switch (playerSide) do {
	case west: { // Police
		EDRP_police_rank = 1;
		EDRP_police_department = "patrol";
		EDRP_police_badge = 0;
		EDRP_police_callsign = "";
		EDRP_police_on_duty = false;
		EDRP_police_gear_loaded = false;
	};
	
	case independent: { // Medical
		EDRP_medical_rank = 1;
		EDRP_medical_certification = "emt";
		EDRP_medical_hospital = "kavala";
		EDRP_medical_on_duty = false;
		EDRP_medical_gear_loaded = false;
	};
	
	case civilian: { // Civilian
		EDRP_civilian_occupation = "unemployed";
		EDRP_civilian_skill_levels = createHashMap;
		EDRP_civilian_job_progress = createHashMap;
	};
};

// Initialize core functions
EDRP_fnc_hint = {
	params ["_message", ["_duration", 5], ["_type", "info"]];
	
	private _color = switch (_type) do {
		case "error": {"#FF0000"};
		case "warning": {"#FFA500"};
		case "success": {"#00FF00"};
		default {"#FFFFFF"};
	};
	
	hint parseText format ["<t color='%1'>%2</t>", _color, _message];
	
	if (_duration > 0) then {
		[{hintSilent ""}, [], _duration] call CBA_fnc_waitAndExecute;
	};
};

EDRP_fnc_notification = {
	params ["_title", "_message", ["_type", "info"], ["_duration", 5]];
	
	private _icon = switch (_type) do {
		case "error": {"\a3\ui_f\data\IGUI\Cfg\Actions\ico_error_ca.paa"};
		case "warning": {"\a3\ui_f\data\IGUI\Cfg\Actions\ico_warning_ca.paa"};
		case "success": {"\a3\ui_f\data\IGUI\Cfg\Actions\ico_check_ca.paa"};
		default {"\a3\ui_f\data\IGUI\Cfg\Actions\ico_info_ca.paa"};
	};
	
	[_title, _message, _icon] call BIS_fnc_showNotification;
};

EDRP_fnc_progressBar = {
	params ["_title", "_duration", ["_condition", {true}], ["_onComplete", {}], ["_onFail", {}]];
	
	private _startTime = time;
	private _endTime = _startTime + _duration;
	
	disableUserInput true;
	
	[{
		params ["_args", "_handle"];
		_args params ["_title", "_endTime", "_condition", "_onComplete", "_onFail"];
		
		private _progress = (time - (_endTime - _duration)) / _duration;
		
		if (time >= _endTime) then {
			[_handle] call CBA_fnc_removePerFrameHandler;
			disableUserInput false;
			call _onComplete;
		} else {
			if (!(call _condition)) then {
				[_handle] call CBA_fnc_removePerFrameHandler;
				disableUserInput false;
				call _onFail;
			} else {
				hintSilent parseText format [
					"<t align='center' size='1.2'>%1</t><br/><t align='center' size='0.8'>Progress: %2%%</t>",
					_title,
					round (_progress * 100)
				];
			};
		};
	}, 0.1, [_title, _endTime, _condition, _onComplete, _onFail]] call CBA_fnc_addPerFrameHandler;
};

EDRP_fnc_cooldownCheck = {
	params ["_action", ["_cooldown", 1]];
	
	private _lastTime = EDRP_script_cooldowns getOrDefault [_action, 0];
	private _currentTime = time;
	
	if (_currentTime - _lastTime < _cooldown) then {
		false
	} else {
		EDRP_script_cooldowns set [_action, _currentTime];
		true
	};
};

EDRP_fnc_validatePlayer = {
	if (isNull player) exitWith {false};
	if (getPlayerUID player isEqualTo "") exitWith {false};
	if (!alive player) exitWith {false};
	true
};

EDRP_fnc_getPlayerData = {
	params ["_key", ["_default", nil]];
	
	private _value = player getVariable [format ["EDRP_%1", _key], _default];
	_value
};

EDRP_fnc_setPlayerData = {
	params ["_key", "_value", ["_global", true]];
	
	player setVariable [format ["EDRP_%1", _key], _value, _global];
};

// Initialize security system
EDRP_fnc_securityCheck = {
	// Basic anti-cheat checks
	if (isNil "player") exitWith {false};
	if (getPlayerUID player isEqualTo "") exitWith {false};
	
	// Check for suspicious variables
	private _suspiciousVars = [
		"life_adminlevel", "life_coplevel", "life_donator",
		"oev_donator", "oev_adminlevel", "olympus_admin"
	];
	
	{
		if (!isNil {missionNamespace getVariable _x}) then {
			diag_log format ["EdenRP Security: Suspicious variable detected: %1", _x];
			[getPlayerUID player, "suspicious_variable", _x] remoteExec ["EDRP_fnc_securityLog", 2];
		};
	} forEach _suspiciousVars;
	
	true
};

// Initialize error handling
EDRP_fnc_errorHandler = {
	params ["_error", "_function", "_params"];
	
	diag_log format ["EdenRP Error in %1: %2", _function, _error];
	
	// Send error to server for logging
	[getPlayerUID player, _function, _error, _params] remoteExec ["EDRP_fnc_errorLog", 2];
};

// Mark system as initialized
EDRP_system_initialized = true;

diag_log "EdenRP: Client system initialization complete";
