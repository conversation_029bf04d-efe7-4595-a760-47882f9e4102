//  File: fn_pumpRepair.sqf
//	Description: Quick simple action that is only temp.
private["_method"];
if(isNil "eden_cash") then {eden_cash = 0; eden_cache_cash = eden_random_cash_val;};
if(isNil "eden_atmcash") then {eden_atmcash = 0; eden_cache_atmcash = eden_random_cash_val;};

if((eden_cash + (eden_random_cash_val - 5000)) > eden_cache_cash || (eden_atmcash + (eden_random_cash_val - 5000)) > eden_cache_atmcash) exitWith {
	[
		["event","Hacked Cash"],
		["player",name player],
		["player_id",getPlayerUID player],
		["hackedcash",eden_cash - (eden_cache_cash - eden_random_cash_val)],
		["hackedbank",eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)],
		["location",getPosATL player]
	] call EDEN_fnc_logIt;
	[[profileName,format["Hacked Cash Detected! (Cash Hacked In = %1) (Bank Hacked In = %2)",eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDEN_fnc_notifyAdmins",-2,false] spawn EDEN_fnc_MP;
	[[1,player,[eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDENS_fnc_handleDisc",false,false] spawn EDEN_fnc_MP;
	["HackedMoney",false,false] call compile PreProcessFileLineNumbers "\a3\functions_f\Misc\fn_endMission.sqf";
};


if(eden_cash < 500) then {
	if(eden_atmcash < 500) exitWith {_method = 0;};
	_method = 2;
} else {
	_method = 1;
};

private _dam_obj = vehicle player;
switch (_method) do {
	case 0: {hint "You do not have $500 in cash or in your bank accoumt."};
	case 1: {_dam_obj setDamage 0; eden_cash = eden_cash - 500; eden_cache_cash = eden_cache_cash - 500; hint "You have repaired your vehicle for $500";};
	case 2: {_dam_obj setDamage 0; eden_atmcash = eden_atmcash - 500; eden_cache_atmcash = eden_cache_atmcash - 500; hint "You have repaired your vehicle for $500";};
};
