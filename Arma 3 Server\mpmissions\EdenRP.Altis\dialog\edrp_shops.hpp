/*
	EdenRP Altis Life - Shop System
	Author: EdenRP Development Team
	Description: Shop interfaces for buying and selling items
	Version: 1.0.0
*/

class EDRP_ShopMenu: EDRP_MenuBase {
	idd = 4000;
	onLoad = "[] spawn EDRP_fnc_updateShop;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "EdenRP - Shop";
		};
		class Header: BaseHeader {
			text = "Buy and Sell Items";
		};
		
		// Shop sections
		class BuyListBackground: EDRP_RscText {
			idc = -1;
			x = 0.21;
			y = 0.26;
			w = 0.28;
			h = 0.45;
			colorBackground[] = {0.05, 0.05, 0.05, 0.9};
		};
		
		class SellListBackground: EDRP_RscText {
			idc = -1;
			x = 0.51;
			y = 0.26;
			w = 0.28;
			h = 0.45;
			colorBackground[] = {0.05, 0.05, 0.05, 0.9};
		};
		
		// Money display
		class MoneyBackground: EDRP_RscText {
			idc = -1;
			x = 0.21;
			y = 0.72;
			w = 0.58;
			h = 0.04;
			colorBackground[] = {0.1, 0.1, 0.1, 0.8};
		};
	};
	
	class controls: controlsBase {
		// Buy section
		class BuyTitle: EDRP_RscText {
			idc = -1;
			text = "Buy Items";
			x = 0.21;
			y = 0.24;
			w = 0.28;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			sizeEx = 0.03;
			font = "RobotoCondensedBold";
		};
		
		class BuyList: EDRP_RscListBox {
			idc = 4001;
			x = 0.22;
			y = 0.27;
			w = 0.26;
			h = 0.38;
			sizeEx = 0.03;
			onLBSelChanged = "[4001, 'buy'] call EDRP_fnc_shopSelectionChanged;";
			onLBDblClick = "[4001, 'buy'] call EDRP_fnc_shopDoubleClick;";
		};
		
		class BuyQuantityEdit: EDRP_RscEdit {
			idc = 4002;
			text = "1";
			x = 0.22;
			y = 0.66;
			w = 0.08;
			h = 0.03;
		};
		
		class BuyButton: EDRP_RscButtonMenu {
			idc = 4003;
			text = "Buy";
			x = 0.31;
			y = 0.66;
			w = 0.08;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_buyItem;";
		};
		
		class BuyMaxButton: EDRP_RscButtonMenu {
			idc = 4004;
			text = "Max";
			x = 0.40;
			y = 0.66;
			w = 0.08;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_buyMaxItems;";
		};
		
		// Sell section
		class SellTitle: EDRP_RscText {
			idc = -1;
			text = "Sell Items";
			x = 0.51;
			y = 0.24;
			w = 0.28;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_WARNING;
			sizeEx = 0.03;
			font = "RobotoCondensedBold";
		};
		
		class SellList: EDRP_RscListBox {
			idc = 4005;
			x = 0.52;
			y = 0.27;
			w = 0.26;
			h = 0.38;
			sizeEx = 0.03;
			onLBSelChanged = "[4005, 'sell'] call EDRP_fnc_shopSelectionChanged;";
			onLBDblClick = "[4005, 'sell'] call EDRP_fnc_shopDoubleClick;";
		};
		
		class SellQuantityEdit: EDRP_RscEdit {
			idc = 4006;
			text = "1";
			x = 0.52;
			y = 0.66;
			w = 0.08;
			h = 0.03;
		};
		
		class SellButton: EDRP_RscButtonMenu {
			idc = 4007;
			text = "Sell";
			x = 0.61;
			y = 0.66;
			w = 0.08;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_sellItem;";
		};
		
		class SellAllButton: EDRP_RscButtonMenu {
			idc = 4008;
			text = "Sell All";
			x = 0.70;
			y = 0.66;
			w = 0.08;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "[] call EDRP_fnc_sellAllItems;";
		};
		
		// Money display
		class MoneyIcon: EDRP_RscPicture {
			idc = -1;
			text = "images\icons\money.paa";
			x = 0.22;
			y = 0.725;
			w = 0.025;
			h = 0.025;
		};
		
		class MoneyText: EDRP_RscStructuredText {
			idc = 4010;
			text = "Cash: $0 | Bank: $0";
			x = 0.25;
			y = 0.72;
			w = 0.3;
			h = 0.04;
			size = 0.03;
		};
		
		// Item information
		class ItemInfoText: EDRP_RscStructuredText {
			idc = 4011;
			text = "Select an item to view details";
			x = 0.55;
			y = 0.72;
			w = 0.24;
			h = 0.04;
			size = 0.025;
		};
		
		// Transaction history
		class TransactionButton: EDRP_RscButtonMenu {
			idc = 4012;
			text = "History";
			x = 0.21;
			y = 0.77;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			onButtonClick = "[] call EDRP_fnc_showTransactionHistory;";
		};
		
		// Filter and search
		class FilterCombo: EDRP_RscCombo {
			idc = 4013;
			x = 0.30;
			y = 0.77;
			w = 0.15;
			h = 0.04;
		};
		
		class SearchEdit: EDRP_RscEdit {
			idc = 4014;
			text = "Search...";
			x = 0.46;
			y = 0.77;
			w = 0.15;
			h = 0.04;
		};
		
		class SearchButton: EDRP_RscButtonMenu {
			idc = 4015;
			text = "Search";
			x = 0.62;
			y = 0.77;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_searchShopItems;";
		};
		
		// Close button
		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 4000; [] call EDRP_fnc_closeShop;";
		};
	};
};

// Vehicle shop dialog
class EDRP_VehicleShopMenu: EDRP_MenuBase {
	idd = 4100;
	onLoad = "[] spawn EDRP_fnc_updateVehicleShop;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "EdenRP - Vehicle Shop";
		};
		class Header: BaseHeader {
			text = "Purchase Vehicles";
		};
		
		class VehicleListBackground: BaseSidePanelBackground {};
		class VehicleInfoBackground: BaseMainContentBackground {};
		
		class PreviewBackground: EDRP_RscText {
			idc = -1;
			x = 0.41;
			y = 0.28;
			w = 0.37;
			h = 0.25;
			colorBackground[] = {0.02, 0.02, 0.02, 0.9};
		};
	};
	
	class controls: controlsBase {
		// Vehicle categories
		class CategoryTitle: EDRP_RscText {
			idc = -1;
			text = "Categories";
			x = 0.22;
			y = 0.26;
			w = 0.16;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			sizeEx = 0.03;
		};
		
		class CategoryList: EDRP_RscListBox {
			idc = 4101;
			x = 0.22;
			y = 0.28;
			w = 0.16;
			h = 0.2;
			sizeEx = 0.03;
			onLBSelChanged = "[] call EDRP_fnc_vehicleCategoryChanged;";
		};
		
		// Vehicle list
		class VehicleTitle: EDRP_RscText {
			idc = -1;
			text = "Vehicles";
			x = 0.22;
			y = 0.49;
			w = 0.16;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			sizeEx = 0.03;
		};
		
		class VehicleList: EDRP_RscListBox {
			idc = 4102;
			x = 0.22;
			y = 0.51;
			w = 0.16;
			h = 0.27;
			sizeEx = 0.025;
			onLBSelChanged = "[] call EDRP_fnc_vehicleSelectionChanged;";
		};
		
		// Vehicle preview
		class VehiclePreview: EDRP_RscPicture {
			idc = 4103;
			x = 0.42;
			y = 0.29;
			w = 0.35;
			h = 0.23;
		};
		
		// Vehicle information
		class VehicleNameText: EDRP_RscText {
			idc = 4104;
			text = "";
			x = 0.42;
			y = 0.54;
			w = 0.35;
			h = 0.03;
			style = 2;
			sizeEx = 0.04;
			font = "RobotoCondensedBold";
		};
		
		class VehicleStatsText: EDRP_RscStructuredText {
			idc = 4105;
			text = "";
			x = 0.42;
			y = 0.58;
			w = 0.35;
			h = 0.15;
			size = 0.03;
		};
		
		class VehiclePriceText: EDRP_RscText {
			idc = 4106;
			text = "";
			x = 0.42;
			y = 0.74;
			w = 0.2;
			h = 0.04;
			sizeEx = 0.035;
			font = "RobotoCondensedBold";
			colorText[] = EDRP_COLOR_SUCCESS;
		};
		
		// Purchase controls
		class ColorCombo: EDRP_RscCombo {
			idc = 4107;
			x = 0.63;
			y = 0.74;
			w = 0.14;
			h = 0.04;
		};
		
		class ColorLabel: EDRP_RscText {
			idc = -1;
			text = "Color:";
			x = 0.63;
			y = 0.72;
			w = 0.14;
			h = 0.02;
			sizeEx = 0.025;
		};
		
		class PurchaseButton: EDRP_RscButtonMenu {
			idc = 4108;
			text = "Purchase";
			x = 0.42;
			y = 0.79;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_purchaseVehicle;";
		};
		
		class PreviewButton: EDRP_RscButtonMenu {
			idc = 4109;
			text = "Preview";
			x = 0.55;
			y = 0.79;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_previewVehicle;";
		};
		
		class TestDriveButton: EDRP_RscButtonMenu {
			idc = 4110;
			text = "Test Drive";
			x = 0.66;
			y = 0.79;
			w = 0.11;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_testDriveVehicle;";
		};
		
		// Close button
		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 4100; [] call EDRP_fnc_closeVehicleShop;";
		};
	};
};

// Clothing shop dialog
class EDRP_ClothingShopMenu: EDRP_MenuBase {
	idd = 4200;
	onLoad = "[] spawn EDRP_fnc_updateClothingShop;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "EdenRP - Clothing Store";
		};
		class Header: BaseHeader {
			text = "Customize Your Appearance";
		};
		
		class ClothingListBackground: BaseSidePanelBackground {};
		class PreviewBackground: BaseMainContentBackground {};
	};
	
	class controls: controlsBase {
		// Clothing categories
		class CategoryList: EDRP_RscListBox {
			idc = 4201;
			x = 0.22;
			y = 0.28;
			w = 0.16;
			h = 0.25;
			sizeEx = 0.03;
			onLBSelChanged = "[] call EDRP_fnc_clothingCategoryChanged;";
		};
		
		// Clothing items
		class ClothingList: EDRP_RscListBox {
			idc = 4202;
			x = 0.22;
			y = 0.54;
			w = 0.16;
			h = 0.24;
			sizeEx = 0.025;
			onLBSelChanged = "[] call EDRP_fnc_clothingSelectionChanged;";
		};
		
		// Player preview (would show 3D model in actual implementation)
		class PlayerPreview: EDRP_RscText {
			idc = 4203;
			text = "Player Preview";
			x = 0.41;
			y = 0.28;
			w = 0.37;
			h = 0.4;
			style = 2;
			colorBackground[] = {0.02, 0.02, 0.02, 0.9};
		};
		
		// Clothing information
		class ClothingNameText: EDRP_RscText {
			idc = 4204;
			text = "";
			x = 0.41;
			y = 0.69;
			w = 0.25;
			h = 0.03;
			sizeEx = 0.035;
			font = "RobotoCondensedBold";
		};
		
		class ClothingPriceText: EDRP_RscText {
			idc = 4205;
			text = "";
			x = 0.67;
			y = 0.69;
			w = 0.11;
			h = 0.03;
			sizeEx = 0.035;
			colorText[] = EDRP_COLOR_SUCCESS;
		};
		
		// Purchase controls
		class TryOnButton: EDRP_RscButtonMenu {
			idc = 4206;
			text = "Try On";
			x = 0.41;
			y = 0.73;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_tryOnClothing;";
		};
		
		class PurchaseButton: EDRP_RscButtonMenu {
			idc = 4207;
			text = "Purchase";
			x = 0.52;
			y = 0.73;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_purchaseClothing;";
		};
		
		class RemoveButton: EDRP_RscButtonMenu {
			idc = 4208;
			text = "Remove";
			x = 0.63;
			y = 0.73;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_removeClothing;";
		};
		
		// Close button
		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 4200; [] call EDRP_fnc_closeClothingShop;";
		};
	};
};

// Transaction history dialog
class EDRP_TransactionHistoryDialog: EDRP_MenuBase {
	idd = 4300;
	onLoad = "[] spawn EDRP_fnc_updateTransactionHistory;";

	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Transaction History";
		};
		class Header: BaseHeader {
			text = "Recent Purchases and Sales";
		};
		class ContentBackground: BaseContentBackground {};
	};

	class controls: controlsBase {
		class TransactionList: BaseListBox {
			idc = 4301;
			x = 0.22;
			y = 0.28;
			w = 0.56;
			h = 0.45;
			sizeEx = 0.03;
		};

		class FilterCombo: EDRP_RscCombo {
			idc = 4302;
			x = 0.22;
			y = 0.74;
			w = 0.15;
			h = 0.04;
		};

		class DateFromEdit: EDRP_RscEdit {
			idc = 4303;
			text = "";
			x = 0.38;
			y = 0.74;
			w = 0.12;
			h = 0.04;
		};

		class DateToEdit: EDRP_RscEdit {
			idc = 4304;
			text = "";
			x = 0.51;
			y = 0.74;
			w = 0.12;
			h = 0.04;
		};

		class RefreshButton: EDRP_RscButtonMenu {
			idc = 4305;
			text = "Refresh";
			x = 0.64;
			y = 0.74;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_refreshTransactionHistory;";
		};

		class TotalText: EDRP_RscText {
			idc = 4306;
			text = "Total Transactions: 0";
			x = 0.22;
			y = 0.79;
			w = 0.3;
			h = 0.03;
		};

		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 4300;";
		};
	};
};
