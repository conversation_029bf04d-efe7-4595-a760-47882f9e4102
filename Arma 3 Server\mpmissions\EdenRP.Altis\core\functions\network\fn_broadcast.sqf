if(isServer && isDedicated) exitWith {};

//  File: fn_broadcast.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Broadcast system used in the life mission for multi-notification purposes.
private["_type","_message","_targetGangID","_exit","_gate"];
_type = param [0,0,[[],0]];
_message = param [1,"",[""]];
_localize = param [2,false,[false]];
_gate = param [4, "", [""]];
_gate = missionNamespace getVariable [_gate, true];

// These notifications were suppressed
if (!_gate) exitWith {};

if(_message == "") exitwith {};

if(_message isEqualTo "Your vehicle is ready!" || _message isEqualTo "Your vehicle is ready! Press the U key to lock and unlock your vehicle.") then{
	if((EDEN_stats_playtime_civ) <= 120) then {
		_message = "Your vehicle is ready! Press U to unlock or lock the vehicle, and T to open the trunk! Upgrades may be purchased at any service station.";
	};
};

if(_localize) exitWith {
	_arr = _this select 3;
	_msg = switch(count _arr) do {
		case 0: {localize _message;};
		case 1: {format[localize _message,_arr select 0];};
		case 2: {format[localize _message,_arr select 0, _arr select 1];};
		case 3: {format[localize _message,_arr select 0, _arr select 1, _arr select 2];};
		case 4: {format[localize _message,_arr select 0, _arr select 1, _arr select 2, _arr select 3];};
	};

	if(_type isEqualType []) then {
		for "_i" from 0 to (count _type)-1 do
		{
			switch((_type select _i)) do
			{
				case 0: {systemChat _msg;};
				case 1: {hint _msg;};
				case 2: {titleText[_msg,"PLAIN DOWN"];};
			};
		};
	} else {
		switch (_type) do
		{
			case 0: {systemChat _msg;};
			case 1: {hint _msg;};
			case 2: {titleText[_msg,"PLAIN DOWN"];};
		};
	};
};

if(_type isEqualType []) then {
	for "_i" from 0 to (count _type)-1 do {
		switch((_type select _i)) do {
			case 0: {systemChat _message};
			case 1: {hint format["%1", _message]};
			case 2: {titleText[format["%1",_message],"PLAIN DOWN"];};
			case 3: {hint parseText format["%1", _message]};
			case 4: {hint format["%1", _message];life_action_gangInUse = nil;};
			case 5: {["WarKillConfirm",["You have gained warpoints from a confirmed kill!"]] call bis_fnc_showNotification;};
			case 6: {["ConquestAlert",[_message]] call bis_fnc_showNotification;};
			case 7: {["AirdropAlert",[_message]] call bis_fnc_showNotification;};
		};
	};
} else {
	switch (_type) do {
		case 0: {systemChat _message};
		case 1: {hint format["%1", _message]};
		case 2: {titleText[format["%1",_message],"PLAIN DOWN"];};
		case 3: {hint parseText format["%1", _message]};
		case 4: {hint format["%1", _message];life_action_gangInUse = nil;};
		case 5: {["WarKillConfirm",["You have gained warpoints from killing an enemy gang member!"]] call bis_fnc_showNotification;};
		case 6: {["ConquestAlert",[_message]] call bis_fnc_showNotification;};
		case 7: {["AirdropAlert",[_message]] call bis_fnc_showNotification;};
	};
};
