version=53;
class EditorData
{
	moveGridStep=1;
	angleGridStep=0.2617994;
	scaleGridStep=1;
	autoGroupingDist=10;
	toggles=1;
	class ItemIDProvider
	{
		nextID=1000;
	};
	class MarkerIDProvider
	{
		nextID=500;
	};
	class Camera
	{
		pos[]={14441.292,20,16083.611};
		dir[]={0,0,1};
		up[]={0,1,0};
		aside[]={1,0,0};
	};
};
binarizationWanted=0;
addons[]=
{
	"A3_Characters_F",
	"a3_map_altis"
};
class AddonsMetaData
{
	class List
	{
		items=2;
		class Item0
		{
			className="A3_Characters_F";
			name="Arma 3 Alpha - Characters and Clothing";
			author="Bohemia Interactive";
			url="https://www.arma3.com";
		};
		class Item1
		{
			className="a3_map_altis";
			name="Arma 3 - Altis";
			author="Bohemia Interactive";
			url="https://www.arma3.com";
		};
	};
};
randomSeed=12345678;
class ScenarioData
{
	author="EdenRP Development Team";
	title="EdenRP Altis Life";
	subtitle="Immersive Roleplay Experience";
	description="Experience the ultimate Altis Life roleplay server with enhanced features, balanced gameplay, and immersive systems.";
};
class Mission
{
	class Intel
	{
		timeOfChanges=1800.0002;
		startWeather=0.30000001;
		startWind=0.1;
		startWaves=0.1;
		forecastWeather=0.30000001;
		forecastWind=0.1;
		forecastWaves=0.1;
		forecastLightnings=0.1;
		year=2035;
		month=6;
		day=24;
		hour=12;
		minute=0;
		startFogDecay=0.014;
		forecastFogDecay=0.014;
	};
	class Entities
	{
		items=3;
		class Item0
		{
			dataType="Group";
			side="West";
			class Entities
			{
				items=20;
				class Item0
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5543.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=7;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=1;
					type="B_Soldier_F";
				};
				class Item1
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5545.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=2;
					type="B_Soldier_F";
				};
				class Item2
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5547.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=3;
					type="B_Soldier_F";
				};
				class Item3
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5549.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=4;
					type="B_Soldier_F";
				};
				class Item4
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5551.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=5;
					type="B_Soldier_F";
				};
				class Item5
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5553.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=6;
					type="B_Soldier_F";
				};
				class Item6
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5555.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=7;
					type="B_Soldier_F";
				};
				class Item7
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5557.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=8;
					type="B_Soldier_F";
				};
				class Item8
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5559.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=9;
					type="B_Soldier_F";
				};
				class Item9
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5561.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=10;
					type="B_Soldier_F";
				};
				class Item10
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5563.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=11;
					type="B_Soldier_F";
				};
				class Item11
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5565.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=12;
					type="B_Soldier_F";
				};
				class Item12
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5567.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=13;
					type="B_Soldier_F";
				};
				class Item13
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5569.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=14;
					type="B_Soldier_F";
				};
				class Item14
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5571.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=15;
					type="B_Soldier_F";
				};
				class Item15
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5573.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=16;
					type="B_Soldier_F";
				};
				class Item16
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5575.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=17;
					type="B_Soldier_F";
				};
				class Item17
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5577.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=18;
					type="B_Soldier_F";
				};
				class Item18
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5579.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=19;
					type="B_Soldier_F";
				};
				class Item19
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={5581.8,5.5,12677.1};
						angles[]={0,0,0};
					};
					side="West";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initCop.sqf""";
						description="Police Officer";
						isPlayable=1;
					};
					id=20;
					type="B_Soldier_F";
				};
			};
			class Attributes
			{
			};
			id=0;
		};
		class Item1
		{
			dataType="Group";
			side="Independent";
			class Entities
			{
				items=15;
				class Item0
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14441.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=7;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=21;
					type="I_Soldier_F";
				};
				class Item1
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14443.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=22;
					type="I_Soldier_F";
				};
				class Item2
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14445.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=23;
					type="I_Soldier_F";
				};
				class Item3
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14447.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=24;
					type="I_Soldier_F";
				};
				class Item4
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14449.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=25;
					type="I_Soldier_F";
				};
				class Item5
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14451.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=26;
					type="I_Soldier_F";
				};
				class Item6
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14453.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=27;
					type="I_Soldier_F";
				};
				class Item7
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14455.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=28;
					type="I_Soldier_F";
				};
				class Item8
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14457.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=29;
					type="I_Soldier_F";
				};
				class Item9
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14459.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=30;
					type="I_Soldier_F";
				};
				class Item10
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14461.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=31;
					type="I_Soldier_F";
				};
				class Item11
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14463.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=32;
					type="I_Soldier_F";
				};
				class Item12
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14465.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=33;
					type="I_Soldier_F";
				};
				class Item13
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14467.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=34;
					type="I_Soldier_F";
				};
				class Item14
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={14469.292,5.5,16083.611};
						angles[]={0,0,0};
					};
					side="Independent";
					flags=5;
					class Attributes
					{
						rank="SERGEANT";
						init="call compile preprocessFileLineNumbers ""core/fn_initMedic.sqf""";
						description="EMS Paramedic";
						isPlayable=1;
					};
					id=35;
					type="I_Soldier_F";
				};
			};
			class Attributes
			{
			};
			id=1;
		};
		class Item2
		{
			dataType="Group";
			side="Civilian";
			class Entities
			{
				items=85;
				class Item0
				{
					dataType="Object";
					class PositionInfo
					{
						position[]={16000,5.5,16000};
						angles[]={0,0,0};
					};
					side="Civilian";
					flags=7;
					class Attributes
					{
						init="call compile preprocessFileLineNumbers ""core/fn_initCivilian.sqf""";
						description="Civilian";
						isPlayable=1;
					};
					id=36;
					type="C_man_1";
				};
			};
			class Attributes
			{
			};
			id=2;
		};
	};
};
class OutroLoose
{
	class Intel
	{
		startWeather=0.1;
		forecastWeather=0.1;
	};
};
class OutroWin
{
	class Intel
	{
		startWeather=0.1;
		forecastWeather=0.1;
	};
};
