/*
	EdenRP Altis Life - Gang War System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Gang warfare and conflict management system
	Version: 1.0.0
*/

// Initialize war system
EDRP_fnc_initWarSystem = {
	// War state variables
	EDRP_active_wars = [];
	EDRP_war_points = 0;
	EDRP_war_kills = 0;
	EDRP_war_deaths = 0;
	
	// War statistics
	EDRP_war_stats = createHashMapFromArray [
		["wars_started", 0],
		["wars_won", 0],
		["wars_lost", 0],
		["total_kills", 0],
		["total_deaths", 0],
		["points_earned", 0]
	];
	
	// Load war configuration
	[] call EDRP_fnc_loadWarConfig;
	
	["War system initialized"] call EDRP_fnc_logInfo;
};

// Load war configuration
EDRP_fnc_loadWarConfig = {
	// War costs and requirements
	EDRP_war_config = createHashMapFromArray [
		["declaration_cost", 25000], // Cost to declare war
		["min_members_online", 3], // Minimum members online to declare war
		["war_duration", 3600], // War duration in seconds (1 hour)
		["cooldown_period", 1800], // Cooldown between wars (30 minutes)
		["kill_points", 10], // Points per kill
		["death_penalty", -5], // Points lost per death
		["territory_bonus", 25], // Bonus points for killing in controlled territory
		["win_threshold", 100] // Points needed to win war
	];
	
	// War zones (areas where gang wars can occur)
	EDRP_war_zones = [
		["Industrial Zone", [4000, 4000, 0], 500],
		["Abandoned Airfield", [5000, 5000, 0], 750],
		["Quarry", [6000, 6000, 0], 400],
		["Salt Flats", [7000, 7000, 0], 600],
		["Mountain Pass", [8000, 8000, 0], 300]
	];
	
	// War rewards
	EDRP_war_rewards = createHashMapFromArray [
		["winner_cash", 100000], // Cash reward for winning gang
		["winner_territory_bonus", 0.2], // 20% territory income bonus for 24 hours
		["loser_penalty", 0.1] // 10% territory income penalty for 12 hours
	];
};

// Declare war on another gang
EDRP_fnc_declareWar = {
	params [["_targetGangID", 0, [0]]];
	
	if (count EDRP_gang_data == 0) exitWith {
		["You must be in a gang to declare war"] call EDRP_fnc_hint;
		false
	};
	
	// Check rank permissions (only leaders can declare war)
	if ((EDRP_gang_data select 2) < 5) exitWith {
		["Only gang leaders can declare war"] call EDRP_fnc_hint;
		false
	};
	
	private _gangID = EDRP_gang_data select 0;
	private _gangName = EDRP_gang_data select 1;
	
	// Check if already at war with target
	{
		if ((_x select 0) == _gangID && (_x select 1) == _targetGangID) exitWith {
			["You are already at war with this gang"] call EDRP_fnc_hint;
			false
		};
	} forEach EDRP_active_wars;
	
	// Check minimum members online
	private _onlineMembers = [_gangID] call EDRP_fnc_getOnlineGangMembers;
	if (count _onlineMembers < (EDRP_war_config get "min_members_online")) exitWith {
		[format ["You need at least %1 gang members online to declare war", EDRP_war_config get "min_members_online"]] call EDRP_fnc_hint;
		false
	};
	
	// Check if gang has enough money
	if (EDRP_gang_bank < (EDRP_war_config get "declaration_cost")) exitWith {
		[format ["Gang needs $%1 in the bank to declare war", [(EDRP_war_config get "declaration_cost")] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
		false
	};
	
	// Get target gang info
	private _targetGangData = [_targetGangID] call EDRP_fnc_getGangInfo;
	if (count _targetGangData == 0) exitWith {
		["Target gang not found"] call EDRP_fnc_hint;
		false
	};
	
	private _targetGangName = _targetGangData select 1;
	
	// Check target gang online members
	private _targetOnlineMembers = [_targetGangID] call EDRP_fnc_getOnlineGangMembers;
	if (count _targetOnlineMembers < (EDRP_war_config get "min_members_online")) exitWith {
		["Target gang doesn't have enough members online"] call EDRP_fnc_hint;
		false
	};
	
	// Send war declaration to server
	[_gangID, _gangName, _targetGangID, _targetGangName] remoteExec ["EDRP_fnc_processWarDeclaration", 2];
	
	["War declaration sent..."] call EDRP_fnc_hint;
	
	true
};

// Process war declaration (server-side)
EDRP_fnc_processWarDeclaration = {
	params [
		["_attackerGangID", 0, [0]],
		["_attackerGangName", "", [""]],
		["_defenderGangID", 0, [0]],
		["_defenderGangName", "", [""]]
	];
	
	// Create war data
	private _warData = [
		_attackerGangID,
		_defenderGangID,
		_attackerGangName,
		_defenderGangName,
		time, // Start time
		time + (EDRP_war_config get "war_duration"), // End time
		0, // Attacker points
		0, // Defender points
		"active" // Status
	];
	
	// Add to active wars
	EDRP_active_wars pushBack _warData;
	publicVariable "EDRP_active_wars";
	
	// Deduct declaration cost from attacker gang
	[_attackerGangID, -(EDRP_war_config get "declaration_cost")] call EDRP_fnc_updateGangBank;
	
	// Notify both gangs
	private _message = format ["%1 has declared war on %2! War will last %3 minutes.", 
		_attackerGangName, 
		_defenderGangName, 
		(EDRP_war_config get "war_duration") / 60
	];
	
	[_message, _attackerGangID] remoteExec ["EDRP_fnc_broadcastGangMessage", -2];
	[_message, _defenderGangID] remoteExec ["EDRP_fnc_broadcastGangMessage", -2];
	
	// Start war timer
	[_warData] spawn EDRP_fnc_warTimer;
	
	// Log war declaration
	[format ["WAR: %1 declared war on %2", _attackerGangName, _defenderGangName]] call EDRP_fnc_logInfo;
};

// War timer
EDRP_fnc_warTimer = {
	params [["_warData", [], [[]]]];
	
	_warData params [
		"_attackerGangID",
		"_defenderGangID", 
		"_attackerGangName",
		"_defenderGangName",
		"_startTime",
		"_endTime",
		"_attackerPoints",
		"_defenderPoints",
		"_status"
	];
	
	// Wait for war to end
	waitUntil { time >= _endTime || _status != "active" };
	
	// Find current war data (may have been updated)
	private _currentWarIndex = -1;
	{
		if ((_x select 0) == _attackerGangID && (_x select 1) == _defenderGangID) exitWith {
			_currentWarIndex = _forEachIndex;
			_warData = _x;
		};
	} forEach EDRP_active_wars;
	
	if (_currentWarIndex >= 0) then {
		// End the war
		[_warData] call EDRP_fnc_endWar;
		
		// Remove from active wars
		EDRP_active_wars deleteAt _currentWarIndex;
		publicVariable "EDRP_active_wars";
	};
};

// End war
EDRP_fnc_endWar = {
	params [["_warData", [], [[]]]];
	
	_warData params [
		"_attackerGangID",
		"_defenderGangID",
		"_attackerGangName", 
		"_defenderGangName",
		"_startTime",
		"_endTime",
		"_attackerPoints",
		"_defenderPoints",
		"_status"
	];
	
	// Determine winner
	private _winnerGangID = 0;
	private _winnerGangName = "";
	private _loserGangID = 0;
	private _loserGangName = "";
	
	if (_attackerPoints > _defenderPoints) then {
		_winnerGangID = _attackerGangID;
		_winnerGangName = _attackerGangName;
		_loserGangID = _defenderGangID;
		_loserGangName = _defenderGangName;
	} else {
		_winnerGangID = _defenderGangID;
		_winnerGangName = _defenderGangName;
		_loserGangID = _attackerGangID;
		_loserGangName = _attackerGangName;
	};
	
	// Award rewards
	[_winnerGangID, EDRP_war_rewards get "winner_cash"] call EDRP_fnc_updateGangBank;
	
	// Notify gangs of war result
	private _resultMessage = format [
		"WAR ENDED: %1 defeated %2 with %3 points vs %4 points!",
		_winnerGangName,
		_loserGangName,
		if (_winnerGangID == _attackerGangID) then { _attackerPoints } else { _defenderPoints },
		if (_winnerGangID == _attackerGangID) then { _defenderPoints } else { _attackerPoints }
	];
	
	[_resultMessage, _winnerGangID] remoteExec ["EDRP_fnc_broadcastGangMessage", -2];
	[_resultMessage, _loserGangID] remoteExec ["EDRP_fnc_broadcastGangMessage", -2];
	
	// Global announcement
	[_resultMessage, "Gang War Result"] remoteExec ["EDRP_fnc_globalMessage", -2];
	
	// Log war result
	[format ["WAR END: %1 defeated %2 (%3 vs %4 points)", _winnerGangName, _loserGangName, 
		if (_winnerGangID == _attackerGangID) then { _attackerPoints } else { _defenderPoints },
		if (_winnerGangID == _attackerGangID) then { _defenderPoints } else { _attackerPoints }
	]] call EDRP_fnc_logInfo;
};

// Handle gang war kill
EDRP_fnc_warKill = {
	params [
		["_killer", objNull, [objNull]],
		["_victim", objNull, [objNull]]
	];
	
	if (isNull _killer || isNull _victim || _killer == _victim) exitWith {};
	
	// Get gang data for both players
	private _killerGangData = _killer getVariable ["gang_data", []];
	private _victimGangData = _victim getVariable ["gang_data", []];
	
	if (count _killerGangData == 0 || count _victimGangData == 0) exitWith {};
	
	private _killerGangID = _killerGangData select 0;
	private _victimGangID = _victimGangData select 0;
	
	// Check if gangs are at war
	private _warIndex = -1;
	{
		if (((_x select 0) == _killerGangID && (_x select 1) == _victimGangID) ||
			((_x select 0) == _victimGangID && (_x select 1) == _killerGangID)) exitWith {
			_warIndex = _forEachIndex;
		};
	} forEach EDRP_active_wars;
	
	if (_warIndex < 0) exitWith {}; // Not at war
	
	// Award points
	private _points = EDRP_war_config get "kill_points";
	
	// Check for territory bonus
	{
		if ([_killerGangID, _x] call EDRP_fnc_gangControlsTerritory) then {
			private _territoryData = EDRP_territory_data get _x;
			private _position = _territoryData select 3;
			private _radius = _territoryData select 4;
			
			if (_victim distance _position <= _radius) then {
				_points = _points + (EDRP_war_config get "territory_bonus");
				[format ["Territory kill bonus: +%1 points", EDRP_war_config get "territory_bonus"]] remoteExec ["EDRP_fnc_hint", _killer];
			};
		};
	} forEach (keys EDRP_territory_data);
	
	// Update war points
	private _warData = EDRP_active_wars select _warIndex;
	if ((_warData select 0) == _killerGangID) then {
		// Attacker killed defender
		_warData set [6, (_warData select 6) + _points];
	} else {
		// Defender killed attacker
		_warData set [7, (_warData select 7) + _points];
	};
	
	EDRP_active_wars set [_warIndex, _warData];
	publicVariable "EDRP_active_wars";
	
	// Notify killer
	[format ["War kill: +%1 points", _points], "success"] remoteExec ["EDRP_fnc_hint", _killer];
	
	// Update personal stats
	[_killer, "war_kills", 1] remoteExec ["EDRP_fnc_updatePlayerStat", 2];
	[_victim, "war_deaths", 1] remoteExec ["EDRP_fnc_updatePlayerStat", 2];
};

// Get active wars for gang
EDRP_fnc_getGangWars = {
	params [["_gangID", 0, [0]]];
	
	private _wars = [];
	
	{
		if ((_x select 0) == _gangID || (_x select 1) == _gangID) then {
			_wars pushBack _x;
		};
	} forEach EDRP_active_wars;
	
	_wars
};

// Check if gangs are at war
EDRP_fnc_gangsAtWar = {
	params [
		["_gangID1", 0, [0]],
		["_gangID2", 0, [0]]
	];
	
	{
		if (((_x select 0) == _gangID1 && (_x select 1) == _gangID2) ||
			((_x select 0) == _gangID2 && (_x select 1) == _gangID1)) exitWith {
			true
		};
	} forEach EDRP_active_wars;
	
	false
};

// Open war menu
EDRP_fnc_openWarMenu = {
	if (count EDRP_gang_data == 0) exitWith {
		["You must be in a gang to view wars"] call EDRP_fnc_hint;
		false
	};
	
	// Create war dialog
	createDialog "EDRP_WarDialog";
	
	// Populate war information
	[] call EDRP_fnc_updateWarMenu;
	
	true
};

// Update war menu
EDRP_fnc_updateWarMenu = {
	private _display = findDisplay 37200;
	if (isNull _display) exitWith {};
	
	private _gangID = EDRP_gang_data select 0;
	private _wars = [_gangID] call EDRP_fnc_getGangWars;
	
	// Update active wars list
	private _warList = _display displayCtrl 37201;
	lbClear _warList;
	
	{
		_x params [
			"_attackerGangID",
			"_defenderGangID",
			"_attackerGangName",
			"_defenderGangName",
			"_startTime",
			"_endTime",
			"_attackerPoints",
			"_defenderPoints",
			"_status"
		];
		
		private _timeRemaining = _endTime - time;
		private _timeStr = [_timeRemaining] call EDRP_fnc_formatTime;
		
		private _entry = format ["%1 vs %2 - %3:%4 - %5",
			_attackerGangName,
			_defenderGangName,
			_attackerPoints,
			_defenderPoints,
			_timeStr
		];
		
		_warList lbAdd _entry;
		_warList lbSetData [_forEachIndex, str _x];
		
	} forEach _wars;
	
	// Update gang list for war declarations
	private _gangList = _display displayCtrl 37202;
	lbClear _gangList;
	
	// Get all gangs (would be populated from server)
	private _allGangs = [] call EDRP_fnc_getAllGangs;
	{
		_x params ["_id", "_name", "_memberCount"];
		
		if (_id != _gangID) then {
			_gangList lbAdd format ["%1 (%2 members)", _name, _memberCount];
			_gangList lbSetData [_forEachIndex, str _id];
		};
	} forEach _allGangs;
};

// Declare war from menu
EDRP_fnc_declareWarFromMenu = {
	private _display = findDisplay 37200;
	if (isNull _display) exitWith {};
	
	private _gangList = _display displayCtrl 37202;
	private _selection = lbCurSel _gangList;
	
	if (_selection < 0) exitWith {
		["No gang selected"] call EDRP_fnc_hint;
	};
	
	private _targetGangID = parseNumber (_gangList lbData _selection);
	private _targetGangName = _gangList lbText _selection;
	
	// Confirm war declaration
	private _message = format ["Are you sure you want to declare war on %1? This will cost $%2.", 
		_targetGangName, 
		[(EDRP_war_config get "declaration_cost")] call EDRP_fnc_numberText
	];
	
	if ([_message, "Declare War", true, true] call EDRP_fnc_messageBox) then {
		[_targetGangID] call EDRP_fnc_declareWar;
		closeDialog 0;
	};
};

// Initialize war system on client
if (hasInterface) then {
	[] call EDRP_fnc_initWarSystem;
	
	// Add kill event handler for war points
	player addEventHandler ["Killed", {
		params ["_unit", "_killer"];
		if (!isNull _killer && _killer != _unit) then {
			[_killer, _unit] remoteExec ["EDRP_fnc_warKill", 2];
		};
	}];
};
