// File: fn_markVehicle.sqf
// Author: Trimo
// Description: mark vehicle for anti despawn

if((_this) getVariable["markedForAntiDespawn",false]) exitWith {hint "This vehicle is already persistence marked!";};
if((_this) getVariable["baited",false]) exitWith {hint "You cannot persistence mark bait vehicles!";};
if(eden_markCD > time) exitWith {hint "Please wait before using the mark feature again!";};

private _keys = false;
{
	if((getPlayerUID player) isEqualTo (_x select 0)) then {
		_keys = true;
	};
} forEach ((_this) getVariable["vehicle_info_owners",[]]);

eden_markCD = time + 60;

if !(isNull (player getVariable["currMarked",objNull])) then {
	(player getVariable["currMarked",objNull]) setVariable["markedForAntiDespawn",nil,true];
};

player setVariable["currMarked",(_this)];
(_this) setVariable["markedForAntiDespawn",true,true];

if (!(isNull (findDisplay 33000)) && _keys) then {
	[] spawn EDEN_fnc_updateKeyChainTab;
};

systemChat format["You have persistence marked this %1. It will not be eligible for despawn until you mark another vehicle or disconnect.",getText(configFile >> "CfgVehicles" >> (typeOf (_this)) >> "displayName")];