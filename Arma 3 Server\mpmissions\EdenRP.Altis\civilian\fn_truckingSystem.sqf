/*
	EdenRP Altis Life - Trucking System
	Author: EdenRP Development Team
	Description: Handles trucking jobs and deliveries
	Version: 1.0.0
*/

// Initialize trucking system
EDRP_fnc_initTruckingSystem = {
	// Trucking state variables
	EDRP_trucking_active = false;
	EDRP_trucking_job = [];
	EDRP_trucking_vehicle = objNull;
	EDRP_trucking_cargo = "";
	EDRP_trucking_destination = "";
	EDRP_trucking_payment = 0;
	EDRP_trucking_deadline = 0;
	
	// Trucking statistics
	EDRP_trucking_stats = createHashMapFromArray [
		["deliveries_completed", 0],
		["total_distance", 0],
		["total_earned", 0],
		["cargo_delivered", 0],
		["on_time_deliveries", 0]
	];
	
	["Trucking system initialized"] call EDRP_fnc_logInfo;
};

// Start trucking job
EDRP_fnc_startTruckingJob = {
	params [
		["_jobType", "", [""]],
		["_startLocation", "", [""]]
	];
	
	if (EDRP_trucking_active) exitWith {
		["You already have an active trucking job"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player has trucking license
	if !("trucking" in EDRP_player_licenses) exitWith {
		["You need a trucking license to take delivery jobs"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player is near a suitable vehicle
	private _nearbyVehicles = nearestObjects [player, ["Truck_F", "Truck_02_base_F", "Truck_03_base_F"], 10];
	if (count _nearbyVehicles == 0) exitWith {
		["You need to be near a truck to start a delivery job"] call EDRP_fnc_hint;
		false
	};
	
	private _vehicle = _nearbyVehicles select 0;
	if (locked _vehicle > 1) exitWith {
		["The vehicle is locked"] call EDRP_fnc_hint;
		false
	};
	
	// Generate random trucking job
	private _jobData = [_jobType, _startLocation] call EDRP_fnc_generateTruckingJob;
	if (_jobData isEqualTo []) exitWith {
		["No trucking jobs available at this location"] call EDRP_fnc_hint;
		false
	};
	
	_jobData params ["_cargo", "_destination", "_distance", "_payment", "_timeLimit"];
	
	// Set trucking variables
	EDRP_trucking_active = true;
	EDRP_trucking_job = _jobData;
	EDRP_trucking_vehicle = _vehicle;
	EDRP_trucking_cargo = _cargo;
	EDRP_trucking_destination = _destination;
	EDRP_trucking_payment = _payment;
	EDRP_trucking_deadline = time + _timeLimit;
	
	// Add cargo to vehicle (visual)
	[_vehicle, _cargo] call EDRP_fnc_loadTruckCargo;
	
	// Create delivery marker
	private _marker = createMarkerLocal [format ["trucking_dest_%1", random 1000], getMarkerPos _destination];
	_marker setMarkerTypeLocal "hd_delivery";
	_marker setMarkerColorLocal "ColorBlue";
	_marker setMarkerTextLocal format ["Deliver %1", _cargo];
	
	// Show job accepted message
	[format ["Job accepted: Deliver %1 to %2 for $%3", _cargo, _destination, [_payment] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
	
	// Start trucking monitoring
	[] spawn EDRP_fnc_truckingMonitorLoop;
	
	true
};

// Generate trucking job
EDRP_fnc_generateTruckingJob = {
	params [
		["_jobType", "", [""]],
		["_startLocation", "", [""]]
	];
	
	// Available cargo types
	private _cargoTypes = [
		"Construction Materials", "Food Supplies", "Medical Equipment",
		"Electronics", "Fuel Barrels", "Industrial Parts",
		"Furniture", "Textiles", "Agricultural Products"
	];
	
	// Available destinations
	private _destinations = [
		"delivery_kavala", "delivery_athira", "delivery_pyrgos",
		"delivery_sofia", "delivery_zaros", "delivery_paros"
	];
	
	// Remove start location from destinations
	_destinations = _destinations - [_startLocation];
	
	if (count _destinations == 0) exitWith { [] };
	
	private _cargo = selectRandom _cargoTypes;
	private _destination = selectRandom _destinations;
	private _distance = (getMarkerPos _startLocation) distance2D (getMarkerPos _destination);
	
	// Calculate payment based on distance and cargo type
	private _basePayment = 500;
	private _distanceBonus = _distance * 2;
	private _cargoMultiplier = switch (_cargo) do {
		case "Medical Equipment": { 1.5 };
		case "Electronics": { 1.3 };
		case "Fuel Barrels": { 1.2 };
		default { 1.0 };
	};
	
	private _payment = round ((_basePayment + _distanceBonus) * _cargoMultiplier);
	
	// Calculate time limit (generous but not unlimited)
	private _timeLimit = (_distance / 50) * 60; // Assuming 50 km/h average speed
	_timeLimit = _timeLimit + 300; // Add 5 minute buffer
	
	[_cargo, _destination, _distance, _payment, _timeLimit]
};

// Load cargo into truck (visual effect)
EDRP_fnc_loadTruckCargo = {
	params [
		["_vehicle", objNull, [objNull]],
		["_cargo", "", [""]]
	];
	
	if (isNull _vehicle) exitWith {};
	
	// Add cargo boxes to vehicle (visual)
	private _cargoBoxes = [];
	for "_i" from 1 to (2 + floor(random 4)) do {
		private _box = "Box_NATO_Equip_F" createVehicle [0,0,0];
		_box attachTo [_vehicle, [
			-1 + (random 2),
			-2 + (random 4),
			0.5 + (random 0.5)
		]];
		_cargoBoxes pushBack _box;
	};
	
	_vehicle setVariable ["EDRP_cargo_boxes", _cargoBoxes];
	_vehicle setVariable ["EDRP_cargo_type", _cargo];
};

// Unload cargo from truck
EDRP_fnc_unloadTruckCargo = {
	params [["_vehicle", objNull, [objNull]]];
	
	if (isNull _vehicle) exitWith {};
	
	private _cargoBoxes = _vehicle getVariable ["EDRP_cargo_boxes", []];
	{
		deleteVehicle _x;
	} forEach _cargoBoxes;
	
	_vehicle setVariable ["EDRP_cargo_boxes", nil];
	_vehicle setVariable ["EDRP_cargo_type", nil];
};

// Trucking monitoring loop
EDRP_fnc_truckingMonitorLoop = {
	while {EDRP_trucking_active} do {
		// Check if vehicle still exists and player is near it
		if (isNull EDRP_trucking_vehicle || player distance EDRP_trucking_vehicle > 100) then {
			["Trucking job failed - vehicle lost or abandoned"] call EDRP_fnc_hint;
			[] call EDRP_fnc_cancelTruckingJob;
		};
		
		// Check if deadline passed
		if (time > EDRP_trucking_deadline) then {
			["Trucking job failed - deadline exceeded"] call EDRP_fnc_hint;
			[] call EDRP_fnc_cancelTruckingJob;
		};
		
		// Check if at destination
		if (player distance2D (getMarkerPos EDRP_trucking_destination) < 50) then {
			[] call EDRP_fnc_completeTruckingJob;
		};
		
		sleep 5;
	};
};

// Complete trucking job
EDRP_fnc_completeTruckingJob = {
	if (!EDRP_trucking_active) exitWith {};
	
	// Check if player is in the delivery vehicle
	if (vehicle player != EDRP_trucking_vehicle) exitWith {
		["You must be in the delivery vehicle to complete the job"] call EDRP_fnc_hint;
	};
	
	// Unload cargo
	[EDRP_trucking_vehicle] call EDRP_fnc_unloadTruckCargo;
	
	// Calculate final payment (with bonuses)
	private _finalPayment = EDRP_trucking_payment;
	private _onTime = time < EDRP_trucking_deadline;
	
	if (_onTime) then {
		_finalPayment = _finalPayment * 1.2; // 20% bonus for on-time delivery
		EDRP_trucking_stats set ["on_time_deliveries", (EDRP_trucking_stats get "on_time_deliveries") + 1];
	};
	
	// Apply trucking skill bonus
	private _truckingLevel = EDRP_job_skills get "trucking";
	private _skillBonus = 1.0 + (_truckingLevel * 0.05); // 5% per level
	_finalPayment = round (_finalPayment * _skillBonus);
	
	// Pay player
	EDRP_player_cash = EDRP_player_cash + _finalPayment;
	
	// Add XP
	private _xpGain = 25 + (_finalPayment / 100);
	["trucking", _xpGain] call EDRP_fnc_addJobXP;
	
	// Update statistics
	EDRP_trucking_stats set ["deliveries_completed", (EDRP_trucking_stats get "deliveries_completed") + 1];
	EDRP_trucking_stats set ["total_earned", (EDRP_trucking_stats get "total_earned") + _finalPayment];
	EDRP_trucking_stats set ["cargo_delivered", (EDRP_trucking_stats get "cargo_delivered") + 1];
	
	// Show completion message
	private _bonusText = if (_onTime) then { " (On-time bonus!)" } else { "" };
	[format ["Delivery completed! Payment: $%1%2", [_finalPayment] call EDRP_fnc_numberText, _bonusText], "success"] call EDRP_fnc_hint;
	
	// Clean up
	[] call EDRP_fnc_endTruckingJob;
	
	playSound "cash_register";
};

// Cancel trucking job
EDRP_fnc_cancelTruckingJob = {
	if (!EDRP_trucking_active) exitWith {};
	
	// Unload cargo if vehicle still exists
	if (!isNull EDRP_trucking_vehicle) then {
		[EDRP_trucking_vehicle] call EDRP_fnc_unloadTruckCargo;
	};
	
	// Small penalty for cancellation
	private _penalty = EDRP_trucking_payment * 0.1;
	if (EDRP_player_cash >= _penalty) then {
		EDRP_player_cash = EDRP_player_cash - _penalty;
		[format ["Job cancelled - penalty: $%1", [_penalty] call EDRP_fnc_numberText], "warning"] call EDRP_fnc_hint;
	};
	
	[] call EDRP_fnc_endTruckingJob;
};

// End trucking job (cleanup)
EDRP_fnc_endTruckingJob = {
	// Remove delivery marker
	{
		if (markerText _x find "Deliver" >= 0) then {
			deleteMarkerLocal _x;
		};
	} forEach allMapMarkers;
	
	// Reset variables
	EDRP_trucking_active = false;
	EDRP_trucking_job = [];
	EDRP_trucking_vehicle = objNull;
	EDRP_trucking_cargo = "";
	EDRP_trucking_destination = "";
	EDRP_trucking_payment = 0;
	EDRP_trucking_deadline = 0;
};

// Get available trucking locations
EDRP_fnc_getTruckingLocations = {
	[
		"trucking_depot_kavala",
		"trucking_depot_athira", 
		"trucking_depot_pyrgos",
		"trucking_depot_sofia"
	]
};

// Initialize trucking system on client
if (hasInterface) then {
	[] call EDRP_fnc_initTruckingSystem;
};
