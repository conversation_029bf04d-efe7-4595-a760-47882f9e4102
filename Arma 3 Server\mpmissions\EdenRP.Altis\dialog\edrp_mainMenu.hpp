/*
	EdenRP Altis Life - Main Interaction Menu (Y-Menu)
	Author: EdenRP Development Team
	Description: Main player interaction menu with tabbed interface
	Version: 1.0.0
*/

class EDRP_MainMenu: EDRP_MenuBase {
	idd = 3000;
	onLoad = "[] spawn EDRP_fnc_updateMainMenu;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "EdenRP - Main Menu";
		};
		class Header: BaseHeader {
			text = "Player Information & Actions";
		};
		
		// Tab backgrounds
		class Tab1Background: BaseTab1Background {};
		class Tab2Background: BaseTab2Background {};
		class Tab3Background: BaseTab3Background {};
		class Tab4Background: BaseTab4Background {};
		class Tab5Background: BaseTab5Background {};
		class Tab6Background: BaseTab6Background {};
		
		// Content areas
		class SidePanelBackground: BaseSidePanelBackground {};
		class MainContentBackground: BaseMainContentBackground {};
		
		// Player info section
		class PlayerInfoBackground: EDRP_RscText {
			idc = -1;
			x = 0.41;
			y = 0.28;
			w = 0.37;
			h = 0.15;
			colorBackground[] = {0.1, 0.1, 0.1, 0.8};
		};
		
		// Money display backgrounds
		class CashBackground: EDRP_RscText {
			idc = -1;
			x = 0.42;
			y = 0.45;
			w = 0.17;
			h = 0.08;
			colorBackground[] = {0.0, 0.4, 0.0, 0.6};
		};
		
		class BankBackground: EDRP_RscText {
			idc = -1;
			x = 0.60;
			y = 0.45;
			w = 0.17;
			h = 0.08;
			colorBackground[] = {0.0, 0.0, 0.4, 0.6};
		};
	};
	
	class controls: controlsBase {
		// Tab buttons
		class Tab1: BaseTab1 {
			text = "Main";
			tooltip = "Main menu and player information";
			onButtonClick = "[3000, 'main'] call EDRP_fnc_switchMenuTab;";
		};
		
		class Tab2: BaseTab2 {
			text = "Inventory";
			tooltip = "View and manage your inventory";
			onButtonClick = "[3000, 'inventory'] call EDRP_fnc_switchMenuTab;";
		};
		
		class Tab3: BaseTab3 {
			text = "Vehicle";
			tooltip = "Vehicle management and keys";
			onButtonClick = "[3000, 'vehicle'] call EDRP_fnc_switchMenuTab;";
		};
		
		class Tab4: BaseTab4 {
			text = "Phone";
			tooltip = "Communication and contacts";
			onButtonClick = "[3000, 'phone'] call EDRP_fnc_switchMenuTab;";
		};
		
		class Tab5: BaseTab5 {
			text = "Stats";
			tooltip = "Player statistics and progression";
			onButtonClick = "[3000, 'stats'] call EDRP_fnc_switchMenuTab;";
		};
		
		class Tab6: BaseTab6 {
			text = "Settings";
			tooltip = "Game settings and preferences";
			onButtonClick = "[3000, 'settings'] call EDRP_fnc_switchMenuTab;";
		};
		
		// Side panel - Quick actions
		class QuickActionsList: BaseSideListBox {
			idc = 3001;
			x = 0.22;
			y = 0.28;
			w = 0.16;
			h = 0.5;
			onLBDblClick = "[] call EDRP_fnc_executeQuickAction;";
		};
		
		class QuickActionsTitle: EDRP_RscText {
			idc = -1;
			text = "Quick Actions";
			x = 0.22;
			y = 0.26;
			w = 0.16;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			sizeEx = 0.03;
		};
		
		// Player information display
		class PlayerNameText: EDRP_RscText {
			idc = 3002;
			text = "";
			x = 0.42;
			y = 0.29;
			w = 0.35;
			h = 0.03;
			style = 2;
			sizeEx = 0.04;
			font = "RobotoCondensedBold";
		};
		
		class PlayerLevelText: EDRP_RscText {
			idc = 3003;
			text = "";
			x = 0.42;
			y = 0.32;
			w = 0.17;
			h = 0.025;
			sizeEx = 0.03;
		};
		
		class PlayerRankText: EDRP_RscText {
			idc = 3004;
			text = "";
			x = 0.60;
			y = 0.32;
			w = 0.17;
			h = 0.025;
			sizeEx = 0.03;
		};
		
		class PlayerPlaytimeText: EDRP_RscText {
			idc = 3005;
			text = "";
			x = 0.42;
			y = 0.35;
			w = 0.35;
			h = 0.025;
			sizeEx = 0.03;
		};
		
		class PlayerLocationText: EDRP_RscText {
			idc = 3006;
			text = "";
			x = 0.42;
			y = 0.38;
			w = 0.35;
			h = 0.025;
			sizeEx = 0.03;
		};
		
		// Money displays
		class CashIcon: EDRP_RscPicture {
			idc = -1;
			text = "images\icons\money.paa";
			x = 0.43;
			y = 0.46;
			w = 0.03;
			h = 0.03;
		};
		
		class CashLabel: EDRP_RscText {
			idc = -1;
			text = "Cash";
			x = 0.47;
			y = 0.46;
			w = 0.1;
			h = 0.025;
			sizeEx = 0.03;
			font = "RobotoCondensedBold";
		};
		
		class CashValue: EDRP_RscStructuredText {
			idc = 3007;
			text = "$0";
			x = 0.43;
			y = 0.49;
			w = 0.15;
			h = 0.03;
			style = 2;
			size = 0.035;
		};
		
		class BankIcon: EDRP_RscPicture {
			idc = -1;
			text = "images\icons\bank.paa";
			x = 0.61;
			y = 0.46;
			w = 0.03;
			h = 0.03;
		};
		
		class BankLabel: EDRP_RscText {
			idc = -1;
			text = "Bank";
			x = 0.65;
			y = 0.46;
			w = 0.1;
			h = 0.025;
			sizeEx = 0.03;
			font = "RobotoCondensedBold";
		};
		
		class BankValue: EDRP_RscStructuredText {
			idc = 3008;
			text = "$0";
			x = 0.61;
			y = 0.49;
			w = 0.15;
			h = 0.03;
			style = 2;
			size = 0.035;
		};
		
		// Main content area - varies by tab
		class MainContentList: BaseMainListBox {
			idc = 3010;
			x = 0.41;
			y = 0.55;
			w = 0.37;
			h = 0.23;
			onLBDblClick = "[] call EDRP_fnc_executeMainAction;";
		};
		
		// Action buttons
		class ActionButton1: EDRP_RscButtonMenu {
			idc = 3011;
			text = "Action 1";
			x = 0.41;
			y = 0.80;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_mainAction1;";
		};
		
		class ActionButton2: EDRP_RscButtonMenu {
			idc = 3012;
			text = "Action 2";
			x = 0.50;
			y = 0.80;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_mainAction2;";
		};
		
		class ActionButton3: EDRP_RscButtonMenu {
			idc = 3013;
			text = "Action 3";
			x = 0.59;
			y = 0.80;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_mainAction3;";
		};
		
		class ActionButton4: EDRP_RscButtonMenu {
			idc = 3014;
			text = "Action 4";
			x = 0.68;
			y = 0.80;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_mainAction4;";
		};
		
		// Input fields for various actions
		class InputEdit: EDRP_RscEdit {
			idc = 3020;
			text = "";
			x = 0.22;
			y = 0.80;
			w = 0.12;
			h = 0.04;
		};
		
		class InputCombo: EDRP_RscCombo {
			idc = 3021;
			x = 0.22;
			y = 0.75;
			w = 0.12;
			h = 0.04;
		};
		
		// Status and information
		class StatusText: BaseStatusText {
			idc = 3030;
			text = "Ready";
		};
		
		class InfoText: EDRP_RscStructuredText {
			idc = 3031;
			text = "Welcome to EdenRP! Use the tabs above to navigate different sections.";
			x = 0.22;
			y = 0.82;
			w = 0.56;
			h = 0.04;
		};
		
		// Close button
		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 3000; [] call EDRP_fnc_closeMainMenu;";
		};
	};
};

// Quick action selection dialog
class EDRP_QuickActionDialog: EDRP_MenuBaseCompact {
	idd = 3100;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Quick Action";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class ActionList: BaseListBox {
			idc = 3101;
			onLBDblClick = "[] call EDRP_fnc_executeSelectedQuickAction; closeDialog 3100;";
		};
		
		class ExecuteButton: BaseButtonOK {
			text = "Execute";
			onButtonClick = "[] call EDRP_fnc_executeSelectedQuickAction; closeDialog 3100;";
		};
		
		class ButtonClose: BaseButtonClose {};
	};
};

// Give money dialog
class EDRP_GiveMoneyDialog: EDRP_MenuBaseCompact {
	idd = 3200;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Give Money";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class PlayerList: EDRP_RscCombo {
			idc = 3201;
			x = 0.32;
			y = 0.3;
			w = 0.36;
			h = 0.04;
		};
		
		class AmountEdit: EDRP_RscEdit {
			idc = 3202;
			text = "0";
			x = 0.32;
			y = 0.36;
			w = 0.36;
			h = 0.04;
		};
		
		class TypeCombo: EDRP_RscCombo {
			idc = 3203;
			x = 0.32;
			y = 0.42;
			w = 0.36;
			h = 0.04;
		};
		
		class PlayerLabel: EDRP_RscText {
			idc = -1;
			text = "Select Player:";
			x = 0.32;
			y = 0.28;
			w = 0.36;
			h = 0.02;
		};
		
		class AmountLabel: EDRP_RscText {
			idc = -1;
			text = "Amount:";
			x = 0.32;
			y = 0.34;
			w = 0.36;
			h = 0.02;
		};
		
		class TypeLabel: EDRP_RscText {
			idc = -1;
			text = "Type:";
			x = 0.32;
			y = 0.40;
			w = 0.36;
			h = 0.02;
		};
		
		class GiveButton: BaseButtonOK {
			text = "Give Money";
			onButtonClick = "[] call EDRP_fnc_giveMoney; closeDialog 3200;";
		};
		
		class ButtonClose: BaseButtonClose {};
	};
};
