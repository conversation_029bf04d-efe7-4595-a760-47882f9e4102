//	File: fn_handleVehicles.sqf
//	Author: Fusah
//	Description: <PERSON><PERSON> opening of player garages.

if(eden_garageCooldown > time) exitWith {hint "Please do not spam your garage. It may take a bit to show your vehicles if the server is under heavy load.";};

params ["_type","_spawn"];

[[getPlayerUID player,playerSide,_type,player],"EDENS_fnc_getVehicles",false,false] spawn EDEN_fnc_MP;
["Life_impound_menu"] call EDEN_fnc_createDialog;
disableSerialization;
ctrlSetText[2802,"Fetching Vehicles...."];
eden_garage_sp = _spawn;
eden_garage_type = _type;
eden_garageCooldown = time+5;