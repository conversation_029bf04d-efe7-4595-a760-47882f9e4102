/*
	EdenRP Altis Life - Civilian Job System
	Author: EdenRP Development Team
	Description: Core civilian job management system with progression and skills
	Version: 1.0.0
*/

// Initialize civilian job system
EDRP_fnc_initJobSystem = {
	// Job skill levels
	EDRP_job_skills = createHashMapFromArray [
		["mining", 0],
		["smelting", 0],
		["farming", 0],
		["fishing", 0],
		["hunting", 0],
		["logging", 0],
		["refining", 0],
		["cooking", 0],
		["crafting", 0],
		["trading", 0],
		["trucking", 0],
		["salvaging", 0]
	];
	
	// Job XP tracking
	EDRP_job_xp = createHashMapFromArray [
		["mining", 0],
		["smelting", 0],
		["farming", 0],
		["fishing", 0],
		["hunting", 0],
		["logging", 0],
		["refining", 0],
		["cooking", 0],
		["crafting", 0],
		["trading", 0],
		["trucking", 0],
		["salvaging", 0]
	];
	
	// Current active job
	EDRP_current_job = "";
	EDRP_job_active = false;
	EDRP_job_location = "";
	EDRP_job_progress = 0;
	
	// Job statistics
	EDRP_job_stats = createHashMapFromArray [
		["total_gathered", 0],
		["total_processed", 0],
		["total_sold", 0],
		["total_earned", 0],
		["jobs_completed", 0],
		["distance_traveled", 0]
	];
	
	// Load job data from server
	[] call EDRP_fnc_requestJobData;
	
	// Start job monitoring
	[] spawn EDRP_fnc_jobMonitorLoop;
	
	["Job system initialized"] call EDRP_fnc_logInfo;
};

// Start a job
EDRP_fnc_startJob = {
	params [
		["_jobType", "", [""]],
		["_location", "", [""]]
	];
	
	if (_jobType == "" || _location == "") exitWith {
		["Invalid job parameters"] call EDRP_fnc_hint;
		false
	};
	
	// Check if already working
	if (EDRP_job_active) exitWith {
		["You are already working on a job"] call EDRP_fnc_hint;
		false
	};
	
	// Check job requirements
	private _canStart = [_jobType] call EDRP_fnc_checkJobRequirements;
	if (!_canStart) exitWith { false };
	
	// Set job variables
	EDRP_current_job = _jobType;
	EDRP_job_active = true;
	EDRP_job_location = _location;
	EDRP_job_progress = 0;
	
	// Show job started notification
	[format ["Started %1 job at %2", _jobType, _location], "success"] call EDRP_fnc_hint;
	
	// Log job start
	[format ["Player started job: %1 at %2", _jobType, _location]] call EDRP_fnc_logInfo;
	
	true
};

// End current job
EDRP_fnc_endJob = {
	params [
		["_reason", "completed", [""]]
	];
	
	if (!EDRP_job_active) exitWith {
		["No active job to end"] call EDRP_fnc_hint;
		false
	};
	
	private _jobType = EDRP_current_job;
	
	// Calculate job completion bonus
	if (_reason == "completed") then {
		private _bonus = [_jobType, EDRP_job_progress] call EDRP_fnc_calculateJobBonus;
		if (_bonus > 0) then {
			EDRP_player_cash = EDRP_player_cash + _bonus;
			[format ["Job completed! Bonus: $%1", [_bonus] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
		};
		
		// Update job statistics
		EDRP_job_stats set ["jobs_completed", (EDRP_job_stats get "jobs_completed") + 1];
	};
	
	// Reset job variables
	EDRP_current_job = "";
	EDRP_job_active = false;
	EDRP_job_location = "";
	EDRP_job_progress = 0;
	
	// Show job ended notification
	[format ["Job ended: %1", _reason], "info"] call EDRP_fnc_hint;
	
	// Save job data
	[] call EDRP_fnc_saveJobData;
	
	true
};

// Check job requirements
EDRP_fnc_checkJobRequirements = {
	params [["_jobType", "", [""]]];
	
	private _config = [_jobType] call EDRP_fnc_getJobConfig;
	if (_config isEqualTo []) exitWith { false };
	
	_config params ["_name", "_levelReq", "_licenseReq", "_toolsReq"];
	
	// Check level requirement
	private _currentLevel = EDRP_job_skills get _jobType;
	if (_currentLevel < _levelReq) exitWith {
		[format ["Requires %1 level %2 (current: %3)", _jobType, _levelReq, _currentLevel], "error"] call EDRP_fnc_hint;
		false
	};
	
	// Check license requirement
	if (_licenseReq != "" && !(_licenseReq in EDRP_player_licenses)) exitWith {
		[format ["Requires %1 license", _licenseReq], "error"] call EDRP_fnc_hint;
		false
	};
	
	// Check tool requirements
	{
		if !([_x] call EDRP_fnc_hasItem) exitWith {
			[format ["Requires %1", [_x] call EDRP_fnc_getItemName], "error"] call EDRP_fnc_hint;
			false
		};
	} forEach _toolsReq;
	
	true
};

// Get job configuration
EDRP_fnc_getJobConfig = {
	params [["_jobType", "", [""]]];
	
	private _configs = createHashMapFromArray [
		["mining", ["Mining", 0, "", ["pickaxe"]]],
		["smelting", ["Smelting", 1, "", []]],
		["farming", ["Farming", 0, "", []]],
		["fishing", ["Fishing", 0, "", ["fishingrod"]]],
		["hunting", ["Hunting", 2, "hunting", ["huntingknife"]]],
		["logging", ["Logging", 0, "", ["chainsaw"]]],
		["refining", ["Refining", 1, "", ["chemkit"]]],
		["cooking", ["Cooking", 0, "", []]],
		["crafting", ["Crafting", 3, "", ["toolkit"]]],
		["trading", ["Trading", 0, "trader", []]],
		["trucking", ["Trucking", 0, "trucking", []]],
		["salvaging", ["Salvaging", 2, "", ["toolkit"]]]
	];
	
	_configs getOrDefault [_jobType, []]
};

// Calculate job completion bonus
EDRP_fnc_calculateJobBonus = {
	params [
		["_jobType", "", [""]],
		["_progress", 0, [0]]
	];
	
	private _baseBonus = 500;
	private _skillLevel = EDRP_job_skills get _jobType;
	private _skillMultiplier = 1 + (_skillLevel * 0.1);
	private _progressMultiplier = _progress / 100;
	
	private _bonus = _baseBonus * _skillMultiplier * _progressMultiplier;
	round _bonus
};

// Add job XP
EDRP_fnc_addJobXP = {
	params [
		["_jobType", "", [""]],
		["_amount", 0, [0]]
	];
	
	if (_jobType == "" || _amount <= 0) exitWith {};
	
	private _currentXP = EDRP_job_xp get _jobType;
	private _newXP = _currentXP + _amount;
	EDRP_job_xp set [_jobType, _newXP];
	
	// Check for level up
	private _currentLevel = EDRP_job_skills get _jobType;
	private _requiredXP = [_jobType, _currentLevel + 1] call EDRP_fnc_getRequiredXP;
	
	if (_newXP >= _requiredXP) then {
		EDRP_job_skills set [_jobType, _currentLevel + 1];
		[format ["Level up! %1 is now level %2", _jobType, _currentLevel + 1], "success"] call EDRP_fnc_hint;
		playSound "level_up";
		
		// Award level up bonus
		private _levelBonus = (_currentLevel + 1) * 100;
		EDRP_player_cash = EDRP_player_cash + _levelBonus;
		[format ["Level bonus: $%1", [_levelBonus] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
	};
	
	// Show XP gain
	[format ["+%1 %2 XP", _amount, _jobType], "info"] call EDRP_fnc_hint;
};

// Get required XP for level
EDRP_fnc_getRequiredXP = {
	params [
		["_jobType", "", [""]],
		["_level", 1, [0]]
	];
	
	// XP formula: level^2 * 100
	(_level * _level) * 100
};

// Job monitoring loop
EDRP_fnc_jobMonitorLoop = {
	while {true} do {
		if (EDRP_job_active) then {
			// Update job progress based on activity
			[] call EDRP_fnc_updateJobProgress;
			
			// Check if job should end (distance, time, etc.)
			[] call EDRP_fnc_checkJobCompletion;
		};
		
		sleep 5;
	};
};

// Update job progress
EDRP_fnc_updateJobProgress = {
	if (!EDRP_job_active) exitWith {};
	
	private _jobType = EDRP_current_job;
	private _location = EDRP_job_location;
	
	// Check if player is still in job area
	private _inArea = [_location] call EDRP_fnc_isInJobArea;
	if (!_inArea) exitWith {
		["You have left the job area", "warning"] call EDRP_fnc_hint;
		["abandoned"] call EDRP_fnc_endJob;
	};
	
	// Increase progress based on activity
	EDRP_job_progress = EDRP_job_progress + 1;
	
	// Cap progress at 100
	if (EDRP_job_progress > 100) then {
		EDRP_job_progress = 100;
	};
};

// Check if player is in job area
EDRP_fnc_isInJobArea = {
	params [["_location", "", [""]]];
	
	private _pos = getMarkerPos _location;
	private _size = getMarkerSize _location;
	private _distance = player distance2D _pos;
	
	_distance <= (_size select 0)
};

// Check job completion conditions
EDRP_fnc_checkJobCompletion = {
	if (!EDRP_job_active) exitWith {};
	
	// Auto-complete at 100% progress
	if (EDRP_job_progress >= 100) then {
		["completed"] call EDRP_fnc_endJob;
	};
};

// Request job data from server
EDRP_fnc_requestJobData = {
	[getPlayerUID player] remoteExec ["EDRP_fnc_loadJobData", 2];
};

// Save job data to server
EDRP_fnc_saveJobData = {
	[
		getPlayerUID player,
		EDRP_job_skills,
		EDRP_job_xp,
		EDRP_job_stats
	] remoteExec ["EDRP_fnc_saveJobDataServer", 2];
};

// Initialize job system on client
if (hasInterface) then {
	[] call EDRP_fnc_initJobSystem;
};
