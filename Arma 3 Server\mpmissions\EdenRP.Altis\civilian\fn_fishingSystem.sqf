/*
	EdenRP Altis Life - Fishing System
	Author: EdenRP Development Team
	Description: Handles fishing activities and boat-based jobs
	Version: 1.0.0
*/

// Initialize fishing system
EDRP_fnc_initFishingSystem = {
	// Fishing state variables
	EDRP_fishing_active = false;
	EDRP_fishing_location = "";
	EDRP_fishing_boat = objNull;
	EDRP_fishing_net_deployed = false;
	
	// Fishing statistics
	EDRP_fishing_stats = createHashMapFromArray [
		["fish_caught", 0],
		["turtles_caught", 0],
		["treasure_found", 0],
		["nets_deployed", 0],
		["boat_trips", 0]
	];
	
	["Fishing system initialized"] call EDRP_fnc_logInfo;
};

// Start fishing
EDRP_fnc_startFishing = {
	params [["_fishingType", "rod", [""]]];
	
	if (EDRP_fishing_active) exitWith {
		["You are already fishing"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player has fishing rod
	if !(["fishingrod"] call EDRP_fnc_hasItem) exitWith {
		["You need a fishing rod to fish"] call EDRP_fnc_hint;
		false
	};
	
	// Check if near water
	if !([player, 5] call EDRP_fnc_isNearWater) exitWith {
		["You need to be near water to fish"] call EDRP_fnc_hint;
		false
	};
	
	// Check fishing zones
	private _nearbyZones = [] call EDRP_fnc_getNearbyFishingZones;
	if (count _nearbyZones == 0) exitWith {
		["No fish in this area"] call EDRP_fnc_hint;
		false
	};
	
	private _zone = (_nearbyZones select 0) select 0;
	
	EDRP_fishing_active = true;
	EDRP_fishing_location = _zone;
	
	[_fishingType, _zone] spawn EDRP_fnc_fishingLoop;
	
	true
};

// Fishing loop
EDRP_fnc_fishingLoop = {
	params ["_fishingType", "_zone"];
	
	while {EDRP_fishing_active} do {
		// Check if still near water
		if !([player, 5] call EDRP_fnc_isNearWater) exitWith {
			["You moved away from the water"] call EDRP_fnc_hint;
			EDRP_fishing_active = false;
		};
		
		// Fishing attempt
		[
			8 + (random 4), // 8-12 seconds
			"Fishing...",
			{
				params ["_zone"];
				
				// Determine catch based on zone and skill
				private _fishingLevel = EDRP_job_skills get "fishing";
				private _catchChance = 60 + (_fishingLevel * 5); // Base 60% + 5% per level
				
				if (random 100 < _catchChance) then {
					// Successful catch
					private _catch = [_zone, _fishingLevel] call EDRP_fnc_determineFishCatch;
					_catch params ["_item", "_quantity", "_name"];
					
					if ([_item, _quantity] call EDRP_fnc_addItem) then {
						[format ["Caught %1x %2!", _quantity, _name], "success"] call EDRP_fnc_hint;
						
						// Add XP
						private _xp = switch (_item) do {
							case "fishraw": { 12 };
							case "turtleraw": { 30 };
							case "treasure": { 100 };
							default { 10 };
						};
						
						["fishing", _xp] call EDRP_fnc_addJobXP;
						
						// Update statistics
						switch (_item) do {
							case "fishraw": {
								EDRP_fishing_stats set ["fish_caught", (EDRP_fishing_stats get "fish_caught") + _quantity];
							};
							case "turtleraw": {
								EDRP_fishing_stats set ["turtles_caught", (EDRP_fishing_stats get "turtles_caught") + _quantity];
							};
							case "treasure": {
								EDRP_fishing_stats set ["treasure_found", (EDRP_fishing_stats get "treasure_found") + _quantity];
							};
						};
						
						playSound "fishing_splash";
					} else {
						["Inventory full - fish got away"] call EDRP_fnc_hint;
					};
				} else {
					// No catch
					["Nothing biting..."] call EDRP_fnc_hint;
				};
				
				true
			},
			{
				["Fishing cancelled"] call EDRP_fnc_hint;
				false
			},
			[_zone]
		] call EDRP_fnc_progressBar;
		
		// Play fishing animation
		player playAction "AinvPercMstpSnonWnonDnon";
		
		sleep 2;
	};
};

// Determine fish catch
EDRP_fnc_determineFishCatch = {
	params [
		["_zone", "", [""]],
		["_fishingLevel", 0, [0]]
	];
	
	private _catches = [
		["fishraw", 1, "Fish", 70],
		["fishraw", 2, "Fish", 20],
		["turtleraw", 1, "Turtle", 5],
		["treasure", 1, "Treasure", 1]
	];
	
	// Adjust probabilities based on zone
	if (_zone find "turtle" >= 0) then {
		// Turtle zone - higher turtle chance
		_catches = [
			["fishraw", 1, "Fish", 40],
			["turtleraw", 1, "Turtle", 50],
			["treasure", 1, "Treasure", 2]
		];
	};
	
	// Skill affects treasure chance
	if (_fishingLevel >= 5) then {
		(_catches select 2) set [3, 3]; // Increase treasure chance
	};
	
	// Weighted random selection
	private _totalWeight = 0;
	{
		_totalWeight = _totalWeight + (_x select 3);
	} forEach _catches;
	
	private _randomValue = random _totalWeight;
	private _currentWeight = 0;
	
	private _selectedCatch = _catches select 0;
	{
		_currentWeight = _currentWeight + (_x select 3);
		if (_randomValue <= _currentWeight) exitWith {
			_selectedCatch = _x;
		};
	} forEach _catches;
	
	[_selectedCatch select 0, _selectedCatch select 1, _selectedCatch select 2]
};

// Deploy fishing net (boat fishing)
EDRP_fnc_deployFishingNet = {
	if (EDRP_fishing_net_deployed) exitWith {
		["You already have a net deployed"] call EDRP_fnc_hint;
		false
	};
	
	// Check if in boat
	if !(vehicle player isKindOf "Ship") exitWith {
		["You need to be in a boat to deploy nets"] call EDRP_fnc_hint;
		false
	};
	
	// Check if in deep water
	if (getPosASL player select 2 > -10) exitWith {
		["You need to be in deeper water to deploy nets"] call EDRP_fnc_hint;
		false
	};
	
	// Check if has fishing net
	if !(["fishingnet"] call EDRP_fnc_hasItem) exitWith {
		["You need a fishing net"] call EDRP_fnc_hint;
		false
	};
	
	EDRP_fishing_net_deployed = true;
	EDRP_fishing_boat = vehicle player;
	
	// Remove net from inventory
	["fishingnet", 1] call EDRP_fnc_removeItem;
	
	["Fishing net deployed! Wait 5 minutes before retrieving", "info"] call EDRP_fnc_hint;
	
	// Schedule net retrieval
	[300] spawn {
		sleep (_this select 0);
		if (EDRP_fishing_net_deployed) then {
			["Your fishing net is ready to retrieve"] call EDRP_fnc_hint;
		};
	};
	
	true
};

// Retrieve fishing net
EDRP_fnc_retrieveFishingNet = {
	if (!EDRP_fishing_net_deployed) exitWith {
		["You don't have a net deployed"] call EDRP_fnc_hint;
		false
	};
	
	// Check if in same boat
	if (vehicle player != EDRP_fishing_boat) exitWith {
		["You need to be in the same boat where you deployed the net"] call EDRP_fnc_hint;
		false
	};
	
	EDRP_fishing_net_deployed = false;
	EDRP_fishing_boat = objNull;
	
	// Generate net catch
	private _fishingLevel = EDRP_job_skills get "fishing";
	private _netCatch = [_fishingLevel] call EDRP_fnc_generateNetCatch;
	
	private _totalValue = 0;
	{
		_x params ["_item", "_quantity", "_name"];
		if ([_item, _quantity] call EDRP_fnc_addItem) then {
			[format ["Net caught %1x %2", _quantity, _name], "success"] call EDRP_fnc_hint;
			_totalValue = _totalValue + _quantity;
		};
	} forEach _netCatch;
	
	// Add XP for net fishing
	["fishing", _totalValue * 5] call EDRP_fnc_addJobXP;
	
	// Update statistics
	EDRP_fishing_stats set ["nets_deployed", (EDRP_fishing_stats get "nets_deployed") + 1];
	
	// Return net (chance of damage)
	if (random 100 > 20) then { // 80% chance to keep net
		["fishingnet", 1] call EDRP_fnc_addItem;
	} else {
		["Your fishing net was damaged and lost"] call EDRP_fnc_hint;
	};
	
	playSound "fishing_splash";
	
	true
};

// Generate net catch
EDRP_fnc_generateNetCatch = {
	params [["_fishingLevel", 0, [0]]];
	
	private _baseCatch = 3 + floor(random 5); // 3-7 fish base
	private _skillBonus = floor(_fishingLevel / 2); // +1 fish per 2 levels
	private _totalFish = _baseCatch + _skillBonus;
	
	private _netCatch = [["fishraw", _totalFish, "Fish"]];
	
	// Chance for bonus items
	if (random 100 < 30) then {
		_netCatch pushBack ["turtleraw", 1, "Turtle"];
	};
	
	if (random 100 < 10 && _fishingLevel >= 3) then {
		_netCatch pushBack ["treasure", 1, "Treasure"];
	};
	
	_netCatch
};

// Check if near water
EDRP_fnc_isNearWater = {
	params [
		["_unit", player, [objNull]],
		["_distance", 5, [0]]
	];
	
	private _pos = getPos _unit;
	private _nearWater = false;
	
	for "_i" from 0 to 360 step 45 do {
		private _checkPos = _pos getPos [_distance, _i];
		if (surfaceIsWater _checkPos) exitWith {
			_nearWater = true;
		};
	};
	
	_nearWater
};

// Get nearby fishing zones
EDRP_fnc_getNearbyFishingZones = {
	private _nearbyZones = [];
	private _playerPos = getPos player;
	
	private _fishingZones = [
		"fishing_1", "fishing_2", "fishing_3",
		"turtle_1", "deep_sea_1", "deep_sea_2"
	];
	
	{
		private _markerPos = getMarkerPos _x;
		if (_playerPos distance2D _markerPos <= 100) then {
			_nearbyZones pushBack [_x, _playerPos distance2D _markerPos];
		};
	} forEach _fishingZones;
	
	// Sort by distance
	_nearbyZones sort true;
	
	_nearbyZones
};

// Stop fishing
EDRP_fnc_stopFishing = {
	EDRP_fishing_active = false;
	EDRP_fishing_location = "";
	
	["Stopped fishing"] call EDRP_fnc_hint;
};

// Initialize fishing system on client
if (hasInterface) then {
	[] call EDRP_fnc_initFishingSystem;
};
