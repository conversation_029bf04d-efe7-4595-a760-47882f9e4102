/*
	EdenRP Altis Life - Icons Configuration
	Author: EdenRP Development Team
	Description: Configuration for all UI icons and images
	Version: 1.0.0
*/

class CfgIcons {
	// Item Icons
	class Items {
		class apple {
			texture = "images\icons\items\apple.paa";
			size = 64;
		};
		
		class peach {
			texture = "images\icons\items\peach.paa";
			size = 64;
		};
		
		class water {
			texture = "images\icons\items\water.paa";
			size = 64;
		};
		
		class bread {
			texture = "images\icons\items\bread.paa";
			size = 64;
		};
		
		class copperore {
			texture = "images\icons\items\copper_ore.paa";
			size = 64;
		};
		
		class copperingot {
			texture = "images\icons\items\copper_ingot.paa";
			size = 64;
		};
		
		class ironore {
			texture = "images\icons\items\iron_ore.paa";
			size = 64;
		};
		
		class ironingot {
			texture = "images\icons\items\iron_ingot.paa";
			size = 64;
		};
		
		class salt {
			texture = "images\icons\items\salt.paa";
			size = 64;
		};
		
		class saltrefined {
			texture = "images\icons\items\salt_refined.paa";
			size = 64;
		};
		
		class diamond {
			texture = "images\icons\items\diamond.paa";
			size = 64;
		};
		
		class diamondcut {
			texture = "images\icons\items\diamond_cut.paa";
			size = 64;
		};
		
		class goldore {
			texture = "images\icons\items\gold_ore.paa";
			size = 64;
		};
		
		class goldbar {
			texture = "images\icons\items\gold_bar.paa";
			size = 64;
		};
		
		class oilunprocessed {
			texture = "images\icons\items\oil_unprocessed.paa";
			size = 64;
		};
		
		class oilprocessed {
			texture = "images\icons\items\oil_processed.paa";
			size = 64;
		};
		
		class heroinunprocessed {
			texture = "images\icons\items\heroin_unprocessed.paa";
			size = 64;
		};
		
		class heroinprocessed {
			texture = "images\icons\items\heroin_processed.paa";
			size = 64;
		};
		
		class cocaineunprocessed {
			texture = "images\icons\items\cocaine_unprocessed.paa";
			size = 64;
		};
		
		class cocaineprocessed {
			texture = "images\icons\items\cocaine_processed.paa";
			size = 64;
		};
		
		class marijuanaunprocessed {
			texture = "images\icons\items\marijuana_unprocessed.paa";
			size = 64;
		};
		
		class marijuanaprocessed {
			texture = "images\icons\items\marijuana_processed.paa";
			size = 64;
		};
		
		class fishraw {
			texture = "images\icons\items\fish_raw.paa";
			size = 64;
		};
		
		class fishcooked {
			texture = "images\icons\items\fish_cooked.paa";
			size = 64;
		};
		
		class turtleraw {
			texture = "images\icons\items\turtle_raw.paa";
			size = 64;
		};
		
		class turtlecooked {
			texture = "images\icons\items\turtle_cooked.paa";
			size = 64;
		};
		
		class pickaxe {
			texture = "images\icons\tools\pickaxe.paa";
			size = 64;
		};
		
		class fishingrod {
			texture = "images\icons\tools\fishing_rod.paa";
			size = 64;
		};
		
		class huntingknife {
			texture = "images\icons\tools\hunting_knife.paa";
			size = 64;
		};
		
		class chemkit {
			texture = "images\icons\tools\chemistry_kit.paa";
			size = 64;
		};
		
		class oilpump {
			texture = "images\icons\tools\oil_pump.paa";
			size = 64;
		};
	};
	
	// Vehicle Icons
	class Vehicles {
		class car {
			texture = "images\icons\vehicles\car.paa";
			size = 128;
		};
		
		class truck {
			texture = "images\icons\vehicles\truck.paa";
			size = 128;
		};
		
		class helicopter {
			texture = "images\icons\vehicles\helicopter.paa";
			size = 128;
		};
		
		class plane {
			texture = "images\icons\vehicles\plane.paa";
			size = 128;
		};
		
		class boat {
			texture = "images\icons\vehicles\boat.paa";
			size = 128;
		};
		
		class motorcycle {
			texture = "images\icons\vehicles\motorcycle.paa";
			size = 128;
		};
	};
	
	// Faction Icons
	class Factions {
		class police {
			texture = "images\icons\factions\police.paa";
			size = 128;
		};
		
		class medical {
			texture = "images\icons\factions\medical.paa";
			size = 128;
		};
		
		class civilian {
			texture = "images\icons\factions\civilian.paa";
			size = 128;
		};
		
		class gang {
			texture = "images\icons\factions\gang.paa";
			size = 128;
		};
	};
	
	// UI Icons
	class UI {
		class inventory {
			texture = "images\icons\ui\inventory.paa";
			size = 64;
		};
		
		class phone {
			texture = "images\icons\ui\phone.paa";
			size = 64;
		};
		
		class map {
			texture = "images\icons\ui\map.paa";
			size = 64;
		};
		
		class settings {
			texture = "images\icons\ui\settings.paa";
			size = 64;
		};
		
		class admin {
			texture = "images\icons\ui\admin.paa";
			size = 64;
		};
		
		class gang {
			texture = "images\icons\ui\gang.paa";
			size = 64;
		};
		
		class house {
			texture = "images\icons\ui\house.paa";
			size = 64;
		};
		
		class vehicle {
			texture = "images\icons\ui\vehicle.paa";
			size = 64;
		};
		
		class bank {
			texture = "images\icons\ui\bank.paa";
			size = 64;
		};
		
		class shop {
			texture = "images\icons\ui\shop.paa";
			size = 64;
		};
		
		class license {
			texture = "images\icons\ui\license.paa";
			size = 64;
		};
		
		class wanted {
			texture = "images\icons\ui\wanted.paa";
			size = 64;
		};
		
		class arrest {
			texture = "images\icons\ui\arrest.paa";
			size = 64;
		};
		
		class revive {
			texture = "images\icons\ui\revive.paa";
			size = 64;
		};
		
		class repair {
			texture = "images\icons\ui\repair.paa";
			size = 64;
		};
		
		class refuel {
			texture = "images\icons\ui\refuel.paa";
			size = 64;
		};
	};
	
	// Status Icons
	class Status {
		class health {
			texture = "images\icons\status\health.paa";
			size = 32;
		};
		
		class hunger {
			texture = "images\icons\status\hunger.paa";
			size = 32;
		};
		
		class thirst {
			texture = "images\icons\status\thirst.paa";
			size = 32;
		};
		
		class fatigue {
			texture = "images\icons\status\fatigue.paa";
			size = 32;
		};
		
		class wanted {
			texture = "images\icons\status\wanted.paa";
			size = 32;
		};
		
		class restrained {
			texture = "images\icons\status\restrained.paa";
			size = 32;
		};
		
		class unconscious {
			texture = "images\icons\status\unconscious.paa";
			size = 32;
		};
		
		class bleeding {
			texture = "images\icons\status\bleeding.paa";
			size = 32;
		};
	};
	
	// Map Icons
	class Map {
		class police_station {
			texture = "images\icons\map\police_station.paa";
			size = 32;
		};
		
		class hospital {
			texture = "images\icons\map\hospital.paa";
			size = 32;
		};
		
		class garage {
			texture = "images\icons\map\garage.paa";
			size = 32;
		};
		
		class shop {
			texture = "images\icons\map\shop.paa";
			size = 32;
		};
		
		class bank {
			texture = "images\icons\map\bank.paa";
			size = 32;
		};
		
		class atm {
			texture = "images\icons\map\atm.paa";
			size = 32;
		};
		
		class gas_station {
			texture = "images\icons\map\gas_station.paa";
			size = 32;
		};
		
		class processor {
			texture = "images\icons\map\processor.paa";
			size = 32;
		};
		
		class gathering {
			texture = "images\icons\map\gathering.paa";
			size = 32;
		};
		
		class illegal {
			texture = "images\icons\map\illegal.paa";
			size = 32;
		};
		
		class gang_base {
			texture = "images\icons\map\gang_base.paa";
			size = 32;
		};
		
		class house {
			texture = "images\icons\map\house.paa";
			size = 32;
		};
	};
};
