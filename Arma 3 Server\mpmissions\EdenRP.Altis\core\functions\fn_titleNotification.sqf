// File: fn_titleNotification.sqf
// Author: Solomon
// Description: Fills arrays of currently unlocked titles when player joins, checks if new titles have been unlocked. Should be called ANY time a stat for titles is changed
// Currently called in: fn_onPlayerKilled, init, fn_statArrUp, fn_revivePlayer, and fn_handleGoKartRace

// Protection against multiple triggers happening for titles
if (eden_titleCoolDown) then {
  uiSleep 6;
};

// Filling array with all title names and descriptions
if ((count eden_allTitles) isEqualTo 0) then {
  for "_i" from 0 to ((count (missionConfigFile >> "CfgTitleCiv"))-1) do {
    eden_allTitles pushBack [getText(((missionConfigFile >> "CfgTitleCiv") select _i) >> "title"), getText(((missionConfigFile >> "CfgTitleCiv") select _i) >> "desc")];
  };
  for "_i" from 0 to ((count (missionConfigFile >> "CfgTitleCop"))-1) do {
    eden_allTitles pushBack [getText(((missionConfigFile >> "CfgTitleCop") select _i) >> "title"), getText(((missionConfigFile >> "CfgTitleCop") select _i) >> "desc")];
  };
  for "_i" from 0 to ((count (missionConfigFile >> "CfgTitleMedic"))-1) do {
    eden_allTitles pushBack [getText(((missionConfigFile >> "CfgTitleMedic") select _i) >> "title"), getText(((missionConfigFile >> "CfgTitleMedic") select _i) >> "desc")];
  };
  for "_i" from 0 to ((count (missionConfigFile >> "CfgTitleSpecial"))-1) do {
    eden_allTitles pushBack [getText(((missionConfigFile >> "CfgTitleSpecial") select _i) >> "title"), getText(((missionConfigFile >> "CfgTitleSpecial") select _i) >> "desc")];
  };
  for "_i" from 0 to ((count (missionConfigFile >> "CfgTitleServerBest"))-1) do {
    eden_allTitles pushBack [getText(((missionConfigFile >> "CfgTitleServerBest") select _i) >> "title"), getText(((missionConfigFile >> "CfgTitleServerBest") select _i) >> "desc")];
  };
};

// Filling up array of currently unlocked stats
private _notEmpty = false;
for "_i" from 0 to ((count  eden_allTitles) - 1) do {
  private _titleProgress = [(eden_allTitles select _i) select 0] call EDEN_fnc_titleCheck;
  if (_titleProgress select 0) then {
    if ((eden_unlockedTitles select 0) isEqualTo "n" || (_notEmpty && !((eden_unlockedTitles select 0) isEqualTo "n") )) then {    // Fills array within initial unlocked titles when user joins
      if (!_notEmpty) then {
        _notEmpty = true;
        eden_unlockedTitles set [0, (eden_allTitles select _i) select 0];
      } else {
          eden_unlockedTitles pushBack ((eden_allTitles select _i) select 0);
      };
    } else {                                                                                                     // Notifies user of new titles
        if ((eden_unlockedTitles find ((eden_allTitles select _i) select 0)) isEqualTo -1) then {
          titleText[format["<t underline='1' size='3'>%1</t><br/><br/><t underline='0' size='1'>%2</t>", ((eden_allTitles select _i) select 0) splitString " " joinString toString [160],((eden_allTitles select _i) select 1)],"PLAIN", -1, true, true];
          titleFadeOut 5;
          eden_titleCoolDown = true;
          uiSleep 6;
          eden_titleCoolDown = false;
          eden_unlockedTitles pushBack (( eden_allTitles select _i) select 0);
        };
    };
  };
};
