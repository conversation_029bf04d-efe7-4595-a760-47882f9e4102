/*
	EdenRP Altis Life - Revive System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Handles player death, revival, and medical emergency response
	Version: 1.0.0
*/

// Player death handler
EDRP_fnc_onPlayerKilled = {
	params [
		["_unit", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
		["_killer", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
		["_instigator", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
		["_useEffects", true, [true]]
	];
	
	if (isNull _unit || _unit != player) exitWith {};
	
	// Set death variables
	EDRP_player_dead = true;
	EDRP_death_time = time;
	EDRP_death_position = getPos player;
	EDRP_death_requested_medic = false;
	
	// Create corpse
	EDRP_player_corpse = "Land_Human_skull_EP1" createVehicleLocal (getPos player);
	EDRP_player_corpse setPos (getPos player);
	EDRP_player_corpse setVariable ["realname", name player, true];
	EDRP_player_corpse setVariable ["Revive", player, true];
	EDRP_player_corpse setVariable ["hasRequested", 0, true];
	
	// Hide player body
	player setPos [0, 0, 0];
	player enableSimulation false;
	
	// Start death screen
	[] call EDRP_fnc_deathScreen;
	
	// Log death
	[format ["DEATH: %1 was killed by %2", name player, if (isNull _killer) then {"unknown"} else {name _killer}]] call EDRP_fnc_logInfo;
};

// Death screen
EDRP_fnc_deathScreen = {
	// Create death camera
	EDRP_death_camera = "camera" camCreate (getPos EDRP_player_corpse);
	EDRP_death_camera cameraEffect ["internal", "back"];
	EDRP_death_camera camSetTarget EDRP_player_corpse;
	EDRP_death_camera camSetRelPos [0, -3, 1.5];
	EDRP_death_camera camCommit 0;
	
	// Create death dialog
	createDialog "EDRP_DeathDialog";
	
	// Start death timer
	[] spawn EDRP_fnc_deathTimer;
	
	// Update death screen
	[] spawn EDRP_fnc_updateDeathScreen;
};

// Death timer
EDRP_fnc_deathTimer = {
	private _respawnTime = 300; // 5 minutes
	private _endTime = EDRP_death_time + _respawnTime;
	
	while {EDRP_player_dead && time < _endTime} do {
		private _remaining = ceil(_endTime - time);
		
		// Update timer display
		private _display = findDisplay 7300;
		if (!isNull _display) then {
			private _timerCtrl = _display displayCtrl 7301;
			_timerCtrl ctrlSetText format ["Respawn Available In: %1", [_remaining] call EDRP_fnc_formatTime];
		};
		
		sleep 1;
	};
	
	// Enable respawn button
	private _display = findDisplay 7300;
	if (!isNull _display) then {
		private _respawnBtn = _display displayCtrl 7302;
		_respawnBtn ctrlEnable true;
		_respawnBtn ctrlSetText "RESPAWN";
	};
};

// Update death screen
EDRP_fnc_updateDeathScreen = {
	while {EDRP_player_dead} do {
		private _display = findDisplay 7300;
		if (!isNull _display) then {
			// Update medic request status
			private _medicStatus = _display displayCtrl 7304;
			if (EDRP_death_requested_medic) then {
				_medicStatus ctrlSetText "Medic Requested - Waiting for Response";
			} else {
				_medicStatus ctrlSetText "No Medic Requested";
			};
			
			// Check for nearby medics
			private _nearbyMedics = [];
			{
				if (side _x == independent && _x distance EDRP_player_corpse < 100) then {
					_nearbyMedics pushBack _x;
				};
			} forEach allPlayers;
			
			if (count _nearbyMedics > 0) then {
				private _medicList = _display displayCtrl 7305;
				_medicList ctrlSetText format ["Medics Nearby: %1", count _nearbyMedics];
			};
		};
		
		sleep 2;
	};
};

// Request medic
EDRP_fnc_requestMedic = {
	if (EDRP_death_requested_medic) exitWith {
		["You have already requested a medic"] call EDRP_fnc_hint;
		false
	};
	
	// Check if medics are online
	private _medicsOnline = {side _x == independent && alive _x} count allPlayers > 0;
	
	if (!_medicsOnline) exitWith {
		["No medics are currently online"] call EDRP_fnc_hint;
		false
	};
	
	// Send request to medics
	EDRP_death_requested_medic = true;
	EDRP_player_corpse setVariable ["hasRequested", time, true];
	
	// Broadcast to medics
	private _message = format ["%1 is requesting medical assistance at %2", name player, mapGridPosition EDRP_death_position];
	[_message, EDRP_player_corpse] remoteExec ["EDRP_fnc_medicRequest", independent];
	
	["Medic request sent"] call EDRP_fnc_hint;
	
	true
};

// Medic request (received by medics)
EDRP_fnc_medicRequest = {
	params [
		["_message", "", [""]],
		["_corpse", objNull, [objNull]]
	];
	
	if (!EDRP_medical_active) exitWith {};
	
	// Show request notification
	[_message, "Medical Request", "Respond", "Ignore"] spawn {
		params ["_message", "_title", "_button1", "_button2"];
		
		private _result = [_message, _title, _button1, _button2] call EDRP_fnc_messageBox;
		
		if (_result) then {
			// Medic accepted - create waypoint
			private _marker = createMarkerLocal [format ["medical_request_%1", random 1000], getPos _corpse];
			_marker setMarkerTypeLocal "hd_medical";
			_marker setMarkerColorLocal "ColorRed";
			_marker setMarkerTextLocal "Medical Emergency";
			
			["Responding to medical emergency"] call EDRP_fnc_hint;
		};
	};
};

// Revive player
EDRP_fnc_revivePlayer = {
	params [
		["_target", objNull, [objNull]],
		["_medic", player, [objNull]],
		["_isAdminRevive", false, [true]]
	];
	
	if (isNull _target) exitWith {
		["Invalid revive target"] call EDRP_fnc_hint;
		false
	};
	
	// Check if medic is on duty
	if (!_isAdminRevive && !EDRP_medical_active) exitWith {
		["You must be on duty to revive players"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is dead
	if (!(_target getVariable ["isDead", false])) exitWith {
		["Target is not dead"] call EDRP_fnc_hint;
		false
	};
	
	// Get corpse object
	private _corpse = _target getVariable ["corpse", objNull];
	if (isNull _corpse) exitWith {
		["Cannot find target's corpse"] call EDRP_fnc_hint;
		false
	};
	
	// Check distance
	if (player distance _corpse > 5) exitWith {
		["You must be closer to the patient"] call EDRP_fnc_hint;
		false
	};
	
	// Start revive process
	[
		10, // 10 seconds
		"Reviving Patient...",
		{
			params ["_target", "_medic", "_corpse", "_isAdminRevive"];
			
			// Calculate revive cost
			private _reviveCost = if (_isAdminRevive) then { 0 } else { 5000 };
			
			// Revive player
			[_medic, _reviveCost, _isAdminRevive] remoteExec ["EDRP_fnc_revived", _target];
			
			// Pay medic (if not admin revive)
			if (!_isAdminRevive) then {
				private _payout = _reviveCost * 0.8; // 80% of cost goes to medic
				EDRP_player_cash = EDRP_player_cash + _payout;
				
				// Update statistics
				EDRP_medical_stats set ["patients_revived", (EDRP_medical_stats get "patients_revived") + 1];
				
				[format ["Revived %1 - Payment: $%2", name _target, [_payout] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
			} else {
				[format ["Admin revived %1", name _target]] call EDRP_fnc_hint;
			};
			
			// Clean up corpse
			deleteVehicle _corpse;
			
			true
		},
		{
			["Revive cancelled"] call EDRP_fnc_hint;
			false
		},
		[_target, _medic, _corpse, _isAdminRevive]
	] call EDRP_fnc_progressBar;
	
	true
};

// Player revived (client-side)
EDRP_fnc_revived = {
	params [
		["_medic", "Unknown Medic", [""]],
		["_cost", 5000, [0]],
		["_isAdminRevive", false, [true]]
	];
	
	if (!EDRP_player_dead) exitWith {};
	
	// Clear death variables
	EDRP_player_dead = false;
	EDRP_death_time = 0;
	EDRP_death_position = [];
	EDRP_death_requested_medic = false;
	
	// Close death dialog
	closeDialog 0;
	
	// Destroy death camera
	EDRP_death_camera cameraEffect ["terminate", "back"];
	camDestroy EDRP_death_camera;
	
	// Restore player
	player enableSimulation true;
	player setPos (getPos EDRP_player_corpse);
	player setDamage 0.25; // Leave player slightly injured
	
	// Pay for revival
	if (!_isAdminRevive) then {
		if (EDRP_player_bank >= _cost) then {
			EDRP_player_bank = EDRP_player_bank - _cost;
		} else {
			EDRP_player_bank = 0;
		};
		
		[format ["%1 has revived you for $%2", _medic, [_cost] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
	} else {
		[format ["%1 has revived you", _medic]] call EDRP_fnc_hint;
	};
	
	// Clean up corpse
	if (!isNull EDRP_player_corpse) then {
		deleteVehicle EDRP_player_corpse;
		EDRP_player_corpse = objNull;
	};
	
	// Restore HUD
	[] call EDRP_fnc_updateHUD;
	
	// Log revival
	[format ["REVIVE: %1 was revived by %2 for $%3", name player, _medic, _cost]] call EDRP_fnc_logInfo;
};

// Respawn player
EDRP_fnc_respawnPlayer = {
	if (!EDRP_player_dead) exitWith {};
	
	// Clear death variables
	EDRP_player_dead = false;
	EDRP_death_time = 0;
	EDRP_death_position = [];
	EDRP_death_requested_medic = false;
	
	// Close death dialog
	closeDialog 0;
	
	// Destroy death camera
	EDRP_death_camera cameraEffect ["terminate", "back"];
	camDestroy EDRP_death_camera;
	
	// Clean up corpse
	if (!isNull EDRP_player_corpse) then {
		deleteVehicle EDRP_player_corpse;
		EDRP_player_corpse = objNull;
	};
	
	// Respawn player
	player enableSimulation true;
	
	// Teleport to hospital
	private _hospitalPos = [3540.26, 13724.2, 0.********]; // Kavala Hospital
	player setPos _hospitalPos;
	player setDamage 0;
	
	// Apply respawn penalties
	EDRP_player_bank = EDRP_player_bank * 0.9; // Lose 10% of bank money
	
	// Clear inventory (keep licenses)
	[] call EDRP_fnc_clearInventory;
	
	// Load civilian gear
	[] call EDRP_fnc_loadCivilianGear;
	
	// Show respawn message
	["You have respawned at the hospital", "info"] call EDRP_fnc_hint;
	
	// Restore HUD
	[] call EDRP_fnc_updateHUD;
	
	// Log respawn
	[format ["RESPAWN: %1 respawned at hospital", name player]] call EDRP_fnc_logInfo;
};

// Treat player (heal without revival)
EDRP_fnc_treatPlayer = {
	params [
		["_target", objNull, [objNull]],
		["_medic", player, [objNull]]
	];
	
	if (isNull _target || _target == _medic) exitWith {
		["Invalid treatment target"] call EDRP_fnc_hint;
		false
	};
	
	// Check if medic is on duty
	if (!EDRP_medical_active) exitWith {
		["You must be on duty to treat patients"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is alive
	if (!alive _target) exitWith {
		["Target needs revival, not treatment"] call EDRP_fnc_hint;
		false
	};
	
	// Start treatment process
	[
		5, // 5 seconds
		"Treating Patient...",
		{
			params ["_target", "_medic"];
			
			// Heal target
			_target setDamage 0;
			
			// Calculate treatment cost
			private _treatmentCost = 1000;
			
			// Send bill to target
			[_medic, _treatmentCost] remoteExec ["EDRP_fnc_receiveMedicalBill", _target];
			
			// Update statistics
			EDRP_medical_stats set ["patients_treated", (EDRP_medical_stats get "patients_treated") + 1];
			
			[format ["Treated %1", name _target], "success"] call EDRP_fnc_hint;
			
			true
		},
		{
			["Treatment cancelled"] call EDRP_fnc_hint;
			false
		},
		[_target, _medic]
	] call EDRP_fnc_progressBar;
	
	true
};

// Initialize revive system
if (hasInterface) then {
	// Set up death event handler
	player addEventHandler ["Killed", EDRP_fnc_onPlayerKilled];
	
	// Initialize death variables
	EDRP_player_dead = false;
	EDRP_death_time = 0;
	EDRP_death_position = [];
	EDRP_death_requested_medic = false;
	EDRP_player_corpse = objNull;
	EDRP_death_camera = objNull;
};
