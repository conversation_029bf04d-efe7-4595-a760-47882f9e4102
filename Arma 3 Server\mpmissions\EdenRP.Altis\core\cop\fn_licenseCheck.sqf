//  File: fn_licenseCheck.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description: Returns the licenses to the cop.
private["_cop"];
_cop = param [0,<PERSON>bj<PERSON><PERSON>,[Obj<PERSON><PERSON>]];
if(isNull _cop) exitWith {}; //Bad entry

_licenses = "";
_tier = "";
_mode = 0;
//Licenses
{
	if(missionNamespace getVariable (_x select 0) && _x select 1 == "civ") then
	{
		_licenses = _licenses + ([_x select 0] call EDEN_fnc_varToStr) + "<br/>";
	};
} foreach eden_licenses;

switch(true) do {
	case ((player getVariable ["isVigi",false]) && eden_vigiarrests < 25): {_tier = "Tier 1"; _mode = 1;};
	case ((player getVariable ["isVigi",false]) && (eden_vigiarrests >= 25 && eden_vigiarrests < 50)): {_tier = "Tier 2"; _mode = 1;};
	case ((player getVariable ["isVigi",false]) && (eden_vigiarrests >= 50 && eden_vigiarrests < 100)): {_tier = "Tier 3"; _mode = 1;};
	case ((player getVariable ["isVigi",false]) && (eden_vigiarrests >= 100 && eden_vigiarrests < 200)): {_tier = "Tier 4"; _mode = 1;};
	case ((player getVariable ["isVigi",false]) && eden_vigiarrests >= 200): {_tier = "Tier 5"; _mode = 1;};
	default {_tier = ""; _mode = 0;};
};

if(_licenses == "") then {_licenses = (localize "STR_Cop_NoLicensesFound");};

[[profileName,_licenses,_tier,_mode],"EDEN_fnc_licensesRead",_cop,FALSE] spawn EDEN_fnc_MP;

[[2,"STR_NOTF_LicenseSearch",true,[_cop getVariable ["realname",name _cop]]],"EDEN_fnc_broadcast",player,false] spawn EDEN_fnc_MP;