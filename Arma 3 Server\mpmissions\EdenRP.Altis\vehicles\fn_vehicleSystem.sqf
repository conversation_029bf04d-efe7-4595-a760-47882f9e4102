/*
	EdenRP Altis Life - Vehicle and Garage System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Vehicle ownership, garage, and management system
	Version: 1.0.0
*/

// Initialize vehicle system
EDRP_fnc_initVehicleSystem = {
	// Vehicle state variables
	EDRP_owned_vehicles = [];
	EDRP_vehicle_keys = [];
	EDRP_impounded_vehicles = [];
	EDRP_garage_transaction = false;
	
	// Vehicle statistics
	EDRP_vehicle_stats = createHashMapFromArray [
		["vehicles_owned", 0],
		["total_spent", 0],
		["insurance_claims", 0],
		["impounds", 0],
		["repairs", 0]
	];
	
	// Load vehicle configuration
	[] call EDRP_fnc_loadVehicleConfig;
	
	["Vehicle system initialized"] call EDRP_fnc_logInfo;
};

// Load vehicle configuration
EDRP_fnc_loadVehicleConfig = {
	// Vehicle categories and pricing (adapted from Olympus)
	EDRP_vehicle_shop_cars = [
		["B_Quadbike_01_F", "Quad Bike", 2500, "civ"],
		["C_Hatchback_01_F", "Hatchback", 9500, "civ"],
		["C_Hatchback_01_sport_F", "Hatchback Sport", 12000, "civ"],
		["C_Offroad_01_F", "Offroad", 12500, "civ"],
		["C_SUV_01_F", "SUV", 15800, "civ"],
		["C_Van_01_transport_F", "Transport Van", 25000, "civ"],
		["C_Van_01_box_F", "Box Truck", 35000, "civ"],
		["I_Truck_02_transport_F", "Zamak Transport", 65000, "civ"],
		["I_Truck_02_covered_F", "Zamak Covered", 75000, "civ"],
		["B_Truck_01_transport_F", "HEMTT Transport", 125000, "civ"],
		["B_Truck_01_box_F", "HEMTT Box", 150000, "civ"]
	];
	
	EDRP_vehicle_shop_air = [
		["C_Heli_Light_01_civil_F", "M-900 Civil", 245000, "civ"],
		["B_Heli_Light_01_F", "MH-9 Hummingbird", 300000, "civ"],
		["O_Heli_Light_02_unarmed_F", "PO-30 Orca", 750000, "civ"],
		["I_Heli_Transport_02_F", "CH-49 Mohawk", 2750000, "civ"]
	];
	
	EDRP_vehicle_shop_ship = [
		["C_Rubberboat", "Assault Boat", 5000, "civ"],
		["C_Boat_Civil_01_F", "Motor Boat", 22000, "civ"],
		["B_Boat_Transport_01_F", "Assault Boat (Black)", 30000, "civ"],
		["O_Boat_Armed_01_hmg_F", "Speedboat HMG", 75000, "civ"]
	];
	
	EDRP_vehicle_shop_police = [
		["C_Offroad_01_F", "Police Offroad", 5000, "cop"],
		["C_SUV_01_F", "Police SUV", 7500, "cop"],
		["B_MRAP_01_F", "Hunter", 15000, "cop"],
		["I_MRAP_03_F", "Strider", 25000, "cop"],
		["B_Heli_Light_01_F", "Police Hummingbird", 75000, "cop"],
		["B_Heli_Transport_01_F", "Ghost Hawk", 200000, "cop"]
	];
	
	EDRP_vehicle_shop_medical = [
		["C_Offroad_01_F", "Medical Offroad", 3000, "med"],
		["C_SUV_01_F", "Medical SUV", 5000, "med"],
		["C_Van_01_box_F", "Medical Van", 15000, "med"],
		["B_Truck_01_medical_F", "Medical HEMTT", 45000, "med"],
		["C_Heli_Light_01_civil_F", "Medical Helicopter", 50000, "med"]
	];
	
	// Vehicle insurance rates
	EDRP_vehicle_insurance_rate = 0.1; // 10% of vehicle value
	
	// Impound costs
	EDRP_impound_cost_multiplier = 0.05; // 5% of vehicle value
	
	// Garage locations
	EDRP_garage_locations = [
		["Kavala Garage", [3664.73, 13220.1, 0], "civ"],
		["Pyrgos Garage", [16019.5, 16952.9, 0], "civ"],
		["Athira Garage", [14707.1, 16835.5, 0], "civ"],
		["Sofia Garage", [4022.15, 11669.5, 0], "civ"],
		["Police HQ Garage", [3542.87, 13216.3, 0], "cop"],
		["Pyrgos PD Garage", [15876.8, 16972.1, 0], "cop"],
		["Medical Center Garage", [3530.54, 13212.9, 0], "med"]
	];
	
	// Maximum vehicles per player
	EDRP_max_vehicles_per_player = 5;
};

// Get vehicle configuration
EDRP_fnc_getVehicleConfig = {
	params [["_vehicleClass", "", [""]], ["_shopType", "civ", [""]]];
	
	private _shopArray = [];
	switch (_shopType) do {
		case "civ": { _shopArray = EDRP_vehicle_shop_cars + EDRP_vehicle_shop_air + EDRP_vehicle_shop_ship; };
		case "cop": { _shopArray = EDRP_vehicle_shop_police; };
		case "med": { _shopArray = EDRP_vehicle_shop_medical; };
		default { _shopArray = EDRP_vehicle_shop_cars; };
	};
	
	{
		if ((_x select 0) == _vehicleClass) exitWith {
			_x
		};
	} forEach _shopArray;
	
	[]
};

// Open vehicle shop
EDRP_fnc_openVehicleShop = {
	params [["_shopType", "civ", [""]]];
	
	// Check vehicle limit
	if (count EDRP_owned_vehicles >= EDRP_max_vehicles_per_player) exitWith {
		[format ["You can only own %1 vehicles", EDRP_max_vehicles_per_player]] call EDRP_fnc_hint;
		false
	};
	
	// Store shop type
	EDRP_current_vehicle_shop = _shopType;
	
	// Create vehicle shop dialog
	createDialog "EDRP_VehicleShopDialog";
	
	// Update shop display
	[] call EDRP_fnc_updateVehicleShop;
	
	true
};

// Update vehicle shop display
EDRP_fnc_updateVehicleShop = {
	private _display = findDisplay 40000;
	if (isNull _display || isNil "EDRP_current_vehicle_shop") exitWith {};
	
	private _shopType = EDRP_current_vehicle_shop;
	
	// Get shop inventory
	private _shopArray = [];
	switch (_shopType) do {
		case "civ": { _shopArray = EDRP_vehicle_shop_cars; };
		case "air": { _shopArray = EDRP_vehicle_shop_air; };
		case "ship": { _shopArray = EDRP_vehicle_shop_ship; };
		case "cop": { _shopArray = EDRP_vehicle_shop_police; };
		case "med": { _shopArray = EDRP_vehicle_shop_medical; };
	};
	
	// Update vehicle list
	private _vehicleList = _display displayCtrl 40001;
	lbClear _vehicleList;
	
	{
		_x params ["_class", "_name", "_price", "_faction"];
		
		// Check faction access
		private _hasAccess = false;
		switch (_faction) do {
			case "civ": { _hasAccess = true; };
			case "cop": { _hasAccess = (playerSide == west); };
			case "med": { _hasAccess = (playerSide == independent); };
		};
		
		if (_hasAccess) then {
			private _entry = format ["%1 - $%2", _name, [_price] call EDRP_fnc_numberText];
			_vehicleList lbAdd _entry;
			_vehicleList lbSetData [_forEachIndex, _class];
			_vehicleList lbSetValue [_forEachIndex, _price];
		};
	} forEach _shopArray;
	
	// Update player money display
	private _moneyCtrl = _display displayCtrl 40002;
	_moneyCtrl ctrlSetText format["Bank: $%1", [EDRP_player_bank] call EDRP_fnc_numberText];
};

// Buy vehicle
EDRP_fnc_buyVehicle = {
	params [["_vehicleClass", "", [""]], ["_spawnPos", [0,0,0], [[]]]];
	
	if (_vehicleClass == "") exitWith {
		["No vehicle selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check transaction status
	if (EDRP_garage_transaction) exitWith {
		["You currently have an active transaction, please wait"] call EDRP_fnc_hint;
		false
	};
	
	// Get vehicle configuration
	private _vehicleConfig = [_vehicleClass, EDRP_current_vehicle_shop] call EDRP_fnc_getVehicleConfig;
	if (count _vehicleConfig == 0) exitWith {
		["Vehicle not available"] call EDRP_fnc_hint;
		false
	};
	
	_vehicleConfig params ["_class", "_name", "_price", "_faction"];
	
	// Check if player has enough money
	if (EDRP_player_bank < _price) exitWith {
		[format ["You need $%1 in your bank to purchase this vehicle", [_price] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
		false
	};
	
	// Calculate insurance cost
	private _insuranceCost = round(_price * EDRP_vehicle_insurance_rate);
	private _totalCost = _price + _insuranceCost;
	
	// Confirm purchase
	private _message = format [
		"Purchase %1 for $%2?\n\nIncludes:\n- Vehicle: $%3\n- Insurance: $%4",
		_name,
		[_totalCost] call EDRP_fnc_numberText,
		[_price] call EDRP_fnc_numberText,
		[_insuranceCost] call EDRP_fnc_numberText
	];
	
	if ([_message, "Purchase Vehicle", true, true] call EDRP_fnc_messageBox) then {
		// Start transaction
		EDRP_garage_transaction = true;
		
		// Deduct money
		EDRP_player_bank = EDRP_player_bank - _totalCost;
		
		// Send purchase request to server
		[player, getPlayerUID player, _vehicleClass, _spawnPos, _price] remoteExec ["EDRP_fnc_addVehicle", 2];
		
		["Processing vehicle purchase..."] call EDRP_fnc_hint;
		
		true
	} else {
		false
	};
};

// Vehicle ownership response (from server)
EDRP_fnc_vehicleOwnership = {
	params [
		["_vehicle", objNull, [objNull]],
		["_mode", 1, [0]],
		["_uid", "", [""]],
		["_price", 0, [0]]
	];
	
	if (isNull _vehicle) exitWith {};
	
	switch (_mode) do {
		case 1: { // Vehicle purchased successfully
			// Set vehicle variables
			_vehicle setVariable ["vehicle_owner", [getPlayerUID player, name player], true];
			_vehicle setVariable ["locked", true, true];
			_vehicle setVariable ["vehicle_id", round(random 99999), true];
			_vehicle setVariable ["insurance_active", true, true];
			_vehicle setVariable ["fuel_level", 1, true];
			
			// Add to owned vehicles
			EDRP_owned_vehicles pushBack [_vehicle getVariable "vehicle_id", typeOf _vehicle];
			
			// Set vehicle keys
			_vehicle setVariable ["vehicle_keys", [getPlayerUID player], true];
			
			// Update statistics
			EDRP_vehicle_stats set ["vehicles_owned", (EDRP_vehicle_stats get "vehicles_owned") + 1];
			EDRP_vehicle_stats set ["total_spent", (EDRP_vehicle_stats get "total_spent") + _price];
			
			// Show success message
			["Vehicle purchased successfully! Keys added to inventory", "success"] call EDRP_fnc_hint;
			
			// Clear transaction flag
			EDRP_garage_transaction = false;
		};
		
		case 2: { // Vehicle sold successfully
			// Remove vehicle variables
			_vehicle setVariable ["vehicle_owner", nil, true];
			_vehicle setVariable ["locked", false, true];
			_vehicle setVariable ["vehicle_keys", nil, true];
			
			// Remove from owned vehicles
			private _vehicleId = _vehicle getVariable ["vehicle_id", -1];
			{
				if ((_x select 0) == _vehicleId) exitWith {
					EDRP_owned_vehicles deleteAt _forEachIndex;
				};
			} forEach EDRP_owned_vehicles;
			
			// Calculate sale price (60% of original)
			private _salePrice = round(_price * 0.6);
			
			// Add money to player
			EDRP_player_bank = EDRP_player_bank + _salePrice;
			
			// Update statistics
			EDRP_vehicle_stats set ["vehicles_owned", (EDRP_vehicle_stats get "vehicles_owned") - 1];
			
			// Show success message
			[format ["Vehicle sold for $%1", [_salePrice] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
			
			// Delete vehicle
			deleteVehicle _vehicle;
		};
		
		case 3: { // Purchase failed
			["Vehicle purchase failed - please try again"] call EDRP_fnc_hint;
			EDRP_garage_transaction = false;
		};
	};
};

// Open garage
EDRP_fnc_openGarage = {
	params [["_garageType", "civ", [""]]];
	
	// Store garage type
	EDRP_current_garage_type = _garageType;
	
	// Create garage dialog
	createDialog "EDRP_GarageDialog";
	
	// Update garage display
	[] call EDRP_fnc_updateGarage;
	
	true
};

// Update garage display
EDRP_fnc_updateGarage = {
	private _display = findDisplay 41000;
	if (isNull _display || isNil "EDRP_current_garage_type") exitWith {};
	
	// Update vehicle list
	private _vehicleList = _display displayCtrl 41001;
	lbClear _vehicleList;
	
	{
		_x params ["_vehicleId", "_vehicleClass"];
		
		// Get vehicle name
		private _vehicleConfig = [_vehicleClass, EDRP_current_garage_type] call EDRP_fnc_getVehicleConfig;
		private _vehicleName = if (count _vehicleConfig > 0) then { _vehicleConfig select 1 } else { _vehicleClass };
		
		// Check if vehicle is spawned
		private _isSpawned = false;
		{
			if ((_x getVariable ["vehicle_id", -1]) == _vehicleId) exitWith {
				_isSpawned = true;
			};
		} forEach vehicles;
		
		private _status = if (_isSpawned) then { " (Spawned)" } else { " (Stored)" };
		private _entry = format ["%1%2", _vehicleName, _status];
		
		_vehicleList lbAdd _entry;
		_vehicleList lbSetData [_forEachIndex, str(_vehicleId)];
		_vehicleList lbSetValue [_forEachIndex, if (_isSpawned) then { 1 } else { 0 }];
	} forEach EDRP_owned_vehicles;
};

// Spawn vehicle from garage
EDRP_fnc_spawnVehicleFromGarage = {
	params [["_vehicleId", -1, [0]], ["_spawnPos", [0,0,0], [[]]]];
	
	if (_vehicleId < 0) exitWith {
		["No vehicle selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check if vehicle is already spawned
	{
		if ((_x getVariable ["vehicle_id", -1]) == _vehicleId) exitWith {
			["Vehicle is already spawned"] call EDRP_fnc_hint;
		};
	} forEach vehicles;
	
	// Find vehicle in owned vehicles
	private _vehicleClass = "";
	{
		if ((_x select 0) == _vehicleId) exitWith {
			_vehicleClass = _x select 1;
		};
	} forEach EDRP_owned_vehicles;
	
	if (_vehicleClass == "") exitWith {
		["Vehicle not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send spawn request to server
	[getPlayerUID player, _vehicleId, _vehicleClass, _spawnPos] remoteExec ["EDRP_fnc_spawnVehicle", 2];
	
	["Spawning vehicle..."] call EDRP_fnc_hint;
	
	true
};

// Store vehicle in garage
EDRP_fnc_storeVehicleInGarage = {
	params [["_vehicle", objNull, [objNull]]];
	
	if (isNull _vehicle) exitWith {
		["No vehicle selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check ownership
	private _owner = _vehicle getVariable ["vehicle_owner", []];
	if (count _owner == 0 || (_owner select 0) != getPlayerUID player) exitWith {
		["You don't own this vehicle"] call EDRP_fnc_hint;
		false
	};
	
	// Check if anyone is in the vehicle
	if (count (crew _vehicle) > 0) exitWith {
		["Cannot store vehicle while occupied"] call EDRP_fnc_hint;
		false
	};
	
	// Send store request to server
	[_vehicle, getPlayerUID player] remoteExec ["EDRP_fnc_storeVehicle", 2];
	
	["Storing vehicle..."] call EDRP_fnc_hint;
	
	true
};

// Lock/unlock vehicle
EDRP_fnc_toggleVehicleLock = {
	params [["_vehicle", objNull, [objNull]]];
	
	if (isNull _vehicle) exitWith {
		["No vehicle selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player has keys
	private _vehicleKeys = _vehicle getVariable ["vehicle_keys", []];
	if !(getPlayerUID player in _vehicleKeys) exitWith {
		["You don't have keys to this vehicle"] call EDRP_fnc_hint;
		false
	};
	
	private _locked = _vehicle getVariable ["locked", false];
	
	if (_locked) then {
		_vehicle setVariable ["locked", false, true];
		_vehicle lock 0;
		["Vehicle unlocked"] call EDRP_fnc_hint;
	} else {
		_vehicle setVariable ["locked", true, true];
		_vehicle lock 2;
		["Vehicle locked"] call EDRP_fnc_hint;
	};
	
	true
};

// Add vehicle actions
EDRP_fnc_addVehicleActions = {
	// Lock/unlock action
	player addAction [
		"<t color='#FFFF00'>Toggle Vehicle Lock</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_toggleVehicleLock;
		},
		[],
		6,
		true,
		true,
		"",
		"cursorTarget isKindOf 'LandVehicle' || cursorTarget isKindOf 'Air' || cursorTarget isKindOf 'Ship' && cursorTarget distance player < 10 && getPlayerUID player in (cursorTarget getVariable ['vehicle_keys', []])"
	];
	
	// Store vehicle action
	player addAction [
		"<t color='#00FF80'>Store Vehicle</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_storeVehicleInGarage;
		},
		[],
		5,
		true,
		true,
		"",
		"cursorTarget isKindOf 'LandVehicle' || cursorTarget isKindOf 'Air' || cursorTarget isKindOf 'Ship' && cursorTarget distance player < 10 && ((cursorTarget getVariable ['vehicle_owner', []]) select 0) == getPlayerUID player"
	];
};

// Initialize vehicle system on client
if (hasInterface) then {
	[] call EDRP_fnc_initVehicleSystem;
	
	// Add vehicle actions
	[] call EDRP_fnc_addVehicleActions;
};
