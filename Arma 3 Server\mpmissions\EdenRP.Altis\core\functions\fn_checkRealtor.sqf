//	File: fn_checkRealtor.sqf
//	Author: TheCmdrRex
//	Description: Queries server for realtor_cash and rewards player with any cash
// This is terrible code pls don't use this for reference. addActions are the gayest pieces of shit when it comes to params.

private ["_foundCash","_mode"];

_foundCash = (_this select 3) select 0;
_mode = (_this select 3) select 1;
// Simple Check
if (isNull player) exitWith {};


if (_foundCash == -1) exitWith {
	if (_mode != 2) then {
		hint "Checking Realtor for money from sold houses....";
	};
// Call server to find cash value
	[[player,_mode],"EDENS_fnc_realtorCash",false,false] call EDEN_fnc_MP;
};
uiSleep 2; // Sleep cuz fuck you

if (_mode == 2) exitWith {
	if (_foundCash > 0) then {
		hint parseText "<t color='#ffff00' size='2' align='center'>Text from Realtor</t><br/><br/>Please come down to the Realtor's office when you can. We have money waiting for you from one of your listed properties getting purchased!";
	};
};
// More checks
if (_foundCash == -2) exitWith {hint "There was an error retreiving your cash!";};
if (_foundCash == 0) exitWith {hint "There was no cash found at the realtor. You can try again later!";};
// Hey we found cash time to pay you out
if (_foundCash > 0) then {
	hint format ["You have recieved $%1 for a listed house that was sold.",[_foundCash] call EDEN_fnc_numberText];
	eden_atmcash = eden_atmcash + _foundCash;
	eden_cache_atmcash = eden_cache_atmcash + _foundCash;
	[1] call EDEN_fnc_ClupdatePartial;
	[
		["event","Recieved Listed House Pay"],
		["player",name player],
		["player_id",getPlayerUID player],
		["value",_foundCash]
	] call EDEN_fnc_logIt;
};
