/*
	EdenRP Altis Life - Setup Player Actions
	Author: EdenRP Development Team
	Description: Sets up player interaction actions and context menus
	Version: 1.0.0
*/

// Setup player actions function
EDRP_fnc_setupPlayerActions = {
	diag_log "EdenRP: Setting up player actions...";
	
	// Clear existing actions
	removeAllActions player;
	
	// Setup faction-specific actions
	switch (playerSide) do {
		case west: {
			call EDRP_fnc_setupPoliceActions;
		};
		case independent: {
			call EDRP_fnc_setupMedicalActions;
		};
		case civilian: {
			call EDRP_fnc_setupCivilianActions;
		};
	};
	
	// Setup universal actions
	call EDRP_fnc_setupUniversalActions;
	
	diag_log "EdenRP: Player actions setup complete";
};

// Setup police actions
EDRP_fnc_setupPoliceActions = {
	// Arrest action
	player addAction [
		"<t color='#FF0000'>Arrest Player</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_arrestPlayer;
		},
		[],
		10,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			alive _target && 
			isPlayer _target && 
			_target != player && 
			!(_target getVariable ['EDRP_player_arrested', false]) &&
			player distance _target < 5 &&
			(player getVariable ['EDRP_police_on_duty', false])
		"
	];
	
	// Ticket action
	player addAction [
		"<t color='#FFA500'>Issue Ticket</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_issueTicket;
		},
		[],
		9,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			alive _target && 
			isPlayer _target && 
			_target != player && 
			player distance _target < 5 &&
			(player getVariable ['EDRP_police_on_duty', false])
		"
	];
	
	// Search action
	player addAction [
		"<t color='#00FFFF'>Search Player</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_searchPlayer;
		},
		[],
		8,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			alive _target && 
			isPlayer _target && 
			_target != player && 
			(_target getVariable ['EDRP_player_restrained', false]) &&
			player distance _target < 3 &&
			(player getVariable ['EDRP_police_on_duty', false])
		"
	];
	
	// Impound vehicle action
	player addAction [
		"<t color='#FF8000'>Impound Vehicle</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_impoundVehicle;
		},
		[],
		7,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			_target isKindOf 'AllVehicles' && 
			!(_target isKindOf 'Man') &&
			player distance _target < 10 &&
			(player getVariable ['EDRP_police_on_duty', false])
		"
	];
};

// Setup medical actions
EDRP_fnc_setupMedicalActions = {
	// Revive action
	player addAction [
		"<t color='#00FF00'>Revive Player</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_revivePlayer;
		},
		[],
		10,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			isPlayer _target && 
			_target != player && 
			!alive _target &&
			player distance _target < 5 &&
			(player getVariable ['EDRP_medical_on_duty', false])
		"
	];
	
	// Heal action
	player addAction [
		"<t color='#00FF00'>Heal Player</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_healPlayer;
		},
		[],
		9,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			alive _target && 
			isPlayer _target && 
			_target != player && 
			damage _target > 0 &&
			player distance _target < 5 &&
			(player getVariable ['EDRP_medical_on_duty', false])
		"
	];
	
	// Medical examination
	player addAction [
		"<t color='#FFFF00'>Medical Examination</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_medicalExam;
		},
		[],
		8,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			alive _target && 
			isPlayer _target && 
			_target != player && 
			player distance _target < 3 &&
			(player getVariable ['EDRP_medical_on_duty', false])
		"
	];
};

// Setup civilian actions
EDRP_fnc_setupCivilianActions = {
	// Rob player action (if gang member)
	player addAction [
		"<t color='#FF0000'>Rob Player</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_robPlayer;
		},
		[],
		5,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			alive _target && 
			isPlayer _target && 
			_target != player && 
			player distance _target < 3 &&
			(player getVariable ['EDRP_player_gang', ''] != '') &&
			!(_target getVariable ['EDRP_player_restrained', false])
		"
	];
	
	// Give money action
	player addAction [
		"<t color='#00FF00'>Give Money</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_giveMoney;
		},
		[],
		6,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			alive _target && 
			isPlayer _target && 
			_target != player && 
			player distance _target < 5 &&
			(player getVariable ['EDRP_player_cash', 0]) > 0
		"
	];
};

// Setup universal actions
EDRP_fnc_setupUniversalActions = {
	// Flip vehicle action
	player addAction [
		"<t color='#FFFF00'>Flip Vehicle</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_flipVehicle;
		},
		[],
		4,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			_target isKindOf 'AllVehicles' && 
			!(_target isKindOf 'Man') &&
			player distance _target < 10 &&
			(vectorUp _target select 2) < 0.5
		"
	];
	
	// Repair vehicle action
	player addAction [
		"<t color='#00FFFF'>Repair Vehicle</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_repairVehicle;
		},
		[],
		3,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			_target isKindOf 'AllVehicles' && 
			!(_target isKindOf 'Man') &&
			player distance _target < 10 &&
			damage _target > 0 &&
			'ToolKit' in items player
		"
	];
	
	// Gather resources action
	player addAction [
		"<t color='#8FBC8F'>Gather Resources</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_gatherResources;
		},
		[],
		2,
		false,
		true,
		"",
		"
			_target = cursorTarget;
			!isNull _target &&
			!(_target isKindOf 'Man') &&
			player distance _target < 5 &&
			(_target getVariable ['EDRP_resource_type', ''] != '')
		"
	];
	
	// Use item action
	player addAction [
		"<t color='#DDA0DD'>Use Item</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[] call EDRP_fnc_useItem;
		},
		[],
		1,
		false,
		true,
		"",
		"
			count (player getVariable ['EDRP_player_inventory', []]) > 0
		"
	];
};

// Vehicle interaction actions
EDRP_fnc_setupVehicleActions = {
	params ["_vehicle"];
	
	// Lock/unlock vehicle
	_vehicle addAction [
		"<t color='#FFD700'>Lock/Unlock Vehicle</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_toggleVehicleLock;
		},
		[],
		10,
		false,
		true,
		"",
		"
			player distance _target < 10 &&
			([_target] call EDRP_fnc_hasVehicleKey)
		"
	];
	
	// Store vehicle
	_vehicle addAction [
		"<t color='#87CEEB'>Store Vehicle</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_storeVehicle;
		},
		[],
		9,
		false,
		true,
		"",
		"
			player distance _target < 10 &&
			([_target] call EDRP_fnc_hasVehicleKey) &&
			([_target] call EDRP_fnc_isNearGarage)
		"
	];
};

// Object interaction actions
EDRP_fnc_setupObjectActions = {
	params ["_object", "_actionType"];
	
	switch (_actionType) do {
		case "atm": {
			_object addAction [
				"<t color='#00FF00'>Use ATM</t>",
				{
					[] call EDRP_fnc_openATM;
				},
				[],
				10,
				false,
				true,
				"",
				"player distance _target < 3"
			];
		};
		
		case "shop": {
			_object addAction [
				"<t color='#FFD700'>Open Shop</t>",
				{
					params ["_target", "_caller", "_actionId", "_arguments"];
					[_target getVariable "EDRP_shop_type"] call EDRP_fnc_openShop;
				},
				[],
				10,
				false,
				true,
				"",
				"player distance _target < 5"
			];
		};
		
		case "garage": {
			_object addAction [
				"<t color='#87CEEB'>Open Garage</t>",
				{
					params ["_target", "_caller", "_actionId", "_arguments"];
					[_target getVariable "EDRP_garage_type"] call EDRP_fnc_openGarage;
				},
				[],
				10,
				false,
				true,
				"",
				"player distance _target < 10"
			];
		};
	};
};

diag_log "EdenRP: Player actions setup functions loaded";
