# EdenRP Database Installation Guide

## Overview
This guide will help you set up the EdenRP database system with MySQL/MariaDB and extDB3.

## Prerequisites
- MySQL 8.0+ or MariaDB 10.4+
- extDB3 extension for Arma 3
- Administrative access to your database server
- Basic knowledge of SQL and server administration

## Installation Steps

### 1. Database Server Setup

#### Install MySQL/MariaDB
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mariadb-server

# CentOS/RHEL
sudo yum install mariadb-server mariadb

# Windows
# Download and install MySQL/MariaDB from official website
```

#### Secure Installation
```bash
sudo mysql_secure_installation
```

### 2. Create EdenRP Database

#### Connect to MySQL
```bash
mysql -u root -p
```

#### Run Database Schema
```sql
-- Create the database
CREATE DATABASE edenrp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create database user
CREATE USER 'edenrp_user'@'localhost' IDENTIFIED BY 'edenrp_secure_password_2024';
CREATE USER 'edenrp_user'@'%' IDENTIFIED BY 'edenrp_secure_password_2024';

-- Grant privileges
GRANT SELECT, INSERT, UPDATE, DELETE ON edenrp.* TO 'edenrp_user'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON edenrp.* TO 'edenrp_user'@'%';
GRANT EXECUTE ON edenrp.* TO 'edenrp_user'@'localhost';
GRANT EXECUTE ON edenrp.* TO 'edenrp_user'@'%';

FLUSH PRIVILEGES;
```

#### Import Schema
```bash
mysql -u root -p edenrp < edenrp_schema.sql
mysql -u root -p edenrp < setup_database.sql
```

### 3. extDB3 Configuration

#### Install extDB3
1. Download extDB3 from: https://github.com/AsYetUntitled/extDB3
2. Extract to your Arma 3 server directory
3. Copy `extdb3-conf.ini` to your server root

#### Configure extDB3
Edit `extdb3-conf.ini`:
```ini
[Database]
Type = MySQL
Name = Database

Host = 127.0.0.1
Port = 3306
Username = edenrp_user
Password = edenrp_secure_password_2024
Database = edenrp

Compress = true
Secure Auth = true
```

### 4. Server Configuration

#### Update Server Startup
Add to your server startup parameters:
```
-serverMod=@extDB3
```

#### Verify Installation
Check server logs for:
```
EDRP Database: extDB3 Version: [version]
EDRP Database: Connection established successfully
EDRP Database: Initialization complete - Server is ready
```

## Database Structure

### Core Tables
- `edrp_players` - Player data and statistics
- `edrp_vehicles` - Vehicle ownership and status
- `edrp_gangs` - Gang information and management
- `edrp_gang_members` - Gang membership data
- `edrp_houses` - Housing system data
- `edrp_server_config` - Server configuration
- `edrp_wanted_list` - Police wanted system
- `edrp_market_data` - Dynamic economy data
- `edrp_transactions` - Financial transaction logs
- `edrp_phone_messages` - Communication system
- `edrp_police_reports` - Law enforcement reports
- `edrp_medical_records` - Medical system data
- `edrp_territories` - Gang territory system
- `edrp_server_logs` - System and admin logs
- `edrp_skill_progression` - Player skill system

### Key Features
- **JSON Support**: Modern data storage for complex structures
- **Performance Optimized**: Proper indexing and query optimization
- **Security**: SQL injection protection and access controls
- **Scalability**: Designed to handle high player counts
- **Maintenance**: Automated cleanup and optimization
- **Monitoring**: Built-in logging and performance tracking

## Maintenance

### Regular Tasks
```sql
-- Clean old logs (run weekly)
CALL CleanupOldLogs(30);

-- Update market prices (automated)
CALL UpdateMarketPrices();

-- Reset vehicle activity (on restart)
CALL ResetVehicleActivity();
```

### Backup Strategy
```bash
# Daily backup
mysqldump -u edenrp_user -p edenrp > backup_$(date +%Y%m%d).sql

# Automated backup script
#!/bin/bash
BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u edenrp_user -p edenrp | gzip > $BACKUP_DIR/edenrp_$DATE.sql.gz

# Keep only last 7 days
find $BACKUP_DIR -name "edenrp_*.sql.gz" -mtime +7 -delete
```

## Performance Tuning

### MySQL Configuration
Add to `my.cnf`:
```ini
[mysqld]
# InnoDB settings
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2

# Query cache
query_cache_type = 1
query_cache_size = 256M

# Connection settings
max_connections = 200
wait_timeout = 600

# Character set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
```

### Monitoring Queries
```sql
-- Check slow queries
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- Monitor connections
SHOW PROCESSLIST;

-- Check table sizes
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'edenrp'
ORDER BY (data_length + index_length) DESC;
```

## Troubleshooting

### Common Issues

#### Connection Failed
- Check MySQL service status
- Verify user credentials
- Check firewall settings
- Ensure extDB3 is loaded

#### Slow Performance
- Check slow query log
- Optimize table indexes
- Increase buffer pool size
- Monitor server resources

#### Data Corruption
- Run table repair: `REPAIR TABLE table_name;`
- Check table integrity: `CHECK TABLE table_name;`
- Restore from backup if necessary

### Log Locations
- MySQL Error Log: `/var/log/mysql/error.log`
- extDB3 Log: `logs/extdb3.log`
- Arma 3 Server Log: Check server console

## Security Considerations

### Database Security
- Use strong passwords
- Limit user privileges
- Enable SSL connections
- Regular security updates
- Monitor access logs

### Network Security
- Firewall configuration
- VPN access for remote management
- Regular security audits
- Backup encryption

## Support

For technical support:
1. Check server logs first
2. Verify database connectivity
3. Test with minimal configuration
4. Contact EdenRP development team

## Version History
- v1.0.0 - Initial release with full EdenRP integration
- Enhanced security and performance features
- Modern JSON-based data storage
- Comprehensive logging and monitoring
