/*
	EdenRP Altis Life - Vehicle Configuration
	Author: EdenRP Development Team
	Description: Configuration for all vehicles in EdenRP
	Version: 1.0.0
*/

class CfgVehicles {
	// Civilian Cars
	class CivilianCars {
		class C_Offroad_01_F {
			displayName = "Offroad";
			price = 12500;
			insurance = 625;
			impound = 1250;
			trunk = 65;
			fuel = 45;
			side = "civ";
			shop = "car";
			license = "driver";
			level = 0;
			texture[] = {};
		};

		class C_Hatchback_01_F {
			displayName = "Hatchback";
			price = 9500;
			insurance = 475;
			impound = 950;
			trunk = 40;
			fuel = 35;
			side = "civ";
			shop = "car";
			license = "driver";
			level = 0;
			texture[] = {};
		};

		class C_Hatchback_01_sport_F {
			displayName = "Hatchback Sport";
			price = 18500;
			insurance = 925;
			impound = 1850;
			trunk = 45;
			fuel = 40;
			side = "civ";
			shop = "car";
			license = "driver";
			level = 5;
			texture[] = {};
		};

		class C_SUV_01_F {
			displayName = "SUV";
			price = 32500;
			insurance = 1625;
			impound = 3250;
			trunk = 150;
			fuel = 70;
			side = "civ";
			shop = "car";
			license = "driver";
			level = 10;
			texture[] = {};
		};

		class C_Van_01_transport_F {
			displayName = "Transport Van";
			price = 45000;
			insurance = 2250;
			impound = 4500;
			trunk = 200;
			fuel = 100;
			side = "civ";
			shop = "car";
			license = "truck";
			level = 15;
			texture[] = {};
		};
	};

	// Civilian Trucks
	class CivilianTrucks {
		class C_Van_01_box_F {
			displayName = "Box Truck";
			price = 65000;
			insurance = 3250;
			impound = 6500;
			trunk = 350;
			fuel = 150;
			side = "civ";
			shop = "truck";
			license = "truck";
			level = 0;
			texture[] = {};
		};

		class I_Truck_02_transport_F {
			displayName = "Zamak Transport";
			price = 125000;
			insurance = 6250;
			impound = 12500;
			trunk = 500;
			fuel = 200;
			side = "civ";
			shop = "truck";
			license = "truck";
			level = 10;
			texture[] = {};
		};

		class I_Truck_02_covered_F {
			displayName = "Zamak Covered";
			price = 135000;
			insurance = 6750;
			impound = 13500;
			trunk = 550;
			fuel = 200;
			side = "civ";
			shop = "truck";
			license = "truck";
			level = 15;
			texture[] = {};
		};

		class B_Truck_01_transport_F {
			displayName = "HEMTT Transport";
			price = 275000;
			insurance = 13750;
			impound = 27500;
			trunk = 750;
			fuel = 300;
			side = "civ";
			shop = "truck";
			license = "trucking";
			level = 25;
			texture[] = {};
		};

		class B_Truck_01_box_F {
			displayName = "HEMTT Box";
			price = 325000;
			insurance = 16250;
			impound = 32500;
			trunk = 1000;
			fuel = 300;
			side = "civ";
			shop = "truck";
			license = "trucking";
			level = 30;
			texture[] = {};
		};
	};

	// Civilian Helicopters
	class CivilianHelicopters {
		class B_Heli_Light_01_F {
			displayName = "MH-9 Hummingbird";
			price = 245000;
			insurance = 12250;
			impound = 24500;
			trunk = 25;
			fuel = 250;
			side = "civ";
			shop = "air";
			license = "pilot";
			level = 20;
			texture[] = {};
		};

		class O_Heli_Light_02_unarmed_F {
			displayName = "PO-30 Orca";
			price = 485000;
			insurance = 24250;
			impound = 48500;
			trunk = 75;
			fuel = 400;
			side = "civ";
			shop = "air";
			license = "pilot";
			level = 35;
			texture[] = {};
		};

		class I_Heli_Transport_02_F {
			displayName = "CH-49 Mohawk";
			price = 725000;
			insurance = 36250;
			impound = 72500;
			trunk = 200;
			fuel = 500;
			side = "civ";
			shop = "air";
			license = "pilot";
			level = 45;
			texture[] = {};
		};
	};

	// Civilian Boats
	class CivilianBoats {
		class C_Boat_Civil_01_F {
			displayName = "Motor Boat";
			price = 22500;
			insurance = 1125;
			impound = 2250;
			trunk = 45;
			fuel = 100;
			side = "civ";
			shop = "boat";
			license = "boat";
			level = 0;
			texture[] = {};
		};

		class C_Rubberboat {
			displayName = "Rescue Boat";
			price = 8500;
			insurance = 425;
			impound = 850;
			trunk = 20;
			fuel = 50;
			side = "civ";
			shop = "boat";
			license = "boat";
			level = 0;
			texture[] = {};
		};

		class C_Boat_Transport_02_F {
			displayName = "RHIB";
			price = 65000;
			insurance = 3250;
			impound = 6500;
			trunk = 85;
			fuel = 150;
			side = "civ";
			shop = "boat";
			license = "boat";
			level = 15;
			texture[] = {};
		};
	};

	// Police Vehicles
	class PoliceVehicles {
		class C_Offroad_01_F {
			displayName = "Police Offroad";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 85;
			fuel = 45;
			side = "cop";
			shop = "cop_car";
			license = "";
			level = 0;
			texture[] = {
				"#(argb,8,8,3)color(0.05,0.05,0.4,1)"
			};
		};

		class C_SUV_01_F {
			displayName = "Police SUV";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 185;
			fuel = 70;
			side = "cop";
			shop = "cop_car";
			license = "";
			level = 2;
			texture[] = {
				"#(argb,8,8,3)color(0.05,0.05,0.4,1)"
			};
		};

		class B_MRAP_01_F {
			displayName = "Police Hunter";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 225;
			fuel = 150;
			side = "cop";
			shop = "cop_car";
			license = "";
			level = 5;
			texture[] = {
				"#(argb,8,8,3)color(0.05,0.05,0.4,1)"
			};
		};

		class B_Heli_Light_01_F {
			displayName = "Police Hummingbird";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 35;
			fuel = 250;
			side = "cop";
			shop = "cop_air";
			license = "";
			level = 10;
			texture[] = {
				"#(argb,8,8,3)color(0.05,0.05,0.4,1)"
			};
		};

		class B_Heli_Attack_01_F {
			displayName = "Police Ghosthawk";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 150;
			fuel = 400;
			side = "cop";
			shop = "cop_air";
			license = "";
			level = 20;
			texture[] = {
				"#(argb,8,8,3)color(0.05,0.05,0.4,1)"
			};
		};
	};

	// Medical Vehicles
	class MedicalVehicles {
		class C_Offroad_01_F {
			displayName = "Medical Offroad";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 85;
			fuel = 45;
			side = "med";
			shop = "med_car";
			license = "";
			level = 0;
			texture[] = {
				"#(argb,8,8,3)color(1,1,1,1)"
			};
		};

		class C_SUV_01_F {
			displayName = "Medical SUV";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 185;
			fuel = 70;
			side = "med";
			shop = "med_car";
			license = "";
			level = 2;
			texture[] = {
				"#(argb,8,8,3)color(1,1,1,1)"
			};
		};

		class C_Van_01_box_F {
			displayName = "Medical Van";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 400;
			fuel = 150;
			side = "med";
			shop = "med_car";
			license = "";
			level = 5;
			texture[] = {
				"#(argb,8,8,3)color(1,1,1,1)"
			};
		};

		class B_Heli_Light_01_F {
			displayName = "Medical Hummingbird";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 35;
			fuel = 250;
			side = "med";
			shop = "med_air";
			license = "";
			level = 8;
			texture[] = {
				"#(argb,8,8,3)color(1,0,0,1)"
			};
		};

		class O_Heli_Light_02_unarmed_F {
			displayName = "Medical Orca";
			price = 0;
			insurance = 0;
			impound = 0;
			trunk = 85;
			fuel = 400;
			side = "med";
			shop = "med_air";
			license = "";
			level = 15;
			texture[] = {
				"#(argb,8,8,3)color(1,0,0,1)"
			};
		};
	};

	// Motorcycles
	class Motorcycles {
		class C_Quadbike_01_F {
			displayName = "Quad Bike";
			price = 4500;
			insurance = 225;
			impound = 450;
			trunk = 25;
			fuel = 25;
			side = "civ";
			shop = "motorcycle";
			license = "driver";
			level = 0;
			texture[] = {};
		};

		class C_Kart_01_Blu_F {
			displayName = "Go-Kart";
			price = 7500;
			insurance = 375;
			impound = 750;
			trunk = 5;
			fuel = 15;
			side = "civ";
			shop = "motorcycle";
			license = "driver";
			level = 0;
			texture[] = {};
		};
	};
};
