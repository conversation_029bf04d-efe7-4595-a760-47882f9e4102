//  File: fn_corpse.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Hides dead bodies.
private["_corpse"];
_corpse = param [0,<PERSON>b<PERSON><PERSON><PERSON>,[<PERSON>bj<PERSON><PERSON>]];
if(isNull _corpse) exitWith {};
if(alive _corpse) exitWith {}; //Stop script kiddies.
if(isPlayer _corpse) exitWith {};

_corpse enableSimulation false;
_corpse hideObject true;
deleteVehicle _corpse;