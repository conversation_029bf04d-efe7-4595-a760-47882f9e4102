/*
	EdenRP Altis Life - Switch Menu Tab Function
	Author: EdenRP Development Team
	Description: Switches between different tabs in menu dialogs
	Version: 1.0.0
	
	Parameters:
		0: NUMBER - Dialog IDD
		1: STRING - Tab name
		
	Example:
		[3000, "inventory"] call EDRP_fnc_switchMenuTab;
*/

params [
	["_dialogIDD", -1, [0]],
	["_tabName", "", [""]]
];

if (_dialogIDD == -1 || _tabName == "") exitWith {
	["Invalid parameters for tab switch"] call EDRP_fnc_logError;
};

disableSerialization;
private _dialog = findDisplay _dialogIDD;
if (isNull _dialog) exitWith {
	["Dialog not found for tab switch"] call EDRP_fnc_logError;
};

// Play tab switch sound
playSound "EDRP_ui_click";

// Handle different dialog types
switch (_dialogIDD) do {
	case 3000: { // Main Menu
		EDRP_mainmenu_current_tab = _tabName;
		
		// Update tab button appearances
		private _tabs = [2001, 2002, 2003, 2004, 2005, 2006];
		private _tabNames = ["main", "inventory", "vehicle", "phone", "stats", "settings"];
		
		{
			private _tabCtrl = _dialog displayCtrl _x;
			if (_tabNames select _forEachIndex == _tabName) then {
				_tabCtrl ctrlSetBackgroundColor EDRP_COLOR_PRIMARY;
			} else {
				_tabCtrl ctrlSetBackgroundColor [0.3, 0.3, 0.3, 0.8];
			};
		} forEach _tabs;
		
		// Update menu content
		[] spawn EDRP_fnc_updateMainMenu;
	};
	
	case 5000: { // Phone
		EDRP_phone_current_tab = _tabName;
		
		// Update tab button appearances
		private _tabs = [5001, 5002, 5003, 5004, 5005];
		private _tabNames = ["contacts", "messages", "dialer", "apps", "settings"];
		
		{
			private _tabCtrl = _dialog displayCtrl _x;
			if (_tabNames select _forEachIndex == _tabName) then {
				_tabCtrl ctrlSetBackgroundColor EDRP_COLOR_PRIMARY;
			} else {
				_tabCtrl ctrlSetBackgroundColor [0.3, 0.3, 0.3, 0.8];
			};
		} forEach _tabs;
		
		// Update phone content
		[] spawn EDRP_fnc_updatePhone;
	};
	
	case 6000: { // Admin Panel
		EDRP_admin_current_tab = _tabName;
		
		// Update tab button appearances
		private _tabs = [2001, 2002, 2003, 2004, 2005, 2006];
		private _tabNames = ["players", "actions", "vehicles", "server", "logs", "settings"];
		
		{
			private _tabCtrl = _dialog displayCtrl _x;
			if (_tabNames select _forEachIndex == _tabName) then {
				_tabCtrl ctrlSetBackgroundColor EDRP_COLOR_PRIMARY;
			} else {
				_tabCtrl ctrlSetBackgroundColor [0.3, 0.3, 0.3, 0.8];
			};
		} forEach _tabs;
		
		// Update admin panel content
		[] spawn EDRP_fnc_updateAdminPanel;
	};
	
	default {
		[format ["Unknown dialog IDD for tab switch: %1", _dialogIDD]] call EDRP_fnc_logError;
	};
};
