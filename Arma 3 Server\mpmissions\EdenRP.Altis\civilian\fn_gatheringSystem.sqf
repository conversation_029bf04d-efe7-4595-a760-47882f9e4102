/*
	EdenRP Altis Life - Resource Gathering System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Handles all resource gathering activities with skill progression
	Version: 1.0.0
*/

// Main gathering function (adapted from Olympus fn_gather)
EDRP_fnc_gather = {
	if (EDRP_action_gathering > 0) exitWith {};
	if (EDRP_action_inUse) exitWith {["You are already doing something else"] call EDRP_fnc_hint;};

	private _resourceZones = [];
	private _zone = "";
	private _distance = 0;
	private _mineable = "";
	private _gather = "";
	private _totalGathered = 0;

	// Get all gathering zones from config
	{
		private _zones = getArray(configFile >> "CfgGather" >> _x >> "zones");
		{
			_resourceZones pushBack [_x, _forEachIndex];
		} forEach _zones;
	} forEach ("true" configClasses (configFile >> "CfgGather"));

	// Find closest gathering zone
	{
		_distance = player distance (getMarkerPos (_x select 0));
		if (_distance < 30) exitWith {
			_zone = _x select 0;
			_mineable = configName ((configFile >> "CfgGather") select (_x select 1));
		};
	} forEach _resourceZones;

	if (_zone == "") exitWith {["You are not near a resource gathering area"] call EDRP_fnc_hint;};

	// Get resource config
	private _gatherCfg = configFile >> "CfgGather" >> _mineable;
	private _requiredItem = getText(_gatherCfg >> "requiredItem");
	private _amount = getNumber(_gatherCfg >> "amount");
	private _delay = getNumber(_gatherCfg >> "delay");
	private _restricted = getBool(_gatherCfg >> "restricted");

	// Check if restricted and player is civilian
	if (_restricted && EDRP_player_faction == "civilian") exitWith {
		["This is an illegal gathering area - police may arrest you"] call EDRP_fnc_hint;
	};

	// Check required tools
	if (_requiredItem != "" && !([_requiredItem] call EDRP_fnc_hasItem)) exitWith {
		[format ["You need a %1 to gather here", [_requiredItem] call EDRP_fnc_getItemName]] call EDRP_fnc_hint;
	};

	_gather = _mineable;
	EDRP_action_gathering = 1;
	EDRP_action_inUse = true;

	// Start gathering loop
	[_gather, _amount, _delay, _zone] spawn EDRP_fnc_gatheringLoop;
};

// Gathering loop (adapted from Olympus)
EDRP_fnc_gatheringLoop = {
	params ["_gather", "_amount", "_delay", "_zone"];

	private _totalGathered = 0;
	private _itemName = [_gather] call EDRP_fnc_getItemName;

	while {EDRP_action_gathering > 0 && !EDRP_interrupted} do {
		// Check if still in zone
		if (player distance (getMarkerPos _zone) > 30) exitWith {
			["You moved too far from the gathering area"] call EDRP_fnc_hint;
			EDRP_action_gathering = 0;
			EDRP_action_inUse = false;
		};

		// Play gathering animation
		player playMove "AinvPercMstpSnonWnonDnon_Putdown_AmovPercMstpSnonWnonDnon";

		// Progress bar
		[
			_delay,
			format ["Gathering %1...", _itemName],
			{
				params ["_gather", "_amount"];

				// Add item to inventory
				if ([_gather, _amount] call EDRP_fnc_addItem) then {
					[format ["Gathered %1x %2", _amount, [_gather] call EDRP_fnc_getItemName], "success"] call EDRP_fnc_hint;

					// Add XP based on item type
					private _xp = switch (_gather) do {
						case "apple"; case "peach": { 5 };
						case "copperore": { 10 };
						case "ironore": { 15 };
						case "salt": { 8 };
						case "diamond": { 50 };
						case "goldore": { 25 };
						case "oilunprocessed": { 20 };
						case "fishraw": { 12 };
						default { 5 };
					};

					// Determine skill type
					private _skill = switch (_gather) do {
						case "copperore"; case "ironore"; case "salt"; case "diamond"; case "goldore": { "mining" };
						case "oilunprocessed": { "extraction" };
						case "fishraw": { "fishing" };
						default { "" };
					};

					if (_skill != "") then {
						[_skill, _xp] call EDRP_fnc_addJobXP;
					};

					playSound "inventory_move";
					true
				} else {
					["Inventory full"] call EDRP_fnc_hint;
					false
				};
			},
			{
				["Gathering cancelled"] call EDRP_fnc_hint;
				false
			},
			[_gather, _amount]
		] call EDRP_fnc_progressBar;

		sleep 0.5;
	};

	EDRP_action_gathering = 0;
	EDRP_action_inUse = false;
};

// Start gathering resource (legacy compatibility)
EDRP_fnc_startGathering = {
	params [
		["_resourceType", "", [""]],
		["_gatherLocation", "", [""]]
	];
	
	if (_resourceType == "" || _gatherLocation == "") exitWith {
		["Invalid gathering parameters"] call EDRP_fnc_hint;
		false
	};
	
	// Check if already gathering
	if (EDRP_gathering_active) exitWith {
		["You are already gathering resources"] call EDRP_fnc_hint;
		false
	};
	
	// Get resource configuration
	private _config = [_resourceType] call EDRP_fnc_getGatherConfig;
	if (_config isEqualTo []) exitWith {
		["Unknown resource type"] call EDRP_fnc_hint;
		false
	};
	
	_config params ["_name", "_item", "_skill", "_skillReq", "_time", "_amount", "_xp", "_levelReq", "_tools"];
	
	// Check requirements
	if !([_resourceType, _config] call EDRP_fnc_checkGatherRequirements) exitWith { false };
	
	// Check if in valid gathering zone
	if !([_gatherLocation] call EDRP_fnc_isInGatherZone) exitWith {
		["You must be in a valid gathering area"] call EDRP_fnc_hint;
		false
	};
	
	// Start gathering process
	EDRP_gathering_active = true;
	EDRP_gathering_type = _resourceType;
	EDRP_gathering_progress = 0;
	EDRP_gathering_location = _gatherLocation;
	
	// Show gathering started message
	[format ["Started gathering %1...", _name], "info"] call EDRP_fnc_hint;
	
	// Start gathering animation and progress
	[_resourceType, _config] spawn EDRP_fnc_gatheringProcess;
	
	true
};

// Gathering process with progress bar
EDRP_fnc_gatheringProcess = {
	params ["_resourceType", "_config"];
	
	_config params ["_name", "_item", "_skill", "_skillReq", "_time", "_amount", "_xp", "_levelReq", "_tools"];
	
	// Calculate actual gathering time based on skill
	private _skillLevel = if (_skill != "") then { EDRP_job_skills get _skill } else { 0 };
	private _timeReduction = _skillLevel * 0.1; // 10% reduction per skill level
	private _actualTime = _time * (1 - _timeReduction);
	if (_actualTime < (_time * 0.5)) then { _actualTime = _time * 0.5; }; // Minimum 50% of base time
	
	// Start progress bar
	[
		_actualTime,
		format ["Gathering %1...", _name],
		{
			// On success
			params ["_resourceType", "_config"];
			_config params ["_name", "_item", "_skill", "_skillReq", "_time", "_amount", "_xp", "_levelReq", "_tools"];
			
			// Calculate amount based on skill
			private _skillLevel = if (_skill != "") then { EDRP_job_skills get _skill } else { 0 };
			private _baseAmount = _amount select 0;
			private _maxAmount = _amount select 1;
			private _actualAmount = _baseAmount + floor(random(_maxAmount - _baseAmount + 1));
			
			// Skill bonus (up to 50% more)
			private _skillBonus = _skillLevel * 0.05;
			_actualAmount = _actualAmount + floor(_actualAmount * _skillBonus);
			
			// Add items to inventory
			private _added = [_item, _actualAmount] call EDRP_fnc_addItem;
			if (_added) then {
				[format ["Gathered %1x %2", _actualAmount, _name], "success"] call EDRP_fnc_hint;
				
				// Add XP
				if (_skill != "") then {
					[_skill, _xp] call EDRP_fnc_addJobXP;
				};
				
				// Update statistics
				private _statKey = format ["%1_gathered", _resourceType];
				if (_statKey in EDRP_gathering_stats) then {
					EDRP_gathering_stats set [_statKey, (EDRP_gathering_stats get _statKey) + _actualAmount];
				};
				
				// Play gathering sound
				playSound "inventory_move";
				
				// Update job progress if active
				if (EDRP_job_active && EDRP_current_job == _skill) then {
					EDRP_job_progress = EDRP_job_progress + (_actualAmount * 2);
				};
			} else {
				["Inventory full - could not gather resources"] call EDRP_fnc_hint;
			};
			
			EDRP_gathering_active = false;
		},
		{
			// On failure/cancel
			["Gathering cancelled"] call EDRP_fnc_hint;
			EDRP_gathering_active = false;
		},
		[_resourceType, _config]
	] call EDRP_fnc_progressBar;
	
	// Play gathering animation
	private _animation = "AinvPknlMstpSnonWnonDnon_medic_1";
	switch (_resourceType) do {
		case "apple";
		case "peach": { _animation = "AinvPknlMstpSnonWnonDnon_medic_1"; };
		case "copper";
		case "iron";
		case "salt";
		case "diamond";
		case "gold": { _animation = "Acts_carFixingWheel"; };
		case "oil": { _animation = "Acts_Briefing_SB"; };
		case "fish": { _animation = "AinvPercMstpSnonWnonDnon"; };
		case "turtle": { _animation = "AinvPknlMstpSnonWnonDnon_medic_1"; };
		case "wood": { _animation = "Acts_carFixingWheel"; };
	};
	
	player playAction _animation;
};

// Check gathering requirements
EDRP_fnc_checkGatherRequirements = {
	params ["_resourceType", "_config"];
	
	_config params ["_name", "_item", "_skill", "_skillReq", "_time", "_amount", "_xp", "_levelReq", "_tools"];
	
	// Check skill level requirement
	if (_skill != "" && _skillReq > 0) then {
		private _currentLevel = EDRP_job_skills get _skill;
		if (_currentLevel < _skillReq) exitWith {
			[format ["Requires %1 level %2 (current: %3)", _skill, _skillReq, _currentLevel], "error"] call EDRP_fnc_hint;
			false
		};
	};
	
	// Check player level requirement
	if (_levelReq > 0 && EDRP_player_level < _levelReq) exitWith {
		[format ["Requires player level %1 (current: %2)", _levelReq, EDRP_player_level], "error"] call EDRP_fnc_hint;
		false
	};
	
	// Check tool requirements
	{
		if !([_x] call EDRP_fnc_hasItem) exitWith {
			[format ["Requires %1", [_x] call EDRP_fnc_getItemName], "error"] call EDRP_fnc_hint;
			false
		};
	} forEach _tools;
	
	// Check inventory space
	if !([_item, (_amount select 1)] call EDRP_fnc_hasInventorySpace) exitWith {
		["Not enough inventory space"] call EDRP_fnc_hint;
		false
	};
	
	true
};

// Get gathering configuration
EDRP_fnc_getGatherConfig = {
	params [["_resourceType", "", [""]]];
	
	private _configs = createHashMapFromArray [
		// Format: [name, item, skill, skillReq, time, [minAmount, maxAmount], xp, levelReq, [tools]]
		["apple", ["Apple", "apple", "", 0, 3, [1, 3], 5, 0, []]],
		["peach", ["Peach", "peach", "", 0, 3, [1, 3], 5, 0, []]],
		["copper", ["Copper Ore", "copperore", "mining", 0, 8, [1, 2], 10, 0, ["pickaxe"]]],
		["iron", ["Iron Ore", "ironore", "mining", 1, 10, [1, 2], 15, 3, ["pickaxe"]]],
		["salt", ["Salt", "salt", "mining", 0, 5, [2, 4], 8, 0, ["pickaxe"]]],
		["diamond", ["Diamond", "diamond", "mining", 3, 15, [1, 1], 50, 15, ["pickaxe"]]],
		["gold", ["Gold Ore", "goldore", "mining", 2, 12, [1, 2], 25, 10, ["pickaxe"]]],
		["oil", ["Crude Oil", "oilunprocessed", "extraction", 1, 10, [1, 2], 20, 5, ["oilpump"]]],
		["fish", ["Raw Fish", "fishraw", "fishing", 0, 8, [1, 2], 12, 0, ["fishingrod"]]],
		["turtle", ["Turtle Meat", "turtleraw", "hunting", 2, 12, [1, 1], 30, 8, ["huntingknife"]]],
		["wood", ["Wood Log", "woodlog", "logging", 0, 6, [2, 4], 8, 0, ["chainsaw"]]]
	];
	
	_configs getOrDefault [_resourceType, []]
};

// Check if player is in gathering zone
EDRP_fnc_isInGatherZone = {
	params [["_zoneName", "", [""]]];
	
	private _pos = getMarkerPos _zoneName;
	private _size = getMarkerSize _zoneName;
	private _distance = player distance2D _pos;
	
	_distance <= (_size select 0)
};

// Get nearby gathering zones
EDRP_fnc_getNearbyGatherZones = {
	params [["_range", 50, [0]]];
	
	private _nearbyZones = [];
	private _playerPos = getPos player;
	
	// Check all gathering markers
	private _gatherMarkers = [
		"apple_1", "apple_2", "apple_3",
		"peach_1", "peach_2",
		"copper_1", "copper_2", "copper_3",
		"iron_1", "iron_2",
		"salt_1", "salt_2",
		"diamond_1",
		"gold_1",
		"oil_1", "oil_2",
		"fishing_1", "fishing_2", "fishing_3",
		"turtle_1",
		"logging_1", "logging_2", "logging_3"
	];
	
	{
		private _markerPos = getMarkerPos _x;
		if (_playerPos distance2D _markerPos <= _range) then {
			private _resourceType = [_x] call EDRP_fnc_getZoneResourceType;
			_nearbyZones pushBack [_x, _resourceType, _playerPos distance2D _markerPos];
		};
	} forEach _gatherMarkers;
	
	// Sort by distance
	_nearbyZones sort true;
	
	_nearbyZones
};

// Get resource type from zone name
EDRP_fnc_getZoneResourceType = {
	params [["_zoneName", "", [""]]];
	
	private _resourceType = "";
	
	switch (true) do {
		case (_zoneName find "apple" >= 0): { _resourceType = "apple"; };
		case (_zoneName find "peach" >= 0): { _resourceType = "peach"; };
		case (_zoneName find "copper" >= 0): { _resourceType = "copper"; };
		case (_zoneName find "iron" >= 0): { _resourceType = "iron"; };
		case (_zoneName find "salt" >= 0): { _resourceType = "salt"; };
		case (_zoneName find "diamond" >= 0): { _resourceType = "diamond"; };
		case (_zoneName find "gold" >= 0): { _resourceType = "gold"; };
		case (_zoneName find "oil" >= 0): { _resourceType = "oil"; };
		case (_zoneName find "fishing" >= 0): { _resourceType = "fish"; };
		case (_zoneName find "turtle" >= 0): { _resourceType = "turtle"; };
		case (_zoneName find "logging" >= 0): { _resourceType = "wood"; };
	};
	
	_resourceType
};

// Stop gathering
EDRP_fnc_stopGathering = {
	if (!EDRP_gathering_active) exitWith {
		["No active gathering to stop"] call EDRP_fnc_hint;
		false
	};
	
	EDRP_gathering_active = false;
	EDRP_gathering_type = "";
	EDRP_gathering_progress = 0;
	EDRP_gathering_location = "";
	
	// Stop progress bar
	[] call EDRP_fnc_stopProgressBar;
	
	["Gathering stopped"] call EDRP_fnc_hint;
	
	true
};

// Initialize gathering system on client
if (hasInterface) then {
	[] call EDRP_fnc_initGatheringSystem;
};
