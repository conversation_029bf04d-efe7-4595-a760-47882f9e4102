/*
	EdenRP Altis Life - Communication System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Phone, radio, and dispatch communication systems
	Version: 1.0.0
*/

// Initialize communication system
EDRP_fnc_initCommunicationSystem = {
	// Communication state variables
	EDRP_phone_number = "";
	EDRP_phone_contacts = [];
	EDRP_phone_messages = [];
	EDRP_phone_call_active = false;
	EDRP_phone_call_target = "";
	EDRP_radio_frequency = 0;
	EDRP_radio_active = false;
	
	// Communication statistics
	EDRP_communication_stats = createHashMapFromArray [
		["calls_made", 0],
		["messages_sent", 0],
		["radio_transmissions", 0],
		["emergency_calls", 0]
	];
	
	// Load communication configuration
	[] call EDRP_fnc_loadCommunicationConfig;
	
	// Generate phone number
	[] call EDRP_fnc_generatePhoneNumber;
	
	["Communication system initialized"] call EDRP_fnc_logInfo;
};

// Load communication configuration
EDRP_fnc_loadCommunicationConfig = {
	// Emergency numbers
	EDRP_emergency_numbers = createHashMapFromArray [
		["911", "Emergency Services"],
		["912", "Police Department"],
		["913", "Medical Services"],
		["914", "Fire Department"],
		["411", "Directory Assistance"],
		["611", "Taxi Services"]
	];
	
	// Radio frequencies
	EDRP_radio_frequencies = createHashMapFromArray [
		[100.1, "Civilian Channel 1"],
		[100.2, "Civilian Channel 2"],
		[100.3, "Civilian Channel 3"],
		[200.1, "Police Primary"],
		[200.2, "Police Secondary"],
		[200.3, "Police SWAT"],
		[300.1, "Medical Primary"],
		[300.2, "Medical Emergency"],
		[400.1, "Fire Department"],
		[500.1, "Taxi Services"],
		[600.1, "News Media"],
		[700.1, "Gang Channel 1"],
		[700.2, "Gang Channel 2"]
	];
	
	// Phone service providers
	EDRP_phone_providers = [
		["EdenTel", "Premium service with unlimited calls", 50],
		["AltisWireless", "Standard service with basic features", 25],
		["IslandConnect", "Budget service with limited features", 10]
	];
	
	// Message templates
	EDRP_message_templates = [
		["Where are you?", "Quick location check"],
		["Need backup!", "Emergency assistance request"],
		["On my way", "Confirmation message"],
		["Job complete", "Task completion notification"],
		["Meet at garage", "Meeting location"],
		["Call me back", "Return call request"]
	];
};

// Generate phone number
EDRP_fnc_generatePhoneNumber = {
	// Generate unique phone number based on player UID
	private _uid = getPlayerUID player;
	private _hash = 0;
	
	// Simple hash function
	{
		_hash = _hash + (toArray _x select 0);
	} forEach (toArray _uid);
	
	// Generate 7-digit number
	private _number = format ["%1-%2", 
		555 + (_hash mod 445), 
		1000 + (_hash mod 8999)
	];
	
	EDRP_phone_number = _number;
	
	[format ["Your phone number: %1", _number]] call EDRP_fnc_hint;
};

// Open phone menu
EDRP_fnc_openPhoneMenu = {
	// Check if player has phone
	if !(["phone"] call EDRP_fnc_hasItem) exitWith {
		["You need a phone to use this feature"] call EDRP_fnc_hint;
		false
	};
	
	// Create phone dialog
	createDialog "EDRP_PhoneDialog";
	
	// Update phone display
	[] call EDRP_fnc_updatePhoneMenu;
	
	true
};

// Update phone menu
EDRP_fnc_updatePhoneMenu = {
	private _display = findDisplay 53000;
	if (isNull _display) exitWith {};
	
	// Update phone info
	private _phoneInfoCtrl = _display displayCtrl 53001;
	private _phoneInfo = format [
		"Phone: %1\nSignal: %2/5\nBattery: %3%%\nProvider: EdenTel",
		EDRP_phone_number,
		4 + (random 2),
		80 + (random 20)
	];
	_phoneInfoCtrl ctrlSetText _phoneInfo;
	
	// Update contacts list
	private _contactsList = _display displayCtrl 53002;
	lbClear _contactsList;
	
	// Add emergency numbers
	{
		private _number = _x;
		private _name = EDRP_emergency_numbers get _number;
		
		private _entry = format ["%1 - %2", _name, _number];
		
		_contactsList lbAdd _entry;
		_contactsList lbSetData [_forEachIndex, _number];
		_contactsList lbSetColor [_forEachIndex, [1, 0, 0, 1]]; // Red for emergency
	} forEach (keys EDRP_emergency_numbers);
	
	// Add personal contacts
	{
		_x params ["_name", "_number", "_category"];
		
		private _entry = format ["%1 - %2", _name, _number];
		
		_contactsList lbAdd _entry;
		_contactsList lbSetData [count EDRP_emergency_numbers + _forEachIndex, _number];
		
		// Color code by category
		switch (_category) do {
			case "friend": { _contactsList lbSetColor [count EDRP_emergency_numbers + _forEachIndex, [0, 1, 0, 1]]; };
			case "business": { _contactsList lbSetColor [count EDRP_emergency_numbers + _forEachIndex, [0, 0, 1, 1]]; };
			default { _contactsList lbSetColor [count EDRP_emergency_numbers + _forEachIndex, [1, 1, 1, 1]]; };
		};
	} forEach EDRP_phone_contacts;
	
	// Update messages list
	private _messagesList = _display displayCtrl 53003;
	lbClear _messagesList;
	
	// Show recent messages (last 20)
	private _recentMessages = EDRP_phone_messages select [count EDRP_phone_messages - 20, 20];
	{
		_x params ["_sender", "_message", "_timestamp", "_read"];
		
		private _timeStr = [_timestamp] call EDRP_fnc_timeToString;
		private _entry = format ["%1 [%2]: %3", _sender, _timeStr, _message];
		
		_messagesList lbAdd _entry;
		_messagesList lbSetData [_forEachIndex, str(_forEachIndex)];
		
		// Color code by read status
		if (_read) then {
			_messagesList lbSetColor [_forEachIndex, [0.7, 0.7, 0.7, 1]]; // Gray for read
		} else {
			_messagesList lbSetColor [_forEachIndex, [1, 1, 1, 1]]; // White for unread
		};
	} forEach _recentMessages;
};

// Make phone call
EDRP_fnc_makePhoneCall = {
	params [["_number", "", [""]]];
	
	if (_number == "") exitWith {
		["Enter a phone number"] call EDRP_fnc_hint;
		false
	};
	
	if (EDRP_phone_call_active) exitWith {
		["You're already in a call"] call EDRP_fnc_hint;
		false
	};
	
	// Check for emergency numbers
	if (_number in (keys EDRP_emergency_numbers)) then {
		[_number] call EDRP_fnc_handleEmergencyCall;
		exitWith { true };
	};
	
	// Find target player by phone number
	private _targetPlayer = [_number] call EDRP_fnc_findPlayerByPhoneNumber;
	
	if (isNull _targetPlayer) exitWith {
		["Number not in service"] call EDRP_fnc_hint;
		false
	};
	
	// Start call
	EDRP_phone_call_active = true;
	EDRP_phone_call_target = _number;
	
	// Send call request to target
	[EDRP_phone_number, name player] remoteExec ["EDRP_fnc_receivePhoneCall", _targetPlayer];
	
	// Update statistics
	EDRP_communication_stats set ["calls_made", (EDRP_communication_stats get "calls_made") + 1];
	
	["Calling..."] call EDRP_fnc_hint;
	
	// Call timeout
	[] spawn {
		sleep 30;
		if (EDRP_phone_call_active && EDRP_phone_call_target == _number) then {
			["Call timed out"] call EDRP_fnc_hint;
			[] call EDRP_fnc_endPhoneCall;
		};
	};
	
	true
};

// Receive phone call
EDRP_fnc_receivePhoneCall = {
	params [["_callerNumber", "", [""]], ["_callerName", "", [""]]];
	
	if (_callerNumber == "" || _callerName == "") exitWith {};
	
	// Check if player has phone
	if !(["phone"] call EDRP_fnc_hasItem) exitWith {};
	
	// Show incoming call notification
	private _message = format ["Incoming call from %1 (%2)", _callerName, _callerNumber];
	
	if ([_message, "Incoming Call", true, true] call EDRP_fnc_messageBox) then {
		// Accept call
		EDRP_phone_call_active = true;
		EDRP_phone_call_target = _callerNumber;
		
		// Notify caller
		[true] remoteExec ["EDRP_fnc_phoneCallResponse", [_callerNumber] call EDRP_fnc_findPlayerByPhoneNumber];
		
		["Call connected"] call EDRP_fnc_hint;
	} else {
		// Decline call
		[false] remoteExec ["EDRP_fnc_phoneCallResponse", [_callerNumber] call EDRP_fnc_findPlayerByPhoneNumber];
	};
};

// Phone call response
EDRP_fnc_phoneCallResponse = {
	params [["_accepted", false, [true]]];
	
	if (_accepted) then {
		["Call connected"] call EDRP_fnc_hint;
	} else {
		["Call declined"] call EDRP_fnc_hint;
		[] call EDRP_fnc_endPhoneCall;
	};
};

// End phone call
EDRP_fnc_endPhoneCall = {
	if (!EDRP_phone_call_active) exitWith { false };
	
	// Notify other party
	if (EDRP_phone_call_target != "") then {
		private _targetPlayer = [EDRP_phone_call_target] call EDRP_fnc_findPlayerByPhoneNumber;
		if (!isNull _targetPlayer) then {
			[] remoteExec ["EDRP_fnc_phoneCallEnded", _targetPlayer];
		};
	};
	
	// Reset call state
	EDRP_phone_call_active = false;
	EDRP_phone_call_target = "";
	
	["Call ended"] call EDRP_fnc_hint;
	
	true
};

// Phone call ended (remote)
EDRP_fnc_phoneCallEnded = {
	EDRP_phone_call_active = false;
	EDRP_phone_call_target = "";
	
	["Call ended"] call EDRP_fnc_hint;
};

// Send text message
EDRP_fnc_sendTextMessage = {
	params [["_number", "", [""]], ["_message", "", [""]]];
	
	if (_number == "" || _message == "") exitWith {
		["Enter number and message"] call EDRP_fnc_hint;
		false
	};
	
	// Find target player
	private _targetPlayer = [_number] call EDRP_fnc_findPlayerByPhoneNumber;
	
	if (isNull _targetPlayer) exitWith {
		["Number not in service"] call EDRP_fnc_hint;
		false
	};
	
	// Send message to target
	[EDRP_phone_number, name player, _message, time] remoteExec ["EDRP_fnc_receiveTextMessage", _targetPlayer];
	
	// Add to sent messages
	EDRP_phone_messages pushBack [format ["To: %1", _number], _message, time, true];
	
	// Update statistics
	EDRP_communication_stats set ["messages_sent", (EDRP_communication_stats get "messages_sent") + 1];
	
	["Message sent"] call EDRP_fnc_hint;
	
	true
};

// Receive text message
EDRP_fnc_receiveTextMessage = {
	params [["_senderNumber", "", [""]], ["_senderName", "", [""]], ["_message", "", [""]], ["_timestamp", 0, [0]]];
	
	if (_senderNumber == "" || _senderName == "" || _message == "") exitWith {};
	
	// Check if player has phone
	if !(["phone"] call EDRP_fnc_hasItem) exitWith {};
	
	// Add to messages
	EDRP_phone_messages pushBack [format ["%1 (%2)", _senderName, _senderNumber], _message, _timestamp, false];
	
	// Show notification
	[format ["New message from %1: %2", _senderName, _message], "message"] call EDRP_fnc_showNotification;
};

// Handle emergency call
EDRP_fnc_handleEmergencyCall = {
	params [["_number", "", [""]]];
	
	if (_number == "") exitWith { false };
	
	private _serviceName = EDRP_emergency_numbers get _number;
	
	// Update statistics
	EDRP_communication_stats set ["emergency_calls", (EDRP_communication_stats get "emergency_calls") + 1];
	
	switch (_number) do {
		case "911": {
			// General emergency - route to appropriate service
			["Emergency Services", "What is your emergency?"] call EDRP_fnc_showEmergencyDialog;
		};
		case "912": {
			// Police
			[format ["Connected to %1", _serviceName]] call EDRP_fnc_hint;
			[getPlayerUID player, getPosATL player, "Police assistance requested"] remoteExec ["EDRP_fnc_policeDispatch", 2];
		};
		case "913": {
			// Medical
			[format ["Connected to %1", _serviceName]] call EDRP_fnc_hint;
			[getPlayerUID player, getPosATL player, "Medical assistance requested"] remoteExec ["EDRP_fnc_medicalDispatch", 2];
		};
		case "914": {
			// Fire Department
			[format ["Connected to %1", _serviceName]] call EDRP_fnc_hint;
			["Fire department dispatch not yet implemented"] call EDRP_fnc_hint;
		};
		case "411": {
			// Directory
			[] call EDRP_fnc_showDirectoryDialog;
		};
		case "611": {
			// Taxi
			[format ["Connected to %1", _serviceName]] call EDRP_fnc_hint;
			[getPlayerUID player, getPosATL player] remoteExec ["EDRP_fnc_taxiDispatch", 2];
		};
	};
	
	true
};

// Open radio menu
EDRP_fnc_openRadioMenu = {
	// Check if player has radio
	if !(["radio"] call EDRP_fnc_hasItem) exitWith {
		["You need a radio to use this feature"] call EDRP_fnc_hint;
		false
	};
	
	// Create radio dialog
	createDialog "EDRP_RadioDialog";
	
	// Update radio display
	[] call EDRP_fnc_updateRadioMenu;
	
	true
};

// Update radio menu
EDRP_fnc_updateRadioMenu = {
	private _display = findDisplay 54000;
	if (isNull _display) exitWith {};
	
	// Update radio info
	private _radioInfoCtrl = _display displayCtrl 54001;
	private _radioInfo = format [
		"Current Frequency: %1 MHz\nStatus: %2\nRange: 5km\nBattery: %3%%",
		EDRP_radio_frequency,
		if (EDRP_radio_active) then { "ACTIVE" } else { "STANDBY" },
		70 + (random 30)
	];
	_radioInfoCtrl ctrlSetText _radioInfo;
	
	// Update frequency list
	private _frequencyList = _display displayCtrl 54002;
	lbClear _frequencyList;
	
	{
		private _freq = _x;
		private _name = EDRP_radio_frequencies get _freq;
		
		private _entry = format ["%1 MHz - %2", _freq, _name];
		
		_frequencyList lbAdd _entry;
		_frequencyList lbSetData [_forEachIndex, str(_freq)];
		
		// Highlight current frequency
		if (_freq == EDRP_radio_frequency) then {
			_frequencyList lbSetColor [_forEachIndex, [0, 1, 0, 1]]; // Green for current
		} else {
			_frequencyList lbSetColor [_forEachIndex, [1, 1, 1, 1]]; // White for others
		};
	} forEach (keys EDRP_radio_frequencies);
};

// Set radio frequency
EDRP_fnc_setRadioFrequency = {
	params [["_frequency", 0, [0]]];
	
	if (_frequency <= 0) exitWith {
		["Invalid frequency"] call EDRP_fnc_hint;
		false
	};
	
	EDRP_radio_frequency = _frequency;
	EDRP_radio_active = true;
	
	private _channelName = EDRP_radio_frequencies get _frequency;
	if (isNil "_channelName") then { _channelName = "Unknown Channel"; };
	
	[format ["Tuned to %1 MHz - %2", _frequency, _channelName]] call EDRP_fnc_hint;
	
	true
};

// Transmit radio message
EDRP_fnc_transmitRadio = {
	params [["_message", "", [""]]];
	
	if (_message == "") exitWith {
		["Enter a message to transmit"] call EDRP_fnc_hint;
		false
	};
	
	if (!EDRP_radio_active || EDRP_radio_frequency == 0) exitWith {
		["Radio not active or no frequency set"] call EDRP_fnc_hint;
		false
	};
	
	// Send radio transmission to all players on same frequency
	[EDRP_radio_frequency, name player, _message, getPosATL player] remoteExec ["EDRP_fnc_receiveRadioTransmission", -2];
	
	// Update statistics
	EDRP_communication_stats set ["radio_transmissions", (EDRP_communication_stats get "radio_transmissions") + 1];
	
	[format ["[RADIO %1] %2: %3", EDRP_radio_frequency, name player, _message], "radio"] call EDRP_fnc_showNotification;
	
	true
};

// Receive radio transmission
EDRP_fnc_receiveRadioTransmission = {
	params [["_frequency", 0, [0]], ["_sender", "", [""]], ["_message", "", [""]], ["_position", [0,0,0], [[]]]];
	
	if (_frequency == 0 || _sender == "" || _message == "") exitWith {};
	
	// Check if player has radio and is on same frequency
	if !(["radio"] call EDRP_fnc_hasItem) exitWith {};
	if (!EDRP_radio_active || EDRP_radio_frequency != _frequency) exitWith {};
	
	// Check range (5km)
	if (player distance _position > 5000) exitWith {};
	
	// Don't receive own transmissions
	if (_sender == name player) exitWith {};
	
	// Show radio message
	[format ["[RADIO %1] %2: %3", _frequency, _sender, _message], "radio"] call EDRP_fnc_showNotification;
};

// Find player by phone number
EDRP_fnc_findPlayerByPhoneNumber = {
	params [["_number", "", [""]]];
	
	if (_number == "") exitWith { objNull };
	
	{
		private _playerNumber = _x getVariable ["phone_number", ""];
		if (_playerNumber == _number) exitWith { _x };
	} forEach allPlayers;
	
	objNull
};

// Add communication actions
EDRP_fnc_addCommunicationActions = {
	// Phone action
	player addAction [
		"<t color='#00FF00'>Use Phone</t>",
		{
			[] call EDRP_fnc_openPhoneMenu;
		},
		[],
		7,
		false,
		true,
		"",
		"['phone'] call EDRP_fnc_hasItem"
	];
	
	// Radio action
	player addAction [
		"<t color='#0080FF'>Use Radio</t>",
		{
			[] call EDRP_fnc_openRadioMenu;
		},
		[],
		6,
		false,
		true,
		"",
		"['radio'] call EDRP_fnc_hasItem"
	];
};

// Initialize communication system on client
if (hasInterface) then {
	[] call EDRP_fnc_initCommunicationSystem;
	
	// Set phone number variable for other players to find
	player setVariable ["phone_number", EDRP_phone_number, true];
	
	// Add communication actions
	[] call EDRP_fnc_addCommunicationActions;
};
