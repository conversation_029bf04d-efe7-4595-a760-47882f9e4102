/*
	EdenRP Altis Life - Server Initialization Script
	Author: EdenRP Development Team
	Description: Server-side initialization for EdenRP Altis Life
	Version: 1.0.0
*/

// Server startup message
diag_log "EdenRP: Starting server initialization...";

// Initialize server configuration
EDRP_server_name = "EdenRP Altis Life";
EDRP_server_version = "1.0.0";
EDRP_server_max_players = 120;
EDRP_server_slots_police = 20;
EDRP_server_slots_medical = 15;
EDRP_server_slots_civilian = 85;

// Database configuration
EDRP_database_name = "edenrp";
EDRP_database_prefix = "edrp_";

// Initialize database system
call EDRP_fnc_initDatabase;

// Economy configuration
EDRP_economy_starting_cash = 0;
EDRP_economy_starting_bank = 5000;
EDRP_economy_tax_rate = 0.05; // 5% tax
EDRP_economy_inflation_rate = 0.02; // 2% inflation per restart
EDRP_economy_market_volatility = 0.15; // 15% price variation

// Gang system configuration
EDRP_gangs_max_members = 15;
EDRP_gangs_creation_cost = 100000;
EDRP_gangs_territory_cost = 50000;
EDRP_gangs_war_cooldown = 3600; // 1 hour cooldown

// Housing system configuration
EDRP_housing_max_per_player = 3;
EDRP_housing_tax_rate = 0.01; // 1% of house value per restart
EDRP_housing_abandonment_time = 604800; // 7 days

// Vehicle system configuration
EDRP_vehicles_max_per_player = 10;
EDRP_vehicles_impound_cost_multiplier = 0.1; // 10% of vehicle value
EDRP_vehicles_insurance_rate = 0.05; // 5% of vehicle value

// Wanted system configuration
EDRP_wanted_max_bounty = 1000000;
EDRP_wanted_decay_rate = 0.1; // 10% decay per hour
EDRP_wanted_minimum_bounty = 1000;

// Job system configuration
EDRP_jobs_xp_multiplier = 1.0;
EDRP_jobs_payout_multiplier = 1.0;
EDRP_jobs_skill_bonus = 0.05; // 5% bonus per skill level

// Event system configuration
EDRP_events_frequency = 1800; // 30 minutes between events
EDRP_events_duration = 900; // 15 minutes event duration
EDRP_events_reward_multiplier = 1.5;

// Admin system configuration
EDRP_admin_log_all_actions = true;
EDRP_admin_spectate_timeout = 300; // 5 minutes
EDRP_admin_godmode_timeout = 600; // 10 minutes

// Initialize server arrays
EDRP_server_players = [];
EDRP_server_vehicles = [];
EDRP_server_objects = [];
EDRP_server_markers = [];
EDRP_server_events = [];
EDRP_server_gangs = [];
EDRP_server_houses = [];
EDRP_server_wanted = [];
EDRP_server_market = [];

// Initialize cleanup system
EDRP_cleanup_vehicles = true;
EDRP_cleanup_objects = true;
EDRP_cleanup_bodies = true;
EDRP_cleanup_interval = 300; // 5 minutes

// Initialize logging system
EDRP_logging_enabled = true;
EDRP_logging_level = 2; // 0=Error, 1=Warning, 2=Info, 3=Debug
EDRP_logging_database = true;
EDRP_logging_file = true;

// Initialize security system
EDRP_security_enabled = true;
EDRP_security_strict_mode = false;
EDRP_security_ban_threshold = 5;
EDRP_security_kick_threshold = 3;

// Initialize performance monitoring
EDRP_performance_monitoring = true;
EDRP_performance_fps_threshold = 30;
EDRP_performance_memory_threshold = 2048; // MB

// Load server configuration from database
[] spawn {
	// Wait for database connection
	waitUntil {!isNil "EDRP_fnc_asyncCall"};
	
	// Load server settings
	_query = format ["SELECT * FROM %1config WHERE id = 1", EDRP_database_prefix];
	_result = [0, _query] call EDRP_fnc_asyncCall;
	
	if (count _result > 0) then {
		_config = _result select 0;
		
		// Apply loaded configuration
		if (!isNil {_config select "economy_multiplier"}) then {
			EDRP_economy_market_multiplier = _config select "economy_multiplier";
		};
		
		if (!isNil {_config select "xp_multiplier"}) then {
			EDRP_jobs_xp_multiplier = _config select "xp_multiplier";
		};
		
		if (!isNil {_config select "event_frequency"}) then {
			EDRP_events_frequency = _config select "event_frequency";
		};
		
		diag_log "EdenRP: Server configuration loaded from database";
	} else {
		diag_log "EdenRP: Using default server configuration";
	};
	
	// Load admin list
	_query = format ["SELECT uid, level FROM %1admins WHERE active = 1", EDRP_database_prefix];
	_result = [0, _query] call EDRP_fnc_asyncCall;
	
	EDRP_admin_list = [];
	{
		EDRP_admin_list pushBack [_x select "uid", _x select "level"];
	} forEach _result;
	
	publicVariable "EDRP_admin_list";
	diag_log format ["EdenRP: Loaded %1 administrators", count EDRP_admin_list];
	
	// Load gang data
	_query = format ["SELECT * FROM %1gangs WHERE active = 1", EDRP_database_prefix];
	_result = [0, _query] call EDRP_fnc_asyncCall;
	
	EDRP_server_gangs = _result;
	publicVariable "EDRP_server_gangs";
	diag_log format ["EdenRP: Loaded %1 active gangs", count EDRP_server_gangs];
	
	// Load housing data
	_query = format ["SELECT * FROM %1houses WHERE owned = 1", EDRP_database_prefix];
	_result = [0, _query] call EDRP_fnc_asyncCall;
	
	EDRP_server_houses = _result;
	publicVariable "EDRP_server_houses";
	diag_log format ["EdenRP: Loaded %1 owned houses", count EDRP_server_houses];
	
	// Load market data
	_query = format ["SELECT * FROM %1market", EDRP_database_prefix];
	_result = [0, _query] call EDRP_fnc_asyncCall;
	
	EDRP_server_market = _result;
	publicVariable "EDRP_server_market";
	diag_log format ["EdenRP: Loaded %1 market items", count EDRP_server_market];
	
	// Initialize market price fluctuation
	[] spawn EDRP_fnc_marketSystem;
	
	// Initialize gang territory system
	[] spawn EDRP_fnc_gangTerritorySystem;
	
	// Initialize event system
	[] spawn EDRP_fnc_eventSystem;
	
	// Initialize cleanup system
	[] spawn EDRP_fnc_cleanupSystem;
	
	// Initialize performance monitoring
	[] spawn EDRP_fnc_performanceMonitor;
	
	// Server is ready
	EDRP_server_isReady = true;
	publicVariable "EDRP_server_isReady";
	
	diag_log "EdenRP: Server initialization complete and ready for players";
};

// Setup server event handlers
addMissionEventHandler ["PlayerConnected", {
	params ["_id", "_uid", "_name", "_jip", "_owner"];
	
	// Log connection
	diag_log format ["EdenRP: Player %1 (%2) connected", _name, _uid];
	
	// Add to player list
	EDRP_server_players pushBack [_id, _uid, _name, time];
	
	// Check for admin privileges
	_adminLevel = 0;
	{
		if ((_x select 0) isEqualTo _uid) then {
			_adminLevel = _x select 1;
		};
	} forEach EDRP_admin_list;
	
	if (_adminLevel > 0) then {
		diag_log format ["EdenRP: Admin %1 connected with level %2", _name, _adminLevel];
	};
	
	// Send welcome message
	[format ["Welcome to %1, %2!", EDRP_server_name, _name]] remoteExec ["EDRP_fnc_hint", _owner];
}];

addMissionEventHandler ["PlayerDisconnected", {
	params ["_id", "_uid", "_name", "_jip", "_owner"];
	
	// Log disconnection
	diag_log format ["EdenRP: Player %1 (%2) disconnected", _name, _uid];
	
	// Remove from player list
	EDRP_server_players = EDRP_server_players select {(_x select 1) != _uid};
	
	// Save player data
	[_uid] call EDRP_fnc_savePlayerData;
	
	// Clean up player vehicles
	[_uid] call EDRP_fnc_cleanupPlayerVehicles;
}];

// Setup periodic server tasks
[] spawn {
	while {true} do {
		sleep 300; // Every 5 minutes
		
		// Save all player data
		{
			[getPlayerUID _x] call EDRP_fnc_savePlayerData;
		} forEach allPlayers;
		
		// Update server statistics
		[count allPlayers, EDRP_server_max_players] call EDRP_fnc_updateServerStats;
		
		// Check server performance
		[] call EDRP_fnc_checkServerPerformance;
	};
};

// Setup hourly tasks
[] spawn {
	while {true} do {
		sleep 3600; // Every hour
		
		// Process housing taxes
		[] call EDRP_fnc_processHousingTaxes;
		
		// Update gang territories
		[] call EDRP_fnc_updateGangTerritories;
		
		// Process wanted list decay
		[] call EDRP_fnc_processWantedDecay;
		
		// Update market trends
		[] call EDRP_fnc_updateMarketTrends;
	};
};

diag_log "EdenRP: Server initialization script completed";
