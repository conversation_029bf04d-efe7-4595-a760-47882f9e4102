/*
	EdenRP Altis Life - Item Configuration
	Author: EdenRP Development Team
	Description: Configuration for all items in EdenRP
	Version: 1.0.0
*/

class CfgItems {
	// Food Items
	class FoodItems {
		class apple {
			displayName = "Apple";
			weight = 1;
			buyPrice = 25;
			sellPrice = 15;
			illegal = 0;
			edible = 1;
			hunger = 10;
			thirst = 5;
			health = 2;
			icon = "images\icons\items\apple.paa";
			description = "A fresh red apple. Provides nutrition and hydration.";
		};

		class peach {
			displayName = "Peach";
			weight = 1;
			buyPrice = 30;
			sellPrice = 18;
			illegal = 0;
			edible = 1;
			hunger = 12;
			thirst = 8;
			health = 3;
			icon = "images\icons\items\peach.paa";
			description = "A juicy peach. Sweet and nutritious.";
		};

		class bread {
			displayName = "Bread";
			weight = 2;
			buyPrice = 45;
			sellPrice = 25;
			illegal = 0;
			edible = 1;
			hunger = 25;
			thirst = 0;
			health = 5;
			icon = "images\icons\items\bread.paa";
			description = "Fresh baked bread. Very filling.";
		};

		class water {
			displayName = "Water Bottle";
			weight = 1;
			buyPrice = 15;
			sellPrice = 8;
			illegal = 0;
			edible = 1;
			hunger = 0;
			thirst = 35;
			health = 0;
			icon = "images\icons\items\water.paa";
			description = "Clean drinking water. Essential for survival.";
		};

		class fishcooked {
			displayName = "Cooked Fish";
			weight = 2;
			buyPrice = 850;
			sellPrice = 650;
			illegal = 0;
			edible = 1;
			hunger = 30;
			thirst = 0;
			health = 8;
			icon = "images\icons\items\fish_cooked.paa";
			description = "Freshly cooked fish. High in protein.";
		};

		class turtlecooked {
			displayName = "Cooked Turtle Meat";
			weight = 3;
			buyPrice = 6500;
			sellPrice = 5200;
			illegal = 1;
			edible = 1;
			hunger = 45;
			thirst = 0;
			health = 15;
			icon = "images\icons\items\turtle_cooked.paa";
			description = "Illegally obtained turtle meat. Highly nutritious but forbidden.";
		};
	};

	// Raw Materials
	class RawMaterials {
		class copperore {
			displayName = "Copper Ore";
			weight = 4;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\copper_ore.paa";
			description = "Raw copper ore extracted from mines.";
		};

		class ironore {
			displayName = "Iron Ore";
			weight = 5;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\iron_ore.paa";
			description = "Raw iron ore. Requires processing.";
		};

		class salt {
			displayName = "Salt";
			weight = 3;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\salt.paa";
			description = "Unrefined salt crystals.";
		};

		class diamond {
			displayName = "Diamond";
			weight = 1;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\diamond.paa";
			description = "Raw diamond. Extremely valuable when cut.";
		};

		class goldore {
			displayName = "Gold Ore";
			weight = 6;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\gold_ore.paa";
			description = "Raw gold ore. Requires smelting.";
		};

		class oilunprocessed {
			displayName = "Crude Oil";
			weight = 7;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\oil_unprocessed.paa";
			description = "Unrefined crude oil.";
		};

		class fishraw {
			displayName = "Raw Fish";
			weight = 2;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\fish_raw.paa";
			description = "Fresh caught fish. Needs cooking.";
		};

		class turtleraw {
			displayName = "Raw Turtle Meat";
			weight = 4;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 1;
			edible = 0;
			icon = "images\icons\items\turtle_raw.paa";
			description = "Illegally obtained turtle meat. Requires cooking.";
		};
	};

	// Processed Materials
	class ProcessedMaterials {
		class copperingot {
			displayName = "Copper Ingot";
			weight = 3;
			buyPrice = 1450;
			sellPrice = 1200;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\copper_ingot.paa";
			description = "Refined copper ingot ready for sale.";
		};

		class ironingot {
			displayName = "Iron Ingot";
			weight = 4;
			buyPrice = 2850;
			sellPrice = 2400;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\iron_ingot.paa";
			description = "Processed iron ingot. High quality metal.";
		};

		class saltrefined {
			displayName = "Refined Salt";
			weight = 2;
			buyPrice = 1250;
			sellPrice = 1000;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\salt_refined.paa";
			description = "Pure refined salt ready for market.";
		};

		class diamondcut {
			displayName = "Cut Diamond";
			weight = 1;
			buyPrice = 3500;
			sellPrice = 3000;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\diamond_cut.paa";
			description = "Expertly cut diamond. Extremely valuable.";
		};

		class goldbar {
			displayName = "Gold Bar";
			weight = 5;
			buyPrice = 2750;
			sellPrice = 2300;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\gold_bar.paa";
			description = "Pure gold bar. Highly valuable.";
		};

		class oilprocessed {
			displayName = "Processed Oil";
			weight = 6;
			buyPrice = 3250;
			sellPrice = 2800;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\oil_processed.paa";
			description = "Refined oil ready for industrial use.";
		};
	};

	// Illegal Drugs
	class IllegalDrugs {
		class heroinunprocessed {
			displayName = "Heroin (Unprocessed)";
			weight = 4;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 1;
			edible = 0;
			icon = "images\icons\items\heroin_unprocessed.paa";
			description = "Raw heroin. Highly illegal and dangerous.";
		};

		class heroinprocessed {
			displayName = "Heroin (Processed)";
			weight = 3;
			buyPrice = 4500;
			sellPrice = 3800;
			illegal = 1;
			edible = 0;
			icon = "images\icons\items\heroin_processed.paa";
			description = "Processed heroin. Extremely illegal and valuable.";
		};

		class cocaineunprocessed {
			displayName = "Cocaine (Unprocessed)";
			weight = 4;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 1;
			edible = 0;
			icon = "images\icons\items\cocaine_unprocessed.paa";
			description = "Raw cocaine. Illegal narcotic.";
		};

		class cocaineprocessed {
			displayName = "Cocaine (Processed)";
			weight = 3;
			buyPrice = 3850;
			sellPrice = 3200;
			illegal = 1;
			edible = 0;
			icon = "images\icons\items\cocaine_processed.paa";
			description = "Pure cocaine. Highly illegal and valuable.";
		};

		class marijuanaunprocessed {
			displayName = "Marijuana (Unprocessed)";
			weight = 3;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 1;
			edible = 0;
			icon = "images\icons\items\marijuana_unprocessed.paa";
			description = "Raw marijuana plants. Illegal substance.";
		};

		class marijuanaprocessed {
			displayName = "Marijuana (Processed)";
			weight = 2;
			buyPrice = 2250;
			sellPrice = 1800;
			illegal = 1;
			edible = 0;
			icon = "images\icons\items\marijuana_processed.paa";
			description = "Processed marijuana. Illegal but less severe.";
		};
	};

	// Tools and Equipment
	class Tools {
		class pickaxe {
			displayName = "Pickaxe";
			weight = 8;
			buyPrice = 750;
			sellPrice = 400;
			illegal = 0;
			edible = 0;
			icon = "images\icons\tools\pickaxe.paa";
			description = "Heavy duty pickaxe for mining operations.";
		};

		class fishingrod {
			displayName = "Fishing Rod";
			weight = 3;
			buyPrice = 850;
			sellPrice = 450;
			illegal = 0;
			edible = 0;
			icon = "images\icons\tools\fishing_rod.paa";
			description = "Professional fishing rod for catching fish.";
		};

		class huntingknife {
			displayName = "Hunting Knife";
			weight = 2;
			buyPrice = 1250;
			sellPrice = 650;
			illegal = 0;
			edible = 0;
			icon = "images\icons\tools\hunting_knife.paa";
			description = "Sharp hunting knife for processing game.";
		};

		class chemkit {
			displayName = "Chemistry Kit";
			weight = 10;
			buyPrice = 2500;
			sellPrice = 1200;
			illegal = 0;
			edible = 0;
			icon = "images\icons\tools\chemistry_kit.paa";
			description = "Professional chemistry equipment for processing.";
		};

		class oilpump {
			displayName = "Oil Pump";
			weight = 15;
			buyPrice = 1850;
			sellPrice = 950;
			illegal = 0;
			edible = 0;
			icon = "images\icons\tools\oil_pump.paa";
			description = "Industrial oil extraction pump.";
		};

		class gemcutter {
			displayName = "Gem Cutter";
			weight = 5;
			buyPrice = 3500;
			sellPrice = 1800;
			illegal = 0;
			edible = 0;
			icon = "images\icons\tools\gem_cutter.paa";
			description = "Precision tool for cutting precious gems.";
		};
	};

	// Miscellaneous Items
	class Miscellaneous {
		class sand {
			displayName = "Sand";
			weight = 2;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\sand.paa";
			description = "Fine sand for construction and glass making.";
		};

		class rock {
			displayName = "Rock";
			weight = 3;
			buyPrice = 0;
			sellPrice = 0;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\rock.paa";
			description = "Common rock for construction materials.";
		};

		class glass {
			displayName = "Glass";
			weight = 2;
			buyPrice = 450;
			sellPrice = 350;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\glass.paa";
			description = "Clear glass for construction and manufacturing.";
		};

		class cement {
			displayName = "Cement";
			weight = 5;
			buyPrice = 650;
			sellPrice = 500;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\cement.paa";
			description = "Construction cement for building projects.";
		};

		class plastic {
			displayName = "Plastic";
			weight = 2;
			buyPrice = 850;
			sellPrice = 650;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\plastic.paa";
			description = "Processed plastic for manufacturing.";
		};

		class rubber {
			displayName = "Rubber";
			weight = 3;
			buyPrice = 550;
			sellPrice = 400;
			illegal = 0;
			edible = 0;
			icon = "images\icons\items\rubber.paa";
			description = "Processed rubber for industrial use.";
		};
	};
};
