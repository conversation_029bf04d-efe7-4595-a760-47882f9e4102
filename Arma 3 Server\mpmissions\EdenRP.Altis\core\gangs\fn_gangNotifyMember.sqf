//  File: fn_gangNotifyMember.sqf
//	Author: <PERSON> "tk<PERSON><PERSON>" Schultz

//	Description: Notifies gang members of situations

params [
	["_mode",-2,[0]]
];
if (_mode isEqualTo -2) exitWith {};

switch (_mode) do {
	case 1: {
		"Gang Alert" hintC ["Your gang building is at risk of being sold! You need to maintain 8+ members to keep your shed... start recruiting!","Your inventory will be held as collateral until you meet member requirments. Once you meet requirements wait until next restart to obtain inventory access back."];
	};

	default
	{};
};