//  File: fn_seizeMoneyConfirmation.sqf
//	Author: TheCmdrRex
//	Description: Yes this is a script for a confirmation because arma sucks. Probably will change this into a universal cop confirmation script

params [
	["_suspect",obj<PERSON>ull,[obj<PERSON><PERSON>]]
];

closeDialog 0;

private _action = [
	format ["Are you sure you want to seize the cash of off %1", name _suspect],
	"Cash Seizure Confirmation",
	"Yes",
	"No"
] call BIS_fnc_guiMessage;

if !(_action) exitWith {};

[[3,player],"EDEN_fnc_seizePlayerItems",_suspect,false] spawn EDEN_fnc_MP;