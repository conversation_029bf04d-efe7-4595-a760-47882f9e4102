/*
	EdenRP Altis Life - Vehicle Garage System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Advanced garage management and vehicle storage
	Version: 1.0.0
*/

// Initialize garage system
EDRP_fnc_initGarageSystem = {
	// Garage state variables
	EDRP_garage_locations = [];
	EDRP_impound_locations = [];
	EDRP_current_garage = objNull;
	
	// Load garage locations
	[] call EDRP_fnc_loadGarageLocations;
	
	["Garage system initialized"] call EDRP_fnc_logInfo;
};

// Load garage locations
EDRP_fnc_loadGarageLocations = {
	// Public garages
	EDRP_public_garages = [
		["Kavala Central Garage", [3664.73, 13220.1, 0], "civ", 50],
		["Pyrgos Garage", [16019.5, 16952.9, 0], "civ", 30],
		["Athira Garage", [14707.1, 16835.5, 0], "civ", 25],
		["Sofia Garage", [4022.15, 11669.5, 0], "civ", 20],
		["Zaros Garage", [8688.33, 15834.2, 0], "civ", 15],
		["Paros Garage", [22893.5, 16710.6, 0], "civ", 15]
	];
	
	// Aircraft hangars
	EDRP_aircraft_hangars = [
		["Kavala Airport", [5035.26, 12844.3, 0], "air", 10],
		["Pyrgos Airfield", [16757.8, 12439.6, 0], "air", 8],
		["Altis Airport", [15179.7, 17380.5, 0], "air", 12]
	];
	
	// Boat marinas
	EDRP_boat_marinas = [
		["Kavala Marina", [3866.15, 13222.6, 0], "ship", 15],
		["Pyrgos Harbor", [15990.4, 17015.2, 0], "ship", 10],
		["Athira Dock", [13549.2, 14588.9, 0], "ship", 8]
	];
	
	// Police garages
	EDRP_police_garages = [
		["Police HQ Garage", [3542.87, 13216.3, 0], "cop", 20],
		["Pyrgos PD Garage", [15876.8, 16972.1, 0], "cop", 15],
		["Athira PD Garage", [14846.1, 16834.7, 0], "cop", 10]
	];
	
	// Medical garages
	EDRP_medical_garages = [
		["Kavala Hospital Garage", [3530.54, 13212.9, 0], "med", 15],
		["Pyrgos Medical Garage", [16028.3, 17000.4, 0], "med", 10],
		["Athira Medical Garage", [14890.2, 16923.1, 0], "med", 8]
	];
	
	// Impound lots
	EDRP_impound_lots = [
		["Kavala Impound", [3312.45, 13128.7, 0], "impound", 100],
		["Pyrgos Impound", [15654.2, 16789.3, 0], "impound", 50],
		["Athira Impound", [14523.8, 16712.4, 0], "impound", 30]
	];
};

// Find nearest garage
EDRP_fnc_findNearestGarage = {
	params [["_position", [0,0,0], [[]]], ["_garageType", "civ", [""]]];
	
	private _garageArray = [];
	switch (_garageType) do {
		case "civ": { _garageArray = EDRP_public_garages; };
		case "air": { _garageArray = EDRP_aircraft_hangars; };
		case "ship": { _garageArray = EDRP_boat_marinas; };
		case "cop": { _garageArray = EDRP_police_garages; };
		case "med": { _garageArray = EDRP_medical_garages; };
		case "impound": { _garageArray = EDRP_impound_lots; };
	};
	
	private _nearestGarage = [];
	private _nearestDistance = 999999;
	
	{
		_x params ["_name", "_pos", "_type", "_capacity"];
		private _distance = _position distance _pos;
		
		if (_distance < _nearestDistance) then {
			_nearestDistance = _distance;
			_nearestGarage = _x;
		};
	} forEach _garageArray;
	
	_nearestGarage
};

// Check garage capacity
EDRP_fnc_checkGarageCapacity = {
	params [["_garagePos", [0,0,0], [[]]], ["_garageType", "civ", [""]]];
	
	// Count vehicles near garage
	private _vehiclesNear = 0;
	{
		if (_x distance _garagePos < 100) then {
			_vehiclesNear = _vehiclesNear + 1;
		};
	} forEach vehicles;
	
	// Get garage capacity
	private _garageInfo = [_garagePos, _garageType] call EDRP_fnc_findNearestGarage;
	if (count _garageInfo == 0) exitWith { false };
	
	private _capacity = _garageInfo select 3;
	
	_vehiclesNear < _capacity
};

// Open advanced garage menu
EDRP_fnc_openAdvancedGarage = {
	params [["_garageType", "civ", [""]]];
	
	// Store garage type
	EDRP_current_garage_type = _garageType;
	
	// Create advanced garage dialog
	createDialog "EDRP_AdvancedGarageDialog";
	
	// Update garage display
	[] call EDRP_fnc_updateAdvancedGarage;
	
	true
};

// Update advanced garage display
EDRP_fnc_updateAdvancedGarage = {
	private _display = findDisplay 42000;
	if (isNull _display || isNil "EDRP_current_garage_type") exitWith {};
	
	// Update garage info
	private _garageInfoCtrl = _display displayCtrl 42001;
	private _nearestGarage = [getPosATL player, EDRP_current_garage_type] call EDRP_fnc_findNearestGarage;
	
	if (count _nearestGarage > 0) then {
		_nearestGarage params ["_name", "_pos", "_type", "_capacity"];
		private _vehiclesStored = count EDRP_owned_vehicles;
		
		private _garageInfo = format [
			"Garage: %1\nType: %2\nCapacity: %3/%4\nDistance: %5m",
			_name,
			_type,
			_vehiclesStored,
			_capacity,
			round(player distance _pos)
		];
		_garageInfoCtrl ctrlSetText _garageInfo;
	};
	
	// Update vehicle list with detailed info
	private _vehicleList = _display displayCtrl 42002;
	lbClear _vehicleList;
	
	{
		_x params ["_vehicleId", "_vehicleClass"];
		
		// Get vehicle details
		private _vehicleConfig = [_vehicleClass, EDRP_current_garage_type] call EDRP_fnc_getVehicleConfig;
		private _vehicleName = if (count _vehicleConfig > 0) then { _vehicleConfig select 1 } else { _vehicleClass };
		
		// Check vehicle status
		private _vehicleObj = objNull;
		private _isSpawned = false;
		{
			if ((_x getVariable ["vehicle_id", -1]) == _vehicleId) exitWith {
				_vehicleObj = _x;
				_isSpawned = true;
			};
		} forEach vehicles;
		
		// Get vehicle condition
		private _condition = "Unknown";
		private _fuel = "Unknown";
		private _damage = "Unknown";
		
		if (_isSpawned && !isNull _vehicleObj) then {
			_condition = if (damage _vehicleObj < 0.1) then { "Excellent" } else {
				if (damage _vehicleObj < 0.3) then { "Good" } else {
					if (damage _vehicleObj < 0.6) then { "Fair" } else { "Poor" }
				}
			};
			_fuel = format ["%1%%", round((fuel _vehicleObj) * 100)];
			_damage = format ["%1%%", round((damage _vehicleObj) * 100)];
		} else {
			_condition = "Stored";
			_fuel = "Full";
			_damage = "0%";
		};
		
		private _status = if (_isSpawned) then { "Spawned" } else { "Stored" };
		private _entry = format ["%1 [%2] - Fuel: %3, Damage: %4", _vehicleName, _status, _fuel, _damage];
		
		_vehicleList lbAdd _entry;
		_vehicleList lbSetData [_forEachIndex, str(_vehicleId)];
		_vehicleList lbSetValue [_forEachIndex, if (_isSpawned) then { 1 } else { 0 }];
		
		// Color code based on condition
		if (_isSpawned) then {
			_vehicleList lbSetColor [_forEachIndex, [0, 1, 0, 1]]; // Green for spawned
		} else {
			_vehicleList lbSetColor [_forEachIndex, [0.7, 0.7, 0.7, 1]]; // Gray for stored
		};
	} forEach EDRP_owned_vehicles;
	
	// Update impounded vehicles
	private _impoundList = _display displayCtrl 42003;
	lbClear _impoundList;
	
	{
		_x params ["_vehicleId", "_vehicleClass", "_impoundReason", "_impoundCost"];
		
		private _vehicleConfig = [_vehicleClass, "civ"] call EDRP_fnc_getVehicleConfig;
		private _vehicleName = if (count _vehicleConfig > 0) then { _vehicleConfig select 1 } else { _vehicleClass };
		
		private _entry = format ["%1 - $%2 (%3)", _vehicleName, [_impoundCost] call EDRP_fnc_numberText, _impoundReason];
		
		_impoundList lbAdd _entry;
		_impoundList lbSetData [_forEachIndex, str(_vehicleId)];
		_impoundList lbSetValue [_forEachIndex, _impoundCost];
		_impoundList lbSetColor [_forEachIndex, [1, 0.5, 0, 1]]; // Orange for impounded
	} forEach EDRP_impounded_vehicles;
};

// Spawn vehicle with advanced options
EDRP_fnc_spawnVehicleAdvanced = {
	params [["_vehicleId", -1, [0]], ["_spawnOptions", [], [[]]]];
	
	if (_vehicleId < 0) exitWith {
		["No vehicle selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check if vehicle is already spawned
	{
		if ((_x getVariable ["vehicle_id", -1]) == _vehicleId) exitWith {
			["Vehicle is already spawned"] call EDRP_fnc_hint;
		};
	} forEach vehicles;
	
	// Find suitable spawn position
	private _spawnPos = [player, EDRP_current_garage_type] call EDRP_fnc_findVehicleSpawnPosition;
	if (count _spawnPos == 0) exitWith {
		["No suitable spawn position found"] call EDRP_fnc_hint;
		false
	};
	
	// Check garage capacity
	if !([_spawnPos, EDRP_current_garage_type] call EDRP_fnc_checkGarageCapacity) exitWith {
		["Garage is at full capacity"] call EDRP_fnc_hint;
		false
	};
	
	// Send spawn request with options
	[getPlayerUID player, _vehicleId, _spawnPos, _spawnOptions] remoteExec ["EDRP_fnc_spawnVehicleAdvanced", 2];
	
	["Spawning vehicle with advanced options..."] call EDRP_fnc_hint;
	
	true
};

// Find vehicle spawn position
EDRP_fnc_findVehicleSpawnPosition = {
	params [["_player", objNull, [objNull]], ["_garageType", "civ", [""]]];
	
	if (isNull _player) exitWith { [] };
	
	private _playerPos = getPosATL _player;
	private _nearestGarage = [_playerPos, _garageType] call EDRP_fnc_findNearestGarage;
	
	if (count _nearestGarage == 0) exitWith { [] };
	
	private _garagePos = _nearestGarage select 1;
	private _spawnPositions = [];
	
	// Generate potential spawn positions around garage
	for "_i" from 0 to 359 step 30 do {
		private _testPos = _garagePos vectorAdd [
			(sin _i) * (10 + (random 20)),
			(cos _i) * (10 + (random 20)),
			0
		];
		
		// Check if position is clear
		if (count (nearestObjects [_testPos, ["LandVehicle", "Air", "Ship"], 5]) == 0) then {
			_spawnPositions pushBack _testPos;
		};
	};
	
	// Return best position (closest to player)
	private _bestPos = [];
	private _bestDistance = 999999;
	
	{
		private _distance = _playerPos distance _x;
		if (_distance < _bestDistance) then {
			_bestDistance = _distance;
			_bestPos = _x;
		};
	} forEach _spawnPositions;
	
	_bestPos
};

// Retrieve impounded vehicle
EDRP_fnc_retrieveImpoundedVehicle = {
	params [["_vehicleId", -1, [0]]];
	
	if (_vehicleId < 0) exitWith {
		["No vehicle selected"] call EDRP_fnc_hint;
		false
	};
	
	// Find impounded vehicle
	private _impoundedVehicle = [];
	{
		if ((_x select 0) == _vehicleId) exitWith {
			_impoundedVehicle = _x;
		};
	} forEach EDRP_impounded_vehicles;
	
	if (count _impoundedVehicle == 0) exitWith {
		["Vehicle not found in impound"] call EDRP_fnc_hint;
		false
	};
	
	_impoundedVehicle params ["_vehId", "_vehicleClass", "_reason", "_cost"];
	
	// Check if player has enough money
	if (EDRP_player_bank < _cost) exitWith {
		[format ["You need $%1 to retrieve this vehicle", [_cost] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
		false
	};
	
	// Confirm retrieval
	private _message = format [
		"Retrieve %1 for $%2?\n\nImpound Reason: %3",
		_vehicleClass,
		[_cost] call EDRP_fnc_numberText,
		_reason
	];
	
	if ([_message, "Retrieve Vehicle", true, true] call EDRP_fnc_messageBox) then {
		// Deduct money
		EDRP_player_bank = EDRP_player_bank - _cost;
		
		// Send retrieval request to server
		[getPlayerUID player, _vehicleId, _cost] remoteExec ["EDRP_fnc_retrieveImpoundedVehicle", 2];
		
		// Remove from impounded list
		{
			if ((_x select 0) == _vehicleId) exitWith {
				EDRP_impounded_vehicles deleteAt _forEachIndex;
			};
		} forEach EDRP_impounded_vehicles;
		
		// Add back to owned vehicles
		EDRP_owned_vehicles pushBack [_vehicleId, _vehicleClass];
		
		// Update statistics
		EDRP_vehicle_stats set ["impounds", (EDRP_vehicle_stats get "impounds") - 1];
		
		["Vehicle retrieved from impound"] call EDRP_fnc_hint;
		
		// Update display
		[] call EDRP_fnc_updateAdvancedGarage;
		
		true
	} else {
		false
	};
};

// Vehicle maintenance and repair
EDRP_fnc_repairVehicle = {
	params [["_vehicle", objNull, [objNull]]];
	
	if (isNull _vehicle) exitWith {
		["No vehicle selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check ownership
	private _owner = _vehicle getVariable ["vehicle_owner", []];
	if (count _owner == 0 || (_owner select 0) != getPlayerUID player) exitWith {
		["You don't own this vehicle"] call EDRP_fnc_hint;
		false
	};
	
	// Calculate repair cost based on damage
	private _damage = damage _vehicle;
	private _vehicleConfig = [typeOf _vehicle, "civ"] call EDRP_fnc_getVehicleConfig;
	private _basePrice = if (count _vehicleConfig > 0) then { _vehicleConfig select 2 } else { 10000 };
	private _repairCost = round(_basePrice * _damage * 0.1); // 10% of vehicle value per damage
	
	if (_damage < 0.05) exitWith {
		["Vehicle doesn't need repairs"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player has enough money
	if (EDRP_player_bank < _repairCost) exitWith {
		[format ["You need $%1 to repair this vehicle", [_repairCost] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
		false
	};
	
	// Confirm repair
	private _message = format [
		"Repair vehicle for $%1?\n\nDamage: %2%%",
		[_repairCost] call EDRP_fnc_numberText,
		round(_damage * 100)
	];
	
	if ([_message, "Repair Vehicle", true, true] call EDRP_fnc_messageBox) then {
		// Deduct money
		EDRP_player_bank = EDRP_player_bank - _repairCost;
		
		// Repair vehicle
		_vehicle setDamage 0;
		_vehicle setFuel 1;
		
		// Update statistics
		EDRP_vehicle_stats set ["repairs", (EDRP_vehicle_stats get "repairs") + 1];
		
		["Vehicle repaired successfully"] call EDRP_fnc_hint;
		
		true
	} else {
		false
	};
};

// Add garage actions
EDRP_fnc_addGarageActions = {
	// Repair vehicle action
	player addAction [
		"<t color='#FF8000'>Repair Vehicle</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_repairVehicle;
		},
		[],
		4,
		true,
		true,
		"",
		"cursorTarget isKindOf 'LandVehicle' || cursorTarget isKindOf 'Air' || cursorTarget isKindOf 'Ship' && cursorTarget distance player < 10 && ((cursorTarget getVariable ['vehicle_owner', []]) select 0) == getPlayerUID player && damage cursorTarget > 0.05"
	];
};

// Initialize garage system on client
if (hasInterface) then {
	[] call EDRP_fnc_initGarageSystem;
	
	// Add garage actions
	[] call EDRP_fnc_addGarageActions;
};
