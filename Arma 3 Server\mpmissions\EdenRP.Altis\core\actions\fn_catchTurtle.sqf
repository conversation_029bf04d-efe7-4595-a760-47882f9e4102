//  File: fn_catchTurtle.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine

//	Description: Catches a dead turtle?
private _obj = cursorTarget;
if(isNull _obj) exitWith {}; //Not valid
if(alive _obj) exitWith {}; //It's alive, don't take it charlie!
if ((player distance2d (getMarkerPos "turtle_one") > 230) && (player distance2d (getMarkerPos "turtle_two") > 230)) exitWith {hint "You need to be in a turtle poaching zone to collect turtles!";};

if([true,"turtle",1] call EDEN_fnc_handleInv) then {
	deleteVehicle _obj;
	titleText[localize "STR_NOTF_CaughtTurtle","PLAIN DOWN"];
};