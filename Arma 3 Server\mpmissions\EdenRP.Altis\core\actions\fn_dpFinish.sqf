//  File: fn_dpFinish.sqf
//	Author: <PERSON> "<PERSON>" <PERSON>wine
//	Editor: TheCmdrRex
//	Description: Finishes the  DP Mission and calculates the money earned based
//	on distance between points

private["_dp","_dis","_price"];

params [
	["_dp",Ob<PERSON><PERSON><PERSON>,[<PERSON>b<PERSON><PERSON><PERSON>]]
];

eden_delivery_in_progress = false;
eden_dp_point = nil;
_dis = round((getPos life_dp_start) distance (getPos _dp));
_price = round(2.4 * _dis);

["DeliverySucceeded",[format[(localize "STR_NOTF_Earned_1"),[_price] call EDEN_fnc_numberText]]] call bis_fnc_showNotification;
[
	["event","Finished DP Mission"],
	["player",name player],
	["player_id",getPlayerUID player],
	["value",[_price] call EDEN_fnc_numberText],
	["location",getPosATL player]
] call EDEN_fnc_logIt;

life_cur_task setTaskState "Succeeded";
player removeSimpleTask life_cur_task;
eden_atmcash = eden_atmcash + _price;
eden_cache_atmcash = eden_cache_atmcash + _price;
systemChat "Package Delievered";
