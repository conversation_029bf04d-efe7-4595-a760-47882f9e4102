/*
	EdenRP Altis Life - Medical System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Core medical/EMS system with ranks, equipment, and emergency response
	Version: 1.0.0
*/

// Initialize medical system
EDRP_fnc_initMedicalSystem = {
	// Medical state variables
	EDRP_medical_active = false;
	EDRP_medical_rank = 1;
	EDRP_medical_department = "ems";
	EDRP_medical_badge_number = 0;
	EDRP_medical_callsign = "";
	
	// Medical equipment state
	EDRP_medical_equipment_level = 1;
	EDRP_medical_vehicle = objNull;
	EDRP_medical_responding = false;
	
	// Medical statistics
	EDRP_medical_stats = createHashMapFromArray [
		["patients_revived", 0],
		["patients_treated", 0],
		["emergency_calls", 0],
		["distance_traveled", 0],
		["time_on_duty", 0],
		["invoices_issued", 0]
	];
	
	// Load medical configuration
	[] call EDRP_fnc_loadMedicalConfig;
	
	["Medical system initialized"] call EDRP_fnc_logInfo;
};

// Load medical configuration
EDRP_fnc_loadMedicalConfig = {
	// Medical rank structure (adapted from Olympus)
	EDRP_medical_ranks = [
		["EMT", 1, 20000, ["Medikit", "FirstAidKit"]],
		["Paramedic", 2, 25000, ["Medikit", "FirstAidKit", "ToolKit"]],
		["Advanced Paramedic", 3, 30000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]],
		["Field Supervisor", 4, 35000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]],
		["Shift Supervisor", 5, 40000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]],
		["Training Officer", 6, 45000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]],
		["Lieutenant", 7, 50000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]],
		["Captain", 8, 55000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]],
		["Deputy Chief", 9, 60000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]],
		["Chief of Medicine", 10, 65000, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]]
	];
	
	// Medical departments
	EDRP_medical_departments = [
		["Emergency Medical Services", "ems", "Emergency response and patient care"],
		["Search and Rescue", "sar", "Search and rescue operations"],
		["Air Medical", "air", "Helicopter emergency medical services"],
		["Training Division", "training", "Medical training and education"],
		["Administration", "admin", "Medical administration and oversight"]
	];
	
	// Medical treatment costs
	EDRP_medical_costs = [
		["revive", 5000, "Emergency Revival"],
		["treatment", 1000, "Medical Treatment"],
		["surgery", 15000, "Emergency Surgery"],
		["transport", 2500, "Medical Transport"],
		["consultation", 500, "Medical Consultation"]
	];
	
	// Medical equipment by rank
	EDRP_medical_equipment = createHashMapFromArray [
		[1, ["Medikit", "FirstAidKit", "FirstAidKit", "FirstAidKit"]],
		[2, ["Medikit", "FirstAidKit", "FirstAidKit", "FirstAidKit", "ToolKit"]],
		[3, ["Medikit", "FirstAidKit", "FirstAidKit", "FirstAidKit", "ToolKit", "hgun_P07_F", "16Rnd_9x21_Mag"]],
		[4, ["Medikit", "FirstAidKit", "FirstAidKit", "FirstAidKit", "ToolKit", "hgun_P07_F", "16Rnd_9x21_Mag", "Binocular"]],
		[5, ["Medikit", "FirstAidKit", "FirstAidKit", "FirstAidKit", "ToolKit", "hgun_P07_F", "16Rnd_9x21_Mag", "Binocular"]]
	];
	
	// Allowed medical items
	EDRP_medical_allowed_items = [
		"ItemGPS", "ItemMap", "ItemWatch", "Binocular", "Rangefinder", "ToolKit",
		"Medikit", "FirstAidKit", "Chemlight_yellow", "NVGoggles_INDEP",
		"SmokeShellYellow", "Chemlight_red", "Chemlight_green", "Chemlight_blue",
		"6Rnd_GreenSignal_F", "6Rnd_RedSignal_F", "hgun_P07_F", "16Rnd_9x21_Mag"
	];
	
	// Allowed medical clothing
	EDRP_medical_allowed_clothing = [
		"U_I_CombatUniform_shortsleeve", "H_Cap_blu", "V_RebreatherIA",
		"B_Carryall_oucamo", "V_Rangemaster_belt", "U_Rangemaster_F"
	];
};

// Go on duty as medical
EDRP_fnc_goOnDutyMedical = {
	if (EDRP_medical_active) exitWith {
		["You are already on duty"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player is medical faction
	if (EDRP_player_faction != "medical") exitWith {
		["You are not a medical officer"] call EDRP_fnc_hint;
		false
	};
	
	// Set on duty
	EDRP_medical_active = true;
	
	// Load medical gear
	[] call EDRP_fnc_loadMedicalGear;
	
	// Set medical uniform
	[] call EDRP_fnc_setMedicalUniform;
	
	// Add medical actions
	[] call EDRP_fnc_addMedicalActions;
	
	// Show on duty message
	["You are now on duty", "success"] call EDRP_fnc_hint;
	
	// Broadcast to other medics
	[format ["%1 is now on duty", name player]] remoteExec ["EDRP_fnc_medicalMessage", independent];
	
	// Start duty timer
	EDRP_medical_duty_start = time;
	
	true
};

// Go off duty
EDRP_fnc_goOffDutyMedical = {
	if (!EDRP_medical_active) exitWith {
		["You are not on duty"] call EDRP_fnc_hint;
		false
	};
	
	// Set off duty
	EDRP_medical_active = false;
	
	// Remove medical actions
	[] call EDRP_fnc_removeMedicalActions;
	
	// Load civilian gear
	[] call EDRP_fnc_loadCivilianGear;
	
	// Update duty time statistics
	if (!isNil "EDRP_medical_duty_start") then {
		private _dutyTime = time - EDRP_medical_duty_start;
		EDRP_medical_stats set ["time_on_duty", (EDRP_medical_stats get "time_on_duty") + _dutyTime];
	};
	
	// Show off duty message
	["You are now off duty"] call EDRP_fnc_hint;
	
	// Broadcast to other medics
	[format ["%1 is now off duty", name player]] remoteExec ["EDRP_fnc_medicalMessage", independent];
	
	true
};

// Load medical gear based on rank
EDRP_fnc_loadMedicalGear = {
	// Strip current gear
	removeAllWeapons player;
	removeAllItems player;
	removeAllAssignedItems player;
	removeUniform player;
	removeVest player;
	removeBackpack player;
	removeHeadgear player;
	removeGoggles player;
	
	// Add medical uniform
	player forceAddUniform "U_I_CombatUniform_shortsleeve";
	player addBackpack "B_Carryall_oucamo";
	player addHeadgear "H_Cap_blu";
	
	// Add basic items
	player addItem "ItemMap";
	player assignItem "ItemMap";
	player addItem "ItemCompass";
	player assignItem "ItemCompass";
	player addItem "ItemWatch";
	player assignItem "ItemWatch";
	player addItem "ItemGPS";
	player assignItem "ItemGPS";
	player addItem "NVGoggles_INDEP";
	player assignItem "NVGoggles_INDEP";
	
	// Add rank-specific equipment
	private _equipment = EDRP_medical_equipment getOrDefault [EDRP_medical_rank, []];
	{
		if (isClass (configFile >> "CfgWeapons" >> _x)) then {
			player addWeapon _x;
		} else {
			player addItem _x;
		};
	} forEach _equipment;
	
	// Add medical-specific items
	player addItem "Chemlight_yellow";
	player addItem "Chemlight_yellow";
	player addItem "SmokeShellYellow";
	player addItem "6Rnd_GreenSignal_F";
	
	// Set medical variables
	player setVariable ["isMedic", true, true];
	player setVariable ["rank", EDRP_medical_rank, true];
	player setVariable ["department", EDRP_medical_department, true];
};

// Set medical uniform based on rank and department
EDRP_fnc_setMedicalUniform = {
	private _uniform = "U_I_CombatUniform_shortsleeve";
	private _vest = "";
	private _headgear = "H_Cap_blu";
	private _backpack = "B_Carryall_oucamo";
	
	// Department-specific uniforms
	switch (EDRP_medical_department) do {
		case "sar": {
			_uniform = "U_I_CombatUniform_shortsleeve";
			_vest = "V_Rangemaster_belt";
			_headgear = "H_Cap_blu";
			_backpack = "B_Carryall_oucamo";
		};
		case "air": {
			_uniform = "U_B_HeliPilotCoveralls";
			_vest = "";
			_headgear = "H_PilotHelmetHeli_I";
			_backpack = "B_Carryall_oucamo";
		};
		case "training": {
			_uniform = "U_I_CombatUniform_shortsleeve";
			_vest = "V_Rangemaster_belt";
			_headgear = "H_Cap_blu";
			_backpack = "B_Carryall_oucamo";
		};
	};
	
	// Apply uniform
	if (_uniform != "") then { player forceAddUniform _uniform; };
	if (_vest != "") then { player addVest _vest; };
	if (_headgear != "") then { player addHeadgear _headgear; };
	if (_backpack != "") then { player addBackpack _backpack; };
};

// Add medical actions to scroll wheel
EDRP_fnc_addMedicalActions = {
	// Revive action
	player addAction [
		"<t color='#00FF00'>Revive Patient</t>",
		{
			private _target = cursorTarget;
			if (isNull _target || !(_target isKindOf "Man") || _target == player) exitWith {
				["Invalid target for revival"] call EDRP_fnc_hint;
			};
			[_target] call EDRP_fnc_revivePlayer;
		},
		[],
		6,
		true,
		true,
		"",
		"EDRP_medical_active && cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 5 && !(alive cursorTarget)"
	];
	
	// Treat action
	player addAction [
		"<t color='#FFFF00'>Treat Patient</t>",
		{
			private _target = cursorTarget;
			if (isNull _target || !(_target isKindOf "Man") || _target == player) exitWith {
				["Invalid target for treatment"] call EDRP_fnc_hint;
			};
			[_target] call EDRP_fnc_treatPlayer;
		},
		[],
		5,
		true,
		true,
		"",
		"EDRP_medical_active && cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 5 && alive cursorTarget"
	];
	
	// Issue invoice action
	player addAction [
		"<t color='#FF8000'>Issue Medical Invoice</t>",
		{
			private _target = cursorTarget;
			if (isNull _target || !(_target isKindOf "Man") || _target == player) exitWith {
				["Invalid target for invoice"] call EDRP_fnc_hint;
			};
			[_target] call EDRP_fnc_issueMedicalInvoice;
		},
		[],
		4,
		true,
		true,
		"",
		"EDRP_medical_active && cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 5"
	];
};

// Remove medical actions
EDRP_fnc_removeMedicalActions = {
	// Remove all actions (simplified - in practice you'd track action IDs)
	removeAllActions player;
};

// Get medical rank name
EDRP_fnc_getMedicalRankName = {
	params [["_rank", EDRP_medical_rank, [0]]];
	
	private _rankData = EDRP_medical_ranks select (_rank - 1);
	if (isNil "_rankData") exitWith { "Unknown" };
	
	_rankData select 0
};

// Get medical rank salary
EDRP_fnc_getMedicalRankSalary = {
	params [["_rank", EDRP_medical_rank, [0]]];
	
	private _rankData = EDRP_medical_ranks select (_rank - 1);
	if (isNil "_rankData") exitWith { 0 };
	
	_rankData select 2
};

// Check medical gear compliance (adapted from Olympus)
EDRP_fnc_checkMedicalGear = {
	if (!EDRP_medical_active) exitWith {};
	
	private _save = false;
	
	// Check weapons
	if !(primaryWeapon player == "" || primaryWeapon player in EDRP_medical_allowed_items) then {
		player removeWeapon primaryWeapon player;
		_save = true;
	};
	
	if !(secondaryWeapon player == "" || secondaryWeapon player in EDRP_medical_allowed_items) then {
		player removeWeapon secondaryWeapon player;
		_save = true;
	};
	
	if !(handgunWeapon player == "" || handgunWeapon player in EDRP_medical_allowed_items) then {
		// Allow sidearm for rank 3+
		if (EDRP_medical_rank < 3) then {
			player removeWeapon handgunWeapon player;
			_save = true;
		};
	};
	
	// Check clothing
	if !(uniform player == "" || uniform player in EDRP_medical_allowed_clothing) then {
		removeUniform player;
		player forceAddUniform "U_I_CombatUniform_shortsleeve";
		_save = true;
	};
	
	if !(vest player == "" || vest player in EDRP_medical_allowed_clothing) then {
		removeVest player;
		_save = true;
	};
	
	if !(headgear player == "" || headgear player in EDRP_medical_allowed_clothing) then {
		removeHeadgear player;
		player addHeadgear "H_Cap_blu";
		_save = true;
	};
	
	// Check items
	private _badItems = [];
	{
		if !(_x in EDRP_medical_allowed_items) then {
			_badItems pushBackUnique _x;
		};
	} forEach (uniformItems player + vestItems player + backpackItems player);
	
	{
		player removeItem _x;
		_save = true;
	} forEach _badItems;
	
	if (_save) then {
		["Unauthorized equipment removed"] call EDRP_fnc_hint;
	};
};

// Initialize medical system on client
if (hasInterface && EDRP_player_faction == "medical") then {
	[] call EDRP_fnc_initMedicalSystem;
	
	// Start gear check loop
	[] spawn {
		while {true} do {
			sleep 30;
			[] call EDRP_fnc_checkMedicalGear;
		};
	};
};
