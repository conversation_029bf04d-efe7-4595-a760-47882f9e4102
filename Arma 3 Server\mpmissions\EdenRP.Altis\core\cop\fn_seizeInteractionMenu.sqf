//  File: fn_seizeInteractionMenu.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Replaces the mass for various cop actions towards another player.

#include <interaction.h>
private["_display","_Btn1","_Btn2","_Btn3","_Btn4","_Btn5","_Btn6","_Btn7","_Btn8"];
if(!dialog) then {
	["pInteraction_Menu"] call EDEN_fnc_createDialog;
};
disableSerialization;
params [
	["_curTarget",objNull,[objNull]]
];
if(isNull _curTarget) exitWith {closeDialog 0;}; //Bad target
if(!isPlayer _curTarget && side _curTarget isEqualTo civilian) exitWith {closeDialog 0;}; //Bad side check?
life_pInact_curTarget = _curTarget;
[
	["License Revoke Menu", "[life_pInact_curTarget] call EDEN_fnc_revokeInteractionMenu;"],
	["Seize Illegal Items/Drugs", "[[1,player],""EDEN_fnc_seizePlayerItems"",life_pInact_curTarget,FALSE] spawn EDEN_fnc_MP;"],
	["Seize Illegal Weapons/Clothing", "[[2,player],""EDEN_fnc_seizePlayerItems"",life_pInact_curTarget,FALSE] spawn EDEN_fnc_MP;"],
	["Seize Cash On Hand", "[life_pInact_curTarget] spawn EDEN_fnc_seizeMoneyConfirmation;"],
	["Take Fire. Pin", "if (player distance life_pInact_curTarget > 4) exitWith {hint 'You are too far away.'}; [life_pInact_curTarget, 6] spawn EDEN_fnc_neuterActionCl; closeDialog 0;"]
] call EDEN_fnc_setupInteractionMenu;
