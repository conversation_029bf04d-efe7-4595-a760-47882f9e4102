// File: fn_localPardon.sqf
// Original Author: codeYeTi
// Description: Executes a pardon via the windows key menu

#include "..\..\macro.h"

params [
	["_officer", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
	["_criminal", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]]
];
if (isNull _officer || isNull _criminal) exitWith {};

private _criminalUID = getPlayerUID _criminal;
if (isNil "_criminalUID" || { _criminalUID == "" }) exitWith {
	hint "Pardoning failed because of bad player U<PERSON>";
};

private _cGangID = (_criminal getVariable ["gang_data", [0, "", 0]]) select 0;
if (__GETC__(life_coplevel) < 3 && { count eden_cop_gangData > 0 } && { _cGangID != 0 } && { (eden_cop_gangData select 0) == _cGangID }) exitWith {
	hint "You may not pardon your own gang member.";
};

private _confirmed = [
	format ["Are you sure you want to pardon %1 of all their charges?", name _criminal],
	"Confirm <PERSON><PERSON>",
	"Yes",
	"No",
	findDisplay 37400
] call BIS_fnc_guiMessage;
if !(_confirmed) exitWith {};

[1, format ["%1 pardoned %2", name _officer, name _criminal]] remoteExecCall ["EDEN_fnc_broadcast", -2];
[_criminalUID] remoteExec ["EDENS_fnc_wantedPardon", 2];
["pardons", 1] spawn EDEN_fnc_statArrUp;
[
	["event", "Pardoned"],
	["player", name player],
	["player_id", getPlayerUID player],
	["target", name _criminal],
	["target_id", _criminalUID],
	["position", getPos player]
]	call EDEN_fnc_logIt;
