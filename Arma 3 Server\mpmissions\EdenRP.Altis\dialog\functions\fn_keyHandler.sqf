/*
	EdenRP Altis Life - Key Handler Function
	Author: EdenRP Development Team
	Description: <PERSON><PERSON> key presses for dialog and game interactions
	Version: 1.0.0
	
	Parameters:
		0: DISPLAY - Display
		1: NUMBER - Key code
		2: BOOL - Shift pressed
		3: BOOL - <PERSON>tr<PERSON> pressed
		4: BOOL - Alt pressed
		
	Returns:
		BOOL - True if key was handled
*/

params ["_display", "_key", "_shift", "_ctrl", "_alt"];

// Don't handle keys if player is typing
if (inputAction "User20" > 0) exitWith { false };

// Key code definitions
#define KEY_Y 21
#define KEY_I 23
#define KEY_U 22
#define KEY_T 20
#define KEY_F1 59
#define KEY_F2 60
#define KEY_F3 61
#define KEY_F4 62
#define KEY_F5 63
#define KEY_ESC 1
#define KEY_ENTER 28
#define KEY_TAB 15

private _handled = false;

// Handle key combinations
switch (_key) do {
	case KEY_Y: {
		if (!_shift && !_ctrl && !_alt) then {
			// Open main menu
			if (EDRP_current_dialog == "") then {
				["mainMenu"] call EDRP_fnc_createDialog;
			} else {
				call EDRP_fnc_closeAllDialogs;
			};
			_handled = true;
		};
	};
	
	case KEY_I: {
		if (!_shift && !_ctrl && !_alt) then {
			// Open inventory
			if (EDRP_current_dialog == "") then {
				["inventory"] call EDRP_fnc_createDialog;
			} else {
				call EDRP_fnc_closeAllDialogs;
			};
			_handled = true;
		};
	};
	
	case KEY_U: {
		if (!_shift && !_ctrl && !_alt) then {
			// Unlock/lock vehicle
			[] call EDRP_fnc_keyInteraction;
			_handled = true;
		};
	};
	
	case KEY_T: {
		if (!_shift && !_ctrl && !_alt) then {
			// Open trunk/storage
			[] call EDRP_fnc_openTrunk;
			_handled = true;
		};
	};
	
	case KEY_F1: {
		if (!_shift && !_ctrl && !_alt) then {
			// Police quick actions
			if (EDRP_player_faction == "police") then {
				[] call EDRP_fnc_policeQuickMenu;
				_handled = true;
			};
		};
	};
	
	case KEY_F2: {
		if (!_shift && !_ctrl && !_alt) then {
			// Medical quick actions
			if (EDRP_player_faction == "medical") then {
				[] call EDRP_fnc_medicalQuickMenu;
				_handled = true;
			};
		};
	};
	
	case KEY_F3: {
		if (!_shift && !_ctrl && !_alt) then {
			// Phone
			["phone"] call EDRP_fnc_createDialog;
			_handled = true;
		};
	};
	
	case KEY_F4: {
		if (!_shift && !_ctrl && !_alt) then {
			// Surrender
			[] call EDRP_fnc_surrender;
			_handled = true;
		};
	};
	
	case KEY_F5: {
		if (!_shift && !_ctrl && !_alt) then {
			// Admin panel (if admin)
			if (call EDRP_fnc_isAdmin) then {
				["adminPanel"] call EDRP_fnc_createDialog;
				_handled = true;
			};
		};
	};
	
	case KEY_ESC: {
		// Close current dialog
		if (EDRP_current_dialog != "") then {
			call EDRP_fnc_closeAllDialogs;
			_handled = true;
		};
	};
	
	case KEY_ENTER: {
		// Quick confirm in dialogs
		if (EDRP_current_dialog != "") then {
			// Handle enter key in specific dialogs
			switch (EDRP_current_dialog) do {
				case "message": {
					[] call EDRP_fnc_sendMessage;
					closeDialog 5100;
					_handled = true;
				};
				case "contact": {
					[] call EDRP_fnc_addContact;
					closeDialog 5200;
					_handled = true;
				};
			};
		};
	};
	
	case KEY_TAB: {
		if (_shift && !_ctrl && !_alt) then {
			// Toggle HUD
			[] call EDRP_fnc_toggleHUD;
			_handled = true;
		};
	};
};

// Handle number keys for quick actions (1-9)
if (_key >= 2 && _key <= 10 && !_shift && !_ctrl && !_alt) then {
	private _actionNumber = _key - 1; // Convert to 1-9
	
	// Execute quick action if main menu is open
	if (EDRP_current_dialog == "mainMenu") then {
		[_actionNumber] call EDRP_fnc_executeQuickActionByNumber;
		_handled = true;
	};
};

_handled
