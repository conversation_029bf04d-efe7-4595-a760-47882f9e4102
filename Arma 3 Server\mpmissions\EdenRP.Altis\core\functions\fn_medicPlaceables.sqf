#include "..\..\macro.h"
//  File: fn_medicPlaceables.sqf
//	Author: Ozadu
//	Description: Medic placables.

params[["_mode","",[""]]];

if (playerSide isEqualTo west && {eden_inCombat}) exitWith {};
if !(isNull objectParent player) exitWith {hint "You cannot use placeable objects from inside a vehicle!";};
if (_mode isEqualTo "") exitWith {};
if (playerSide isEqualTo civilian) exitWith {};
if (eden_newsTeam) exitWith {};
if (playerSide isEqualTo independent && call (life_medicLevel) < 2) exitWith {};
if (playerSide isEqualTo west && call (life_coplevel) < 2) exitWith {};

_objectTypes = ["Barrier","Cone","HelipadLight"];

if (playerSide isEqualTo west) then {
	[] spawn{
		while {!isNull eden_medic_placeable} do {
			uiSleep 0.2;
			if (eden_inCombat) then {
				deleteVehicle eden_medic_placeable;
				hint "Please do not use placeable objects while in combat";
			};
		};
	};
};

switch(_mode) do {
	case "Place":{
		if(isNull eden_medic_placeable) exitWith {};
		_collision = [eden_medic_placeable] call EDEN_fnc_objectCollides;
		if(_collision) then {
			deleteVehicle eden_medic_placeable;
			hint "Object placement blocked.";
		}else{
			_objType = typeOf eden_medic_placeable;
			_objPos = getPosATL eden_medic_placeable;
			_objDir = getDir eden_medic_placeable;
			deleteVehicle eden_medic_placeable;
			[[player, _objType,_objPos,_objDir],"EDENS_fnc_spawnMedicPlaceable",false] call EDEN_fnc_mp;
		};
		["life_medic_roadKit"] call EDEN_fnc_createDialog;
	};
	case "Pickup":{
		_object = param[1,objNull,[objNull]];
		deleteVehicle _object;
	};
	case "":{
		//why tho?
	};
	default {
		if(!isNull eden_medic_placeable) exitWith {};
		if(!(_mode in _objectTypes)) exitWith {};

		private ["_objType","_offSet"];
		switch(_mode) do {
			case "Barrier":{
				_objType = "RoadBarrier_small_F";
				_offSet = [0,1.5,0.8];
			};
			case "Cone":{
				_objType = "RoadCone_F";
				_offSet = [0,1.5,0.3];
			};
			case "HelipadLight":{
				_objType = "PortableHelipadLight_01_yellow_F";
				_offSet = [0,1.5,0.3];
			};
		};
		_obj = _objType createVehicleLocal getPos player;
		_obj attachTo [player,_offSet];
		eden_medic_placeable = _obj;
	};
};