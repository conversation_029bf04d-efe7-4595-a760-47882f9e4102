//  File: fn_hudSetup.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Setups the hud for the player?
private["_display","_ui"];
if(isServer && isDedicated) exitWith {};
disableSerialization;
_display = findDisplay 46;

2 cutRsc ["playerHUD","PLAIN DOWN"];
[] call EDEN_fnc_hudUpdate;

if(eden_hudSetup) exitWith {};
eden_hudSetup = true;
LIFE_ID_PlayerTags = ["LIFE_PlayerTags","onEachFrame","EDEN_fnc_playerTags"] call BIS_fnc_addStackedEventHandler;

[] spawn{
	private ["_dam","_eventStatus","_food","_water","_weight"];
	while {true} do{
		_dam = damage player;
		_eventStatus = (player getVariable ["isInEvent",["no"]]) select 0;
		_food = eden_hunger;
		_water = eden_thirst;
		_weight = eden_carryWeight;
		waitUntil {uiSleep 0.25; damage player != _dam || (player getVariable ["isInEvent",["no"]]) select 0 != _eventStatus || eden_hunger != _food || eden_thirst != _water || eden_carryWeight != _weight};
		[] call EDEN_fnc_hudUpdate;
	};
};

[] spawn{
	_alvl = call life_adminlevel;
	_dlvl = call eden_developerlevel;
	while {_alvl > 0 || _dlvl > 0} do{
		private["_god","_esp","_stream","_fly","_invis","_stase"];
		_god = eden_godmode;
		_esp = eden_eventESP;
		_stream = eden_streamerMode;
		_fly = player getVariable["fly",false];
		_invis = player getVariable ["invis", false];
		_stase = player getVariable ["superTaze", false];
		waitUntil {uiSleep 1; (!(eden_godmode isEqualTo _god) || !(eden_eventESP isEqualTo _esp) || !(eden_streamerMode isEqualTo _stream) || !(player getVariable["fly",false] isEqualTo _fly) || !(player getVariable ["invis", false] isEqualTo _invis) || !(player getVariable ["superTaze", false] isEqualTo _stase))};
		[] call EDEN_fnc_hudUpdate;
	};
};

"eden_conquestData" addPublicVariableEventHandler {
	if (alive player) then {
		if (eden_conquestData select 0 && getPos player inPolygon (eden_conquestData select 1 select 1)) then {
			if (uiNamespace getVariable ["conqHUD", displayNull] isEqualType displayNull) then {
				"conq" cutRsc ["conqHUD", "PLAIN"];
			};
			_ui = uiNamespace getVariable ["conqHUD", displayNull];
			(_ui displayCtrl 23800) ctrlSetText format["$%1", [eden_conquestData select 4] call EDEN_fnc_numberText];
			if !(eden_gang_data isEqualTo [] || {(eden_conquestData select 2) find (eden_gang_data select 0) < 0}) then {
				(_ui displayCtrl 23804) ctrlSetText format["%1 - %2", eden_gang_data select 1, eden_conquestData select 3 select ((eden_conquestData select 2) find (eden_gang_data select 0))];
			};
			if ((((eden_conquestData select 6) select 0) select 0) != -1) then { //1st Place name
				(_ui displayCtrl 23801) ctrlSetText format["%1 - %2", ((eden_conquestData select 6) select 0) select 2, ((eden_conquestData select 6) select 0) select 1];
			} else {
				(_ui displayCtrl 23801) ctrlSetText "";
			};
			if ((((eden_conquestData select 6) select 1) select 0) != -1) then { //2nd Place name
				(_ui displayCtrl 23802) ctrlSetText format["%1 - %2", ((eden_conquestData select 6) select 1) select 2, ((eden_conquestData select 6) select 1) select 1];
			} else {
 				(_ui displayCtrl 23802) ctrlSetText "";
 			};
			if ((((eden_conquestData select 6) select 2) select 0) != -1) then { //3rd place name
				(_ui displayCtrl 23803) ctrlSetText format["%1 - %2", ((eden_conquestData select 6) select 2) select 2, ((eden_conquestData select 6) select 2) select 1];
			} else {
 				(_ui displayCtrl 23803) ctrlSetText "";
 			};
		} else {
			"conq" cutText ["", "PLAIN"];
		};
	};
};
