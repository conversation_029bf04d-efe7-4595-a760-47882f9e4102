/*
	EdenRP Altis Life - Loading Screen System
	Author: EdenRP Development Team
	Description: Handles client loading screen and progress display
	Version: 1.0.0
*/

// Initialize loading screen
EDRP_fnc_initLoadingScreen = {
	diag_log "EdenRP: Initializing loading screen...";
	
	// Loading screen variables
	EDRP_loading_active = true;
	EDRP_loading_progress = 0;
	EDRP_loading_status = "Connecting to EdenRP...";
	EDRP_loading_tips = [
		"Use Y to open the interaction menu",
		"Press T while in a vehicle for vehicle options",
		"<PERSON>ft+P opens your phone",
		"I opens your inventory",
		"H holsters/unholsters your weapon",
		"Always roleplay - stay in character!",
		"Respect other players and follow server rules",
		"Join our Discord for updates and support",
		"Report bugs and issues to server staff",
		"Have fun and enjoy your time on EdenRP!"
	];
	EDRP_current_tip = 0;
	
	// Create loading screen display
	call EDRP_fnc_createLoadingDisplay;
	
	// Start loading screen loop
	[] spawn EDRP_fnc_loadingScreenLoop;
	
	diag_log "EdenRP: Loading screen initialized";
};

// Create loading display
EDRP_fnc_createLoadingDisplay = {
	// Disable user input during loading
	disableUserInput true;
	
	// Create loading screen layer
	EDRP_loading_layer = "EDRP_LoadingScreen" cutRsc ["EDRP_LoadingScreen", "PLAIN", -1, false];
	
	// Get display and controls
	private _display = uiNamespace getVariable "EDRP_LoadingDisplay";
	
	if (!isNil "_display") then {
		// Setup initial values
		private _progressBar = _display displayCtrl 1001;
		private _statusText = _display displayCtrl 1002;
		private _tipText = _display displayCtrl 1003;
		private _serverInfo = _display displayCtrl 1004;
		
		// Set initial text
		_statusText ctrlSetText EDRP_loading_status;
		_tipText ctrlSetText (EDRP_loading_tips select EDRP_current_tip);
		_serverInfo ctrlSetText "EdenRP Altis Life - Welcome to the Island";
		
		// Set initial progress
		_progressBar progressSetPosition (EDRP_loading_progress / 100);
	};
};

// Loading screen loop
EDRP_fnc_loadingScreenLoop = {
	private _lastTipChange = time;
	private _tipChangeInterval = 5; // Change tip every 5 seconds
	
	while {EDRP_loading_active} do {
		// Get display
		private _display = uiNamespace getVariable "EDRP_LoadingDisplay";
		
		if (!isNil "_display") then {
			// Update progress bar
			private _progressBar = _display displayCtrl 1001;
			_progressBar progressSetPosition (EDRP_loading_progress / 100);
			
			// Update status text
			private _statusText = _display displayCtrl 1002;
			_statusText ctrlSetText EDRP_loading_status;
			
			// Update tip text periodically
			if (time - _lastTipChange > _tipChangeInterval) then {
				EDRP_current_tip = (EDRP_current_tip + 1) mod (count EDRP_loading_tips);
				private _tipText = _display displayCtrl 1003;
				_tipText ctrlSetText format ["TIP: %1", EDRP_loading_tips select EDRP_current_tip];
				_lastTipChange = time;
			};
			
			// Update server info with player count
			private _serverInfo = _display displayCtrl 1004;
			private _playerCount = count allPlayers;
			_serverInfo ctrlSetText format ["EdenRP Altis Life - Players Online: %1/120", _playerCount];
		};
		
		sleep 0.1;
	};
	
	// Loading complete - hide screen
	call EDRP_fnc_hideLoadingScreen;
};

// Update loading progress
EDRP_fnc_updateLoadingProgress = {
	params ["_progress", "_status"];
	
	EDRP_loading_progress = _progress;
	EDRP_loading_status = _status;
	
	diag_log format ["EdenRP Loading: %1%% - %2", _progress, _status];
};

// Hide loading screen
EDRP_fnc_hideLoadingScreen = {
	diag_log "EdenRP: Hiding loading screen...";
	
	// Fade out loading screen
	EDRP_loading_layer cutFadeOut 2;
	
	// Wait for fade out
	sleep 2;
	
	// Enable user input
	disableUserInput false;
	
	// Mark loading as complete
	EDRP_loading_active = false;
	
	// Show welcome message
	[] spawn {
		sleep 1;
		["Welcome to EdenRP", "Enjoy your stay on Altis!", "success", 10] call EDRP_fnc_notification;
	};
	
	diag_log "EdenRP: Loading screen hidden";
};

// Loading screen phases
EDRP_fnc_loadingPhases = {
	// Phase 1: System initialization
	[5, "Initializing EdenRP systems..."] call EDRP_fnc_updateLoadingProgress;
	sleep 0.5;
	
	// Phase 2: Server connection
	[15, "Connecting to server..."] call EDRP_fnc_updateLoadingProgress;
	sleep 1;
	
	// Phase 3: Player data request
	[25, "Requesting player data..."] call EDRP_fnc_updateLoadingProgress;
	waitUntil {EDRP_session_completed};
	
	// Phase 4: Player setup
	[45, "Setting up player..."] call EDRP_fnc_updateLoadingProgress;
	sleep 0.5;
	
	// Phase 5: Faction initialization
	[55, "Initializing faction systems..."] call EDRP_fnc_updateLoadingProgress;
	sleep 0.5;
	
	// Phase 6: Spawn selection
	[65, "Select spawn location..."] call EDRP_fnc_updateLoadingProgress;
	call EDRP_fnc_spawnSelection;
	
	// Phase 7: UI initialization
	[75, "Loading user interface..."] call EDRP_fnc_updateLoadingProgress;
	sleep 0.5;
	
	// Phase 8: Event handlers
	[85, "Setting up event handlers..."] call EDRP_fnc_updateLoadingProgress;
	sleep 0.5;
	
	// Phase 9: Final setup
	[95, "Finalizing setup..."] call EDRP_fnc_updateLoadingProgress;
	sleep 0.5;
	
	// Phase 10: Complete
	[100, "Welcome to EdenRP!"] call EDRP_fnc_updateLoadingProgress;
	sleep 1;
	
	// Hide loading screen
	EDRP_loading_active = false;
};

// Error handling for loading screen
EDRP_fnc_loadingError = {
	params ["_errorMessage"];
	
	EDRP_loading_status = format ["<t color='#FF0000'>ERROR: %1</t>", _errorMessage];
	
	// Show error for 5 seconds then retry or exit
	sleep 5;
	
	// Attempt recovery
	if (EDRP_loading_progress < 50) then {
		// Early stage error - retry initialization
		EDRP_loading_progress = 0;
		EDRP_loading_status = "Retrying initialization...";
		[] spawn EDRP_fnc_loadingPhases;
	} else {
		// Late stage error - emergency spawn
		EDRP_loading_status = "Emergency spawn in progress...";
		call EDRP_fnc_emergencyRespawn;
		EDRP_loading_active = false;
	};
};

// Loading screen intro sequence
EDRP_fnc_introSequence = {
	if (!EDRP_loading_active) then {
		// Show intro cutscene
		[] spawn {
			// Fade from black
			titleCut ["", "BLACK FADED", 999];
			sleep 1;
			titleCut ["", "BLACK IN", 3];
			
			// Show server name
			[
				[
					["EdenRP", "<t align = 'center' shadow = '1' size = '0.8' font='PuristaBold'>%1</t>", 15],
					["", "<br/>", 1],
					["Altis Life Roleplay", "<t align = 'center' shadow = '1' size = '0.6'>%1</t>", 15]
				]
			] spawn BIS_fnc_typeText;
			
			sleep 5;
			
			// Show faction-specific intro
			switch (playerSide) do {
				case west: {
					[
						[
							["Welcome Officer", "<t align = 'center' shadow = '1' size = '0.7' color='#0080FF'>%1</t>", 15],
							["", "<br/>", 1],
							["Protect and Serve", "<t align = 'center' shadow = '1' size = '0.5'>%1</t>", 15]
						]
					] spawn BIS_fnc_typeText;
				};
				case independent: {
					[
						[
							["Welcome Medic", "<t align = 'center' shadow = '1' size = '0.7' color='#FF8000'>%1</t>", 15],
							["", "<br/>", 1],
							["Save Lives", "<t align = 'center' shadow = '1' size = '0.5'>%1</t>", 15]
						]
					] spawn BIS_fnc_typeText;
				};
				case civilian: {
					[
						[
							["Welcome Citizen", "<t align = 'center' shadow = '1' size = '0.7' color='#00FF00'>%1</t>", 15],
							["", "<br/>", 1],
							["Live Your Life", "<t align = 'center' shadow = '1' size = '0.5'>%1</t>", 15]
						]
					] spawn BIS_fnc_typeText;
				};
			};
			
			sleep 5;
			
			// Clear text
			titleText ["", "PLAIN"];
		};
	};
};

// Initialize loading screen on client
if (hasInterface) then {
	[] spawn {
		// Wait for basic initialization
		waitUntil {!isNull player};
		
		// Start loading screen
		call EDRP_fnc_initLoadingScreen;
		
		// Run loading phases
		[] spawn EDRP_fnc_loadingPhases;
	};
};

diag_log "EdenRP: Loading screen system loaded";
