/*
	EdenRP Altis Life - Market System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Handles all market transactions and dynamic pricing
	Version: 1.0.0
*/

// Sell items at market
EDRP_fnc_sellAtMarket = {
	params [
		["_vendor", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
		["_player", player, [obj<PERSON><PERSON>]],
		["_id", -1, [0]],
		["_type", "", [""]]
	];
	
	if (EDRP_action_inUse) exitWith {["You are currently doing something else"] call EDRP_fnc_hint;};
	
	// Get market configuration
	private _marketConfig = [_type] call EDRP_fnc_getMarketConfig;
	if (_marketConfig isEqualTo []) exitWith {["This market doesn't buy that item"] call EDRP_fnc_hint;};
	
	_marketConfig params ["_itemName", "_basePrice", "_priceVariation", "_maxQuantity"];
	
	// Check if player has the item
	private _playerQuantity = [_type] call EDRP_fnc_getItemQuantity;
	if (_playerQuantity <= 0) exitWith {
		[format ["You don't have any %1 to sell", _itemName]] call EDRP_fnc_hint;
	};
	
	// Calculate current market price (with dynamic pricing)
	private _currentPrice = [_type] call EDRP_fnc_getCurrentMarketPrice;
	
	// Determine quantity to sell (all or max market capacity)
	private _quantityToSell = _playerQuantity min _maxQuantity;
	
	// Calculate total payment
	private _totalPayment = _currentPrice * _quantityToSell;
	
	// Apply skill bonuses
	private _skillBonus = [_type] call EDRP_fnc_getSellingSkillBonus;
	_totalPayment = _totalPayment * _skillBonus;
	
	// Confirm sale dialog
	private _confirmText = format [
		"Sell %1x %2 for $%3 each?\nTotal: $%4",
		_quantityToSell,
		_itemName,
		[_currentPrice] call EDRP_fnc_numberText,
		[_totalPayment] call EDRP_fnc_numberText
	];
	
	if ([_confirmText, "Confirm Sale", true, true] call EDRP_fnc_messageBox) then {
		// Remove items from inventory
		if ([_type, _quantityToSell] call EDRP_fnc_removeItem) then {
			// Add money
			EDRP_player_cash = EDRP_player_cash + _totalPayment;
			
			// Update market data
			[_type, _quantityToSell] call EDRP_fnc_updateMarketSupply;
			
			// Show success message
			[format ["Sold %1x %2 for $%3", _quantityToSell, _itemName, [_totalPayment] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
			
			// Add trading XP
			["trading", _quantityToSell * 2] call EDRP_fnc_addJobXP;
			
			// Play cash register sound
			playSound "cash_register";
			
			// Update statistics
			EDRP_job_stats set ["total_sold", (EDRP_job_stats get "total_sold") + _quantityToSell];
			EDRP_job_stats set ["total_earned", (EDRP_job_stats get "total_earned") + _totalPayment];
			
			// Log transaction
			[format ["Sold %1x %2 for $%3 at %4", _quantityToSell, _type, _totalPayment, _vendor]] call EDRP_fnc_logInfo;
		} else {
			["Failed to remove items from inventory"] call EDRP_fnc_hint;
		};
	};
};

// Get market configuration for item
EDRP_fnc_getMarketConfig = {
	params [["_itemType", "", [""]]];
	
	private _configs = createHashMapFromArray [
		// Format: [displayName, basePrice, priceVariation%, maxQuantity]
		["apple", ["Apple", 25, 20, 50]],
		["peach", ["Peach", 30, 20, 50]],
		["copperingot", ["Copper Ingot", 1450, 15, 25]],
		["ironingot", ["Iron Ingot", 2250, 15, 20]],
		["saltrefined", ["Refined Salt", 1850, 10, 30]],
		["diamondcut", ["Cut Diamond", 3500, 25, 5]],
		["goldbar", ["Gold Bar", 2800, 20, 10]],
		["oilprocessed", ["Processed Oil", 2100, 18, 15]],
		["fishcooked", ["Cooked Fish", 85, 15, 40]],
		
		// Illegal items (higher prices, more variation)
		["marijuanaprocessed", ["Processed Marijuana", 2500, 30, 10]],
		["cocaineprocessed", ["Processed Cocaine", 4200, 35, 8]],
		["heroinprocessed", ["Processed Heroin", 5800, 40, 5]]
	];
	
	_configs getOrDefault [_itemType, []]
};

// Get current market price with dynamic pricing
EDRP_fnc_getCurrentMarketPrice = {
	params [["_itemType", "", [""]]];
	
	private _config = [_itemType] call EDRP_fnc_getMarketConfig;
	if (_config isEqualTo []) exitWith { 0 };
	
	_config params ["_itemName", "_basePrice", "_priceVariation", "_maxQuantity"];
	
	// Get market supply data (simulated for now)
	private _marketSupply = missionNamespace getVariable [format ["EDRP_market_supply_%1", _itemType], 50];
	private _demandMultiplier = (100 - _marketSupply) / 100; // Higher demand = higher price
	
	// Calculate price variation
	private _variation = (_priceVariation / 100) * _demandMultiplier;
	private _currentPrice = _basePrice * (1 + _variation);
	
	// Add random market fluctuation (±5%)
	private _randomFactor = 0.95 + (random 0.1);
	_currentPrice = _currentPrice * _randomFactor;
	
	round _currentPrice
};

// Get selling skill bonus
EDRP_fnc_getSellingSkillBonus = {
	params [["_itemType", "", [""]]];
	
	private _tradingLevel = EDRP_job_skills get "trading";
	private _baseBonus = 1.0;
	
	// Trading skill provides up to 15% bonus
	private _skillBonus = _baseBonus + (_tradingLevel * 0.015);
	
	// Item-specific skill bonuses
	private _itemSkillBonus = switch (_itemType) do {
		case "copperingot"; case "ironingot"; case "goldbar": {
			private _smeltingLevel = EDRP_job_skills get "smelting";
			1.0 + (_smeltingLevel * 0.01); // 1% per level
		};
		case "saltrefined"; case "oilprocessed": {
			private _refiningLevel = EDRP_job_skills get "refining";
			1.0 + (_refiningLevel * 0.01);
		};
		case "diamondcut": {
			private _gemcuttingLevel = EDRP_job_skills get "gemcutting";
			1.0 + (_gemcuttingLevel * 0.02); // 2% per level (rare skill)
		};
		case "fishcooked": {
			private _cookingLevel = EDRP_job_skills get "cooking";
			1.0 + (_cookingLevel * 0.01);
		};
		default { 1.0 };
	};
	
	_skillBonus * _itemSkillBonus
};

// Update market supply (affects future prices)
EDRP_fnc_updateMarketSupply = {
	params [
		["_itemType", "", [""]],
		["_quantitySold", 0, [0]]
	];
	
	private _currentSupply = missionNamespace getVariable [format ["EDRP_market_supply_%1", _itemType], 50];
	private _newSupply = (_currentSupply + _quantitySold) min 100;
	
	missionNamespace setVariable [format ["EDRP_market_supply_%1", _itemType], _newSupply];
	
	// Sync to server for persistence
	[_itemType, _newSupply] remoteExec ["EDRP_fnc_updateMarketSupplyServer", 2];
};

// Get available markets near player
EDRP_fnc_getNearbyMarkets = {
	params [["_range", 50, [0]]];
	
	private _nearbyMarkets = [];
	private _playerPos = getPos player;
	
	// Check all market markers
	private _marketMarkers = [
		"market_copper", "market_iron", "market_salt",
		"market_diamond", "market_gold", "market_oil",
		"market_fish", "market_general",
		"market_illegal_1", "market_illegal_2"
	];
	
	{
		private _markerPos = getMarkerPos _x;
		if (_playerPos distance2D _markerPos <= _range) then {
			private _marketType = [_x] call EDRP_fnc_getMarketType;
			_nearbyMarkets pushBack [_x, _marketType, _playerPos distance2D _markerPos];
		};
	} forEach _marketMarkers;
	
	// Sort by distance
	_nearbyMarkets sort true;
	
	_nearbyMarkets
};

// Get market type from marker name
EDRP_fnc_getMarketType = {
	params [["_markerName", "", [""]]];
	
	private _marketType = "";
	
	switch (true) do {
		case (_markerName find "copper" >= 0): { _marketType = "copper"; };
		case (_markerName find "iron" >= 0): { _marketType = "iron"; };
		case (_markerName find "salt" >= 0): { _marketType = "salt"; };
		case (_markerName find "diamond" >= 0): { _marketType = "diamond"; };
		case (_markerName find "gold" >= 0): { _marketType = "gold"; };
		case (_markerName find "oil" >= 0): { _marketType = "oil"; };
		case (_markerName find "fish" >= 0): { _marketType = "fish"; };
		case (_markerName find "general" >= 0): { _marketType = "general"; };
		case (_markerName find "illegal" >= 0): { _marketType = "illegal"; };
	};
	
	_marketType
};

// Initialize market system
EDRP_fnc_initMarketSystem = {
	// Initialize market supply values
	private _items = ["apple", "peach", "copperingot", "ironingot", "saltrefined", "diamondcut", "goldbar", "oilprocessed", "fishcooked"];
	{
		missionNamespace setVariable [format ["EDRP_market_supply_%1", _x], 50 + (random 30)];
	} forEach _items;
	
	// Start market fluctuation system
	[] spawn EDRP_fnc_marketFluctuationLoop;
	
	["Market system initialized"] call EDRP_fnc_logInfo;
};

// Market fluctuation loop (simulates supply/demand changes)
EDRP_fnc_marketFluctuationLoop = {
	while {true} do {
		sleep 300; // Every 5 minutes
		
		// Gradually reduce supply (simulate consumption)
		private _items = ["apple", "peach", "copperingot", "ironingot", "saltrefined", "diamondcut", "goldbar", "oilprocessed", "fishcooked"];
		{
			private _currentSupply = missionNamespace getVariable [format ["EDRP_market_supply_%1", _x], 50];
			private _newSupply = (_currentSupply - (1 + random 3)) max 0;
			missionNamespace setVariable [format ["EDRP_market_supply_%1", _x], _newSupply];
		} forEach _items;
	};
};

// Initialize market system on client
if (hasInterface) then {
	[] call EDRP_fnc_initMarketSystem;
};
