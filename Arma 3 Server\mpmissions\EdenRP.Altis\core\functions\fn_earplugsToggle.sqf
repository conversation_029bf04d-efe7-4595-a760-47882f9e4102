//Default settings: 40%, 10%

if (eden_earplugs) then {
	if (eden_earVol) then {
		eden_earVol = false;
		1 fadeSound ((life_earplugs select 0) / 100);
		hint format["Earplugs partially inserted. %1%2", (life_earplugs select 0), "%"];
	} else {
		eden_earplugs = false;
		hint format["Earplugs fully inserted. %1%2", (life_earplugs select 1), "%"];
		1 fadeSound ((life_earplugs select 1) / 100);
	};
} else {
	eden_earplugs = true;
	eden_earVol = true;
	hint "Earplugs removed";
	1 fadeSound 1;
};
[] call EDEN_fnc_hudUpdate;
