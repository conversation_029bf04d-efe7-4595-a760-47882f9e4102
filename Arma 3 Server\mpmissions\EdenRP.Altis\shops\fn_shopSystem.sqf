/*
	EdenRP Altis Life - Market and Shop System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Dynamic market system with supply/demand pricing
	Version: 1.0.0
*/

// Initialize shop system
EDRP_fnc_initShopSystem = {
	// Shop state variables
	EDRP_market_prices = createHashMap;
	EDRP_market_stock = createHashMap;
	EDRP_market_demand = createHashMap;
	EDRP_player_transactions = [];
	EDRP_shop_discounts = createHashMap;
	
	// Shopping statistics
	EDRP_shopping_stats = createHashMapFromArray [
		["items_bought", 0],
		["items_sold", 0],
		["money_spent", 0],
		["money_earned", 0],
		["best_deal", 0]
	];
	
	// Load shop configuration
	[] call EDRP_fnc_loadShopConfig;
	
	// Initialize market prices
	[] call EDRP_fnc_initializeMarketPrices;
	
	["Shop system initialized"] call EDRP_fnc_logInfo;
};

// Load shop configuration
EDRP_fnc_loadShopConfig = {
	// General stores
	EDRP_general_stores = [
		["Kavala General Store", [3665.39, 13220.1, 0], "general"],
		["Pyrgos General Store", [16019.5, 16952.9, 0], "general"],
		["Athira General Store", [14707.1, 16835.5, 0], "general"],
		["Sofia General Store", [4022.15, 11669.5, 0], "general"]
	];
	
	// Clothing stores
	EDRP_clothing_stores = [
		["Kavala Clothing", [3688.45, 13208.7, 0], "clothing"],
		["Pyrgos Outfitters", [16028.3, 16945.2, 0], "clothing"],
		["Athira Fashion", [14720.8, 16828.9, 0], "clothing"]
	];
	
	// Vehicle dealerships
	EDRP_vehicle_dealers = [
		["Kavala Motors", [3542.87, 13216.3, 0], "vehicle"],
		["Pyrgos Auto", [15876.8, 16972.1, 0], "vehicle"],
		["Athira Vehicles", [14846.1, 16834.7, 0], "vehicle"]
	];
	
	// Weapon shops (police/rebel)
	EDRP_weapon_shops = [
		["Police Armory", [3542.87, 13216.3, 0], "police"],
		["Rebel Outpost", [18312.8, 15855.4, 0], "rebel"]
	];
	
	// Market categories and items
	EDRP_market_categories = createHashMapFromArray [
		["food", [
			["apple", "Apple", 15, 25, 1000, 0.8],
			["peach", "Peach", 18, 30, 800, 0.9],
			["water_bottle", "Water Bottle", 10, 20, 2000, 0.7],
			["energy_drink", "Energy Drink", 25, 40, 500, 1.2],
			["protein_bar", "Protein Bar", 30, 50, 300, 1.5]
		]],
		["tools", [
			["pickaxe", "Pickaxe", 750, 1200, 50, 2.0],
			["shovel", "Shovel", 350, 600, 100, 1.8],
			["toolkit", "Toolkit", 1500, 2500, 25, 2.5],
			["lockpick", "Lockpick", 150, 300, 200, 1.5],
			["bolt_cutter", "Bolt Cutter", 2500, 4000, 10, 3.0]
		]],
		["medical", [
			["first_aid_kit", "First Aid Kit", 150, 250, 500, 1.3],
			["bandage", "Bandage", 25, 50, 1000, 1.0],
			["morphine", "Morphine", 500, 800, 100, 2.0],
			["epinephrine", "Epinephrine", 750, 1200, 50, 2.5],
			["blood_bag", "Blood Bag", 1000, 1500, 25, 3.0]
		]],
		["electronics", [
			["phone", "Cell Phone", 500, 800, 200, 1.5],
			["radio", "Radio", 750, 1200, 150, 1.8],
			["gps", "GPS Device", 1200, 2000, 100, 2.0],
			["laptop", "Laptop", 2500, 4000, 50, 2.5],
			["tablet", "Tablet", 1800, 3000, 75, 2.2]
		]],
		["clothing", [
			["civilian_uniform", "Civilian Clothes", 50, 100, 1000, 1.0],
			["business_suit", "Business Suit", 250, 500, 200, 1.5],
			["work_clothes", "Work Clothes", 100, 200, 500, 1.2],
			["winter_jacket", "Winter Jacket", 150, 300, 300, 1.3],
			["sports_outfit", "Sports Outfit", 80, 150, 400, 1.1]
		]]
	];
	
	// Processing materials
	EDRP_processing_materials = createHashMapFromArray [
		["raw_materials", [
			["iron_ore", "Iron Ore", 45, 75, 2000, 1.0],
			["copper_ore", "Copper Ore", 65, 110, 1500, 1.2],
			["salt", "Salt", 25, 45, 3000, 0.8],
			["sand", "Sand", 15, 30, 5000, 0.6],
			["diamond_uncut", "Uncut Diamond", 2500, 4000, 50, 3.0],
			["oil_unprocessed", "Crude Oil", 150, 250, 1000, 1.5]
		]],
		["processed_goods", [
			["iron_refined", "Refined Iron", 120, 200, 1000, 1.5],
			["copper_refined", "Refined Copper", 180, 300, 750, 1.8],
			["salt_refined", "Refined Salt", 65, 110, 1500, 1.2],
			["glass", "Glass", 45, 80, 2000, 1.0],
			["diamond_cut", "Cut Diamond", 6000, 10000, 25, 4.0],
			["oil_processed", "Processed Oil", 400, 650, 500, 2.0]
		]]
	];
	
	// Shop access permissions
	EDRP_shop_permissions = createHashMapFromArray [
		["general", ["civilian", "police", "medical"]],
		["clothing", ["civilian", "police", "medical"]],
		["vehicle", ["civilian"]],
		["police", ["police"]],
		["medical", ["medical"]],
		["rebel", ["civilian"]]
	];
	
	// Bulk purchase discounts
	EDRP_bulk_discounts = [
		[10, 0.05], // 5% discount for 10+ items
		[25, 0.10], // 10% discount for 25+ items
		[50, 0.15], // 15% discount for 50+ items
		[100, 0.20] // 20% discount for 100+ items
	];
};

// Initialize market prices
EDRP_fnc_initializeMarketPrices = {
	// Set initial prices and stock levels
	{
		private _category = _x;
		private _items = EDRP_market_categories get _category;
		
		{
			_x params ["_item", "_name", "_minPrice", "_maxPrice", "_maxStock", "_volatility"];
			
			// Set initial price (random between min and max)
			private _initialPrice = _minPrice + random(_maxPrice - _minPrice);
			EDRP_market_prices set [_item, _initialPrice];
			
			// Set initial stock (80-100% of max)
			private _initialStock = round(_maxStock * (0.8 + random 0.2));
			EDRP_market_stock set [_item, _initialStock];
			
			// Set initial demand (neutral)
			EDRP_market_demand set [_item, 1.0];
		} forEach _items;
	} forEach (keys EDRP_market_categories);
	
	// Initialize processing materials
	{
		private _category = _x;
		private _items = EDRP_processing_materials get _category;
		
		{
			_x params ["_item", "_name", "_minPrice", "_maxPrice", "_maxStock", "_volatility"];
			
			private _initialPrice = _minPrice + random(_maxPrice - _minPrice);
			EDRP_market_prices set [_item, _initialPrice];
			
			private _initialStock = round(_maxStock * (0.8 + random 0.2));
			EDRP_market_stock set [_item, _initialStock];
			
			EDRP_market_demand set [_item, 1.0];
		} forEach _items;
	} forEach (keys EDRP_processing_materials);
};

// Open shop menu
EDRP_fnc_openShopMenu = {
	params [["_shopType", "general", [""]], ["_shopName", "General Store", [""]]];
	
	// Check shop access permissions
	private _allowedFactions = EDRP_shop_permissions get _shopType;
	if (isNil "_allowedFactions") exitWith {
		["Invalid shop type"] call EDRP_fnc_hint;
		false
	};
	
	private _playerFaction = [player] call EDRP_fnc_getPlayerFaction;
	if !(_playerFaction in _allowedFactions) exitWith {
		["You don't have access to this shop"] call EDRP_fnc_hint;
		false
	};
	
	// Store current shop info
	EDRP_current_shop_type = _shopType;
	EDRP_current_shop_name = _shopName;
	
	// Create shop dialog
	createDialog "EDRP_ShopDialog";
	
	// Update shop display
	[] call EDRP_fnc_updateShopMenu;
	
	true
};

// Update shop menu
EDRP_fnc_updateShopMenu = {
	private _display = findDisplay 55000;
	if (isNull _display) exitWith {};
	
	// Update shop info
	private _shopInfoCtrl = _display displayCtrl 55001;
	private _shopInfo = format [
		"Shop: %1\nType: %2\nYour Money: $%3\nDiscount: %4%%",
		EDRP_current_shop_name,
		EDRP_current_shop_type,
		[EDRP_player_bank] call EDRP_fnc_numberText,
		round((EDRP_shop_discounts getOrDefault [EDRP_current_shop_type, 0]) * 100)
	];
	_shopInfoCtrl ctrlSetText _shopInfo;
	
	// Update items list based on shop type
	private _itemsList = _display displayCtrl 55002;
	lbClear _itemsList;
	
	private _shopItems = [];
	switch (EDRP_current_shop_type) do {
		case "general": {
			_shopItems = (EDRP_market_categories get "food") + (EDRP_market_categories get "tools") + (EDRP_market_categories get "electronics");
		};
		case "clothing": {
			_shopItems = EDRP_market_categories get "clothing";
		};
		case "medical": {
			_shopItems = EDRP_market_categories get "medical";
		};
		case "vehicle": {
			// Vehicle shop handled separately
			[] call EDRP_fnc_updateVehicleShop;
			exitWith {};
		};
		default {
			_shopItems = EDRP_market_categories get "food";
		};
	};
	
	// Add items to list
	{
		_x params ["_item", "_name", "_minPrice", "_maxPrice", "_maxStock", "_volatility"];
		
		private _currentPrice = EDRP_market_prices get _item;
		private _currentStock = EDRP_market_stock get _item;
		private _demand = EDRP_market_demand get _item;
		
		// Apply shop discount
		private _discount = EDRP_shop_discounts getOrDefault [EDRP_current_shop_type, 0];
		private _finalPrice = round(_currentPrice * (1 - _discount));
		
		// Create entry with market info
		private _priceIndicator = if (_currentPrice > (_minPrice + _maxPrice) / 2) then { "↑" } else { "↓" };
		private _stockIndicator = if (_currentStock < _maxStock * 0.3) then { "LOW" } else { 
			if (_currentStock > _maxStock * 0.8) then { "HIGH" } else { "MED" }
		};
		
		private _entry = format ["%1 - $%2 %3 [Stock: %4 %5]", 
			_name, 
			[_finalPrice] call EDRP_fnc_numberText, 
			_priceIndicator,
			_currentStock,
			_stockIndicator
		];
		
		_itemsList lbAdd _entry;
		_itemsList lbSetData [_forEachIndex, _item];
		_itemsList lbSetValue [_forEachIndex, _finalPrice];
		
		// Color code by stock level
		if (_currentStock == 0) then {
			_itemsList lbSetColor [_forEachIndex, [1, 0, 0, 1]]; // Red for out of stock
		} else {
			if (_currentStock < _maxStock * 0.3) then {
				_itemsList lbSetColor [_forEachIndex, [1, 0.5, 0, 1]]; // Orange for low stock
			} else {
				_itemsList lbSetColor [_forEachIndex, [1, 1, 1, 1]]; // White for normal stock
			};
		};
	} forEach _shopItems;
	
	// Update market trends
	private _trendsCtrl = _display displayCtrl 55003;
	private _trendsText = "Market Trends:\n";
	
	// Show top 5 trending items
	private _trendingItems = [];
	{
		private _item = _x;
		private _demand = EDRP_market_demand get _item;
		_trendingItems pushBack [_item, _demand];
	} forEach (keys EDRP_market_demand);
	
	_trendingItems sort false; // Sort by demand (descending)
	
	for "_i" from 0 to (4 min (count _trendingItems - 1)) do {
		(_trendingItems select _i) params ["_item", "_demand"];
		private _itemName = [_item] call EDRP_fnc_getItemName;
		private _trend = if (_demand > 1.2) then { "🔥 HOT" } else {
			if (_demand < 0.8) then { "❄️ COLD" } else { "📈 STABLE" }
		};
		_trendsText = _trendsText + format ["%1: %2\n", _itemName, _trend];
	};
	
	_trendsCtrl ctrlSetText _trendsText;
};

// Buy item from shop
EDRP_fnc_buyItem = {
	params [["_item", "", [""]], ["_quantity", 1, [0]], ["_price", 0, [0]]];
	
	if (_item == "" || _quantity <= 0) exitWith {
		["Invalid item or quantity"] call EDRP_fnc_hint;
		false
	};
	
	// Check stock
	private _currentStock = EDRP_market_stock get _item;
	if (isNil "_currentStock" || _currentStock < _quantity) exitWith {
		["Insufficient stock"] call EDRP_fnc_hint;
		false
	};
	
	// Calculate total cost with bulk discount
	private _bulkDiscount = 0;
	{
		_x params ["_minQty", "_discount"];
		if (_quantity >= _minQty) then {
			_bulkDiscount = _discount;
		};
	} forEach EDRP_bulk_discounts;
	
	private _totalCost = round(_price * _quantity * (1 - _bulkDiscount));
	
	// Check if player has enough money
	if (EDRP_player_bank < _totalCost) exitWith {
		[format ["You need $%1 to buy this", [_totalCost] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
		false
	};
	
	// Check inventory space
	if !([_item, _quantity] call EDRP_fnc_canAddItem) exitWith {
		["Not enough inventory space"] call EDRP_fnc_hint;
		false
	};
	
	// Process purchase
	EDRP_player_bank = EDRP_player_bank - _totalCost;
	[_item, _quantity] call EDRP_fnc_addItem;
	
	// Update market
	EDRP_market_stock set [_item, _currentStock - _quantity];
	
	// Increase demand slightly
	private _currentDemand = EDRP_market_demand get _item;
	EDRP_market_demand set [_item, _currentDemand + (_quantity * 0.01)];
	
	// Update statistics
	EDRP_shopping_stats set ["items_bought", (EDRP_shopping_stats get "items_bought") + _quantity];
	EDRP_shopping_stats set ["money_spent", (EDRP_shopping_stats get "money_spent") + _totalCost];
	
	// Record transaction
	EDRP_player_transactions pushBack ["BUY", _item, _quantity, _totalCost, time];
	
	// Show purchase confirmation
	private _message = format ["Purchased %1 x%2 for $%3", [_item] call EDRP_fnc_getItemName, _quantity, [_totalCost] call EDRP_fnc_numberText];
	if (_bulkDiscount > 0) then {
		_message = _message + format [" (Bulk discount: %1%%)", round(_bulkDiscount * 100)];
	};
	[_message, "success"] call EDRP_fnc_hint;
	
	// Update display
	[] call EDRP_fnc_updateShopMenu;
	
	true
};

// Sell item to shop
EDRP_fnc_sellItem = {
	params [["_item", "", [""]], ["_quantity", 1, [0]]];
	
	if (_item == "" || _quantity <= 0) exitWith {
		["Invalid item or quantity"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player has the item
	if !([_item, _quantity] call EDRP_fnc_hasItem) exitWith {
		["You don't have enough of this item"] call EDRP_fnc_hint;
		false
	};
	
	// Calculate sell price (70% of current market price)
	private _marketPrice = EDRP_market_prices get _item;
	if (isNil "_marketPrice") exitWith {
		["This item cannot be sold here"] call EDRP_fnc_hint;
		false
	};
	
	private _sellPrice = round(_marketPrice * 0.7);
	private _totalEarnings = _sellPrice * _quantity;
	
	// Process sale
	[_item, _quantity] call EDRP_fnc_removeItem;
	EDRP_player_bank = EDRP_player_bank + _totalEarnings;
	
	// Update market
	private _currentStock = EDRP_market_stock get _item;
	EDRP_market_stock set [_item, _currentStock + _quantity];
	
	// Decrease demand slightly
	private _currentDemand = EDRP_market_demand get _item;
	EDRP_market_demand set [_item, _currentDemand - (_quantity * 0.005)];
	
	// Update statistics
	EDRP_shopping_stats set ["items_sold", (EDRP_shopping_stats get "items_sold") + _quantity];
	EDRP_shopping_stats set ["money_earned", (EDRP_shopping_stats get "money_earned") + _totalEarnings];
	
	// Record transaction
	EDRP_player_transactions pushBack ["SELL", _item, _quantity, _totalEarnings, time];
	
	[format ["Sold %1 x%2 for $%3", [_item] call EDRP_fnc_getItemName, _quantity, [_totalEarnings] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
	
	// Update display
	[] call EDRP_fnc_updateShopMenu;
	
	true
};

// Update market prices (called periodically)
EDRP_fnc_updateMarketPrices = {
	{
		private _item = _x;
		private _currentPrice = EDRP_market_prices get _item;
		private _demand = EDRP_market_demand get _item;
		private _stock = EDRP_market_stock get _item;
		
		// Find item config for min/max prices
		private _itemConfig = [];
		{
			private _category = _x;
			private _items = EDRP_market_categories get _category;
			if (isNil "_items") then {
				_items = EDRP_processing_materials get _category;
			};
			
			if (!isNil "_items") then {
				{
					if ((_x select 0) == _item) exitWith {
						_itemConfig = _x;
					};
				} forEach _items;
			};
		} forEach ((keys EDRP_market_categories) + (keys EDRP_processing_materials));
		
		if (count _itemConfig > 0) then {
			_itemConfig params ["", "", "_minPrice", "_maxPrice", "_maxStock", "_volatility"];
			
			// Calculate price change based on supply and demand
			private _supplyFactor = 1 - (_stock / _maxStock); // Low stock = higher prices
			private _demandFactor = _demand - 1; // High demand = higher prices
			private _randomFactor = (random 0.2) - 0.1; // +/- 10% random variation
			
			private _priceChange = (_supplyFactor + _demandFactor + _randomFactor) * _volatility * 0.1;
			private _newPrice = _currentPrice * (1 + _priceChange);
			
			// Clamp to min/max prices
			_newPrice = _newPrice max _minPrice;
			_newPrice = _newPrice min _maxPrice;
			
			EDRP_market_prices set [_item, round(_newPrice)];
			
			// Gradually return demand to neutral
			private _newDemand = _demand * 0.99; // 1% decay per update
			if (_newDemand < 0.5) then { _newDemand = 0.5; };
			if (_newDemand > 2.0) then { _newDemand = 2.0; };
			EDRP_market_demand set [_item, _newDemand];
		};
	} forEach (keys EDRP_market_prices);
};

// Add shop actions
EDRP_fnc_addShopActions = {
	// General stores
	{
		_x params ["_name", "_position", "_type"];
		
		private _shopObj = createSimpleObject ["Land_Kiosk_papers_F", _position];
		_shopObj addAction [
			format ["<t color='#00FF00'>Shop at %1</t>", _name],
			{
				params ["_target", "_caller", "_actionId", "_arguments"];
				_arguments params ["_shopType", "_shopName"];
				[_shopType, _shopName] call EDRP_fnc_openShopMenu;
			},
			[_type, _name],
			5,
			true,
			true,
			"",
			"true"
		];
	} forEach EDRP_general_stores;
	
	// Clothing stores
	{
		_x params ["_name", "_position", "_type"];
		
		private _shopObj = createSimpleObject ["Land_Rack_F", _position];
		_shopObj addAction [
			format ["<t color='#FF8000'>Shop at %1</t>", _name],
			{
				params ["_target", "_caller", "_actionId", "_arguments"];
				_arguments params ["_shopType", "_shopName"];
				[_shopType, _shopName] call EDRP_fnc_openShopMenu;
			},
			[_type, _name],
			5,
			true,
			true,
			"",
			"true"
		];
	} forEach EDRP_clothing_stores;
};

// Start market update loop
EDRP_fnc_startMarketUpdates = {
	[] spawn {
		while { true } do {
			sleep 300; // Update every 5 minutes
			[] call EDRP_fnc_updateMarketPrices;
		};
	};
};

// Initialize shop system on client
if (hasInterface) then {
	[] call EDRP_fnc_initShopSystem;
	
	// Add shop actions
	[] call EDRP_fnc_addShopActions;
	
	// Start market updates
	[] call EDRP_fnc_startMarketUpdates;
};
