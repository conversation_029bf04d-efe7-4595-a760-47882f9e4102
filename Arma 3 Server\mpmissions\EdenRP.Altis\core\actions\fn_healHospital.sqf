//  File: fn_healHospital.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Modified: <PERSON> "tkc<PERSON>" Schultz

//	Description: Script used on hospital NPCs to heal players back to full health
if (eden_action_inUse) exitWith {};
if (isNil "eden_cash") then {eden_cash = 0; eden_cache_cash = eden_random_cash_val;};
if (isNil "eden_atmcash") then {eden_atmcash = 0; eden_cache_atmcash = eden_random_cash_val;};
if((eden_cash + (eden_random_cash_val - 5000)) > eden_cache_cash || (eden_atmcash + (eden_random_cash_val - 5000)) > eden_cache_atmcash) exitWith {
	[["event","Hacked Cash"],["player",name player],["player_id",getPlayerUID player],["hackedcash",eden_cash - (eden_cache_cash - eden_random_cash_val)],["hackedbank",eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)],["location",getPos player]] call EDEN_fnc_logIt;
	[[profileName,format["Hacked Cash Detected! (Cash Hacked In = %1) (Bank Hacked In = %2)",eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDEN_fnc_notifyAdmins",-2,false] spawn EDEN_fnc_MP;
	[[1,player,[eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDENS_fnc_handleDisc",false,false] spawn EDEN_fnc_MP;
	["HackedMoney",false,false] call compile PreProcessFileLineNumbers "\a3\functions_f\Misc\fn_endMission.sqf";
};
if (eden_atmcash < 1000 && eden_atmcash < 1000) exitWith {hint format[localize "STR_NOTF_HS_NoCash",[1000] call EDEN_fnc_numberText];};

eden_action_inUse = true;
_action = [
	"Spend $1,000 to be fully healed?",
	"EdenRP Medical Doctor",
	localize "STR_Global_Yes",
	localize "STR_Global_No"
] call BIS_fnc_guiMessage;

if(_action) then {
	titleText[localize "STR_NOTF_HS_Healing","PLAIN DOWN"];
	closeDialog 0;
	uiSleep 6.3;
	if(player distance (_this select 0) > 5) exitWith {eden_action_inUse = false; titleText[localize "STR_NOTF_HS_ToFar","PLAIN DOWN"]};
	player setVariable["kidneyRemoved",false,true];
	uisleep 1.7;
	titleText[localize "STR_NOTF_HS_Healed","PLAIN DOWN"];
	private _dam_obj = player;
	_dam_obj setDamage 0;
	eden_hunger = 100;
	eden_thirst = 100;
	[] call EDEN_fnc_hudUpdate;
	if (eden_cash >= 1000) then {
		eden_cash = eden_cash - 1000;
		eden_cache_cash = eden_cache_cash - 1000;
	} else {
		eden_atmcash = eden_atmcash - 1000;
		eden_cache_atmcash = eden_cache_atmcash - 1000;
	};
	eden_action_inUse = false;
} else {
	hint localize "STR_NOTF_ActionCancel";
	closeDialog 0;
	eden_action_inUse = false;
};
