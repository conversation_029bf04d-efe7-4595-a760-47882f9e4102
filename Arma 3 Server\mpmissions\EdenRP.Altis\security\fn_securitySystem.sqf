/*
	EdenRP Altis Life - Security and Anti-Cheat System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Comprehensive security measures and cheat detection
	Version: 1.0.0
*/

// Initialize security system
EDRP_fnc_initSecuritySystem = {
	// Security state variables
	EDRP_security_enabled = true;
	EDRP_player_violations = [];
	EDRP_security_logs = [];
	EDRP_last_position = [0, 0, 0];
	EDRP_last_money_check = 0;
	EDRP_last_inventory_check = [];
	EDRP_movement_history = [];
	
	// Security statistics
	EDRP_security_stats = createHashMapFromArray [
		["violations_detected", 0],
		["players_banned", 0],
		["teleport_detections", 0],
		["speed_detections", 0],
		["money_violations", 0],
		["item_violations", 0]
	];
	
	// Load security configuration
	[] call EDRP_fnc_loadSecurityConfig;
	
	// Start security monitoring
	[] call EDRP_fnc_startSecurityMonitoring;
	
	["Security system initialized"] call EDRP_fnc_logInfo;
};

// Load security configuration
EDRP_fnc_loadSecurityConfig = {
	// Detection thresholds
	EDRP_security_thresholds = createHashMapFromArray [
		["max_speed", 150], // km/h
		["max_teleport_distance", 1000], // meters
		["max_money_gain", 100000], // per minute
		["max_item_gain", 50], // per minute
		["max_altitude", 5000], // meters
		["min_ground_distance", -50] // meters below sea level
	];
	
	// Violation penalties
	EDRP_violation_penalties = createHashMapFromArray [
		["teleport", [1, "kick", "Teleport detection"]],
		["speed", [2, "kick", "Speed hacking"]],
		["money", [3, "ban", "Money duplication"]],
		["item", [3, "ban", "Item duplication"]],
		["altitude", [1, "teleport", "Invalid altitude"]],
		["underground", [1, "teleport", "Underground glitch"]],
		["godmode", [2, "kick", "God mode detection"]],
		["ammo", [1, "warning", "Unlimited ammo"]],
		["weapon", [2, "kick", "Illegal weapon"]],
		["vehicle", [1, "warning", "Vehicle spawn"]]
	];
	
	// Whitelisted areas (no teleport detection)
	EDRP_whitelisted_areas = [
		[[3000, 12000, 0], 2000], // Kavala area
		[[15000, 16000, 0], 1500], // Pyrgos area
		[[14000, 16000, 0], 1000], // Athira area
		[[16000, 16000, 0], 500]   // Airport area
	];
	
	// Admin UIDs (exempt from checks)
	EDRP_admin_uids = [
		"76561198000000000", // Example admin UID
		"76561198000000001"  // Example admin UID
	];
	
	// Allowed weapons by faction
	EDRP_allowed_weapons = createHashMapFromArray [
		["civilian", [
			"hgun_Rook40_F",
			"hgun_Pistol_heavy_02_F",
			"SMG_05_F",
			"arifle_SDAR_F"
		]],
		["police", [
			"hgun_P07_F",
			"hgun_Pistol_heavy_01_F",
			"SMG_02_F",
			"arifle_MX_F",
			"arifle_MXC_F",
			"srifle_EBR_F",
			"LMG_Mk200_F"
		]],
		["medical", [
			"hgun_P07_F"
		]]
	];
};

// Start security monitoring
EDRP_fnc_startSecurityMonitoring = {
	if (!EDRP_security_enabled) exitWith {};
	
	// Position and movement monitoring
	[] spawn {
		while { EDRP_security_enabled } do {
			sleep 1;
			[] call EDRP_fnc_checkPlayerMovement;
		};
	};
	
	// Money and inventory monitoring
	[] spawn {
		while { EDRP_security_enabled } do {
			sleep 5;
			[] call EDRP_fnc_checkPlayerAssets;
		};
	};
	
	// Weapon and equipment monitoring
	[] spawn {
		while { EDRP_security_enabled } do {
			sleep 10;
			[] call EDRP_fnc_checkPlayerEquipment;
		};
	};
	
	// Vehicle monitoring
	[] spawn {
		while { EDRP_security_enabled } do {
			sleep 15;
			[] call EDRP_fnc_checkPlayerVehicles;
		};
	};
	
	// God mode detection
	[] spawn {
		while { EDRP_security_enabled } do {
			sleep 30;
			[] call EDRP_fnc_checkGodMode;
		};
	};
};

// Check player movement for teleporting and speed hacking
EDRP_fnc_checkPlayerMovement = {
	if ([getPlayerUID player] call EDRP_fnc_isAdminExempt) exitWith {};
	
	private _currentPos = getPosATL player;
	private _currentTime = time;
	
	// Skip if first position check
	if (EDRP_last_position isEqualTo [0, 0, 0]) then {
		EDRP_last_position = _currentPos;
		EDRP_last_movement_time = _currentTime;
		exitWith {};
	};
	
	private _distance = EDRP_last_position distance _currentPos;
	private _timeDiff = _currentTime - EDRP_last_movement_time;
	
	// Skip if time difference is too small
	if (_timeDiff < 0.5) exitWith {};
	
	// Calculate speed (km/h)
	private _speed = (_distance / _timeDiff) * 3.6;
	
	// Check for teleporting
	if (_distance > (EDRP_security_thresholds get "max_teleport_distance") && _timeDiff < 5) then {
		// Check if in whitelisted area
		private _inWhitelistedArea = false;
		{
			_x params ["_center", "_radius"];
			if (_currentPos distance _center < _radius || EDRP_last_position distance _center < _radius) then {
				_inWhitelistedArea = true;
				break;
			};
		} forEach EDRP_whitelisted_areas;
		
		if (!_inWhitelistedArea) then {
			[player, "teleport", format ["Teleported %1m in %2s", round(_distance), round(_timeDiff)]] call EDRP_fnc_logViolation;
		};
	};
	
	// Check for speed hacking (only if on ground and in vehicle)
	if (_speed > (EDRP_security_thresholds get "max_speed") && vehicle player != player) then {
		private _vehicle = vehicle player;
		private _maxVehicleSpeed = getNumber (configFile >> "CfgVehicles" >> typeOf _vehicle >> "maxSpeed");
		
		// Allow some tolerance for vehicle max speed
		if (_speed > (_maxVehicleSpeed * 1.2)) then {
			[player, "speed", format ["Speed: %1 km/h (Vehicle max: %2)", round(_speed), _maxVehicleSpeed]] call EDRP_fnc_logViolation;
		};
	};
	
	// Check altitude
	private _altitude = _currentPos select 2;
	if (_altitude > (EDRP_security_thresholds get "max_altitude")) then {
		[player, "altitude", format ["Altitude: %1m", round(_altitude)]] call EDRP_fnc_logViolation;
	};
	
	// Check underground
	if (_altitude < (EDRP_security_thresholds get "min_ground_distance")) then {
		[player, "underground", format ["Underground: %1m", round(_altitude)]] call EDRP_fnc_logViolation;
	};
	
	// Update movement history
	EDRP_movement_history pushBack [_currentPos, _currentTime, _speed];
	if (count EDRP_movement_history > 10) then {
		EDRP_movement_history deleteAt 0;
	};
	
	// Update last position
	EDRP_last_position = _currentPos;
	EDRP_last_movement_time = _currentTime;
};

// Check player money and inventory for duplication
EDRP_fnc_checkPlayerAssets = {
	if ([getPlayerUID player] call EDRP_fnc_isAdminExempt) exitWith {};
	
	// Check money
	private _currentMoney = EDRP_player_cash + EDRP_player_bank;
	private _moneyGain = _currentMoney - EDRP_last_money_check;
	private _maxGain = (EDRP_security_thresholds get "max_money_gain") * 5; // 5 minute window
	
	if (_moneyGain > _maxGain && EDRP_last_money_check > 0) then {
		[player, "money", format ["Money gain: $%1 in 5 minutes", [_moneyGain] call EDRP_fnc_numberText]] call EDRP_fnc_logViolation;
	};
	
	EDRP_last_money_check = _currentMoney;
	
	// Check inventory for item duplication
	private _currentInventory = +EDRP_player_inventory;
	
	if (count EDRP_last_inventory_check > 0) then {
		// Compare inventories
		{
			_x params ["_item", "_quantity"];
			
			private _lastQuantity = 0;
			{
				if ((_x select 0) == _item) exitWith {
					_lastQuantity = _x select 1;
				};
			} forEach EDRP_last_inventory_check;
			
			private _itemGain = _quantity - _lastQuantity;
			if (_itemGain > (EDRP_security_thresholds get "max_item_gain")) then {
				[player, "item", format ["Item gain: %1 x%2", [_item] call EDRP_fnc_getItemName, _itemGain]] call EDRP_fnc_logViolation;
			};
		} forEach _currentInventory;
	};
	
	EDRP_last_inventory_check = _currentInventory;
};

// Check player equipment for illegal items
EDRP_fnc_checkPlayerEquipment = {
	if ([getPlayerUID player] call EDRP_fnc_isAdminExempt) exitWith {};
	
	private _playerFaction = [player] call EDRP_fnc_getPlayerFaction;
	private _allowedWeapons = EDRP_allowed_weapons get _playerFaction;
	
	if (isNil "_allowedWeapons") then { _allowedWeapons = []; };
	
	// Check primary weapon
	private _primaryWeapon = primaryWeapon player;
	if (_primaryWeapon != "" && !(_primaryWeapon in _allowedWeapons)) then {
		[player, "weapon", format ["Illegal primary weapon: %1", _primaryWeapon]] call EDRP_fnc_logViolation;
	};
	
	// Check handgun
	private _handgun = handgunWeapon player;
	if (_handgun != "" && !(_handgun in _allowedWeapons)) then {
		[player, "weapon", format ["Illegal handgun: %1", _handgun]] call EDRP_fnc_logViolation;
	};
	
	// Check secondary weapon
	private _secondaryWeapon = secondaryWeapon player;
	if (_secondaryWeapon != "" && !(_secondaryWeapon in _allowedWeapons)) then {
		[player, "weapon", format ["Illegal secondary weapon: %1", _secondaryWeapon]] call EDRP_fnc_logViolation;
	};
	
	// Check for unlimited ammo
	if (count (magazines player) > 50) then {
		[player, "ammo", format ["Excessive magazines: %1", count (magazines player)]] call EDRP_fnc_logViolation;
	};
};

// Check player vehicles for spawning
EDRP_fnc_checkPlayerVehicles = {
	if ([getPlayerUID player] call EDRP_fnc_isAdminExempt) exitWith {};
	
	// Check vehicles near player
	private _nearbyVehicles = nearestObjects [player, ["LandVehicle", "Air", "Ship"], 100];
	
	{
		private _vehicle = _x;
		private _owner = _vehicle getVariable ["vehicle_owner", []];
		
		// Check if vehicle has no owner (potentially spawned)
		if (count _owner == 0 && !(_vehicle in EDRP_server_vehicles)) then {
			// Check if vehicle was recently spawned
			private _spawnTime = _vehicle getVariable ["spawn_time", 0];
			if (time - _spawnTime < 60) then { // Within last minute
				[player, "vehicle", format ["Suspicious vehicle spawn: %1", typeOf _vehicle]] call EDRP_fnc_logViolation;
			};
		};
	} forEach _nearbyVehicles;
};

// Check for god mode
EDRP_fnc_checkGodMode = {
	if ([getPlayerUID player] call EDRP_fnc_isAdminExempt) exitWith {};
	
	// Check if player has taken damage recently
	private _lastDamage = player getVariable ["last_damage_time", 0];
	private _currentDamage = damage player;
	
	// If player has been in combat but has no damage, might be god mode
	if (time - _lastDamage < 300 && _currentDamage == 0) then { // 5 minutes
		private _inCombat = player getVariable ["in_combat", false];
		if (_inCombat) then {
			[player, "godmode", "No damage taken during combat"] call EDRP_fnc_logViolation;
		};
	};
};

// Log security violation
EDRP_fnc_logViolation = {
	params [["_player", objNull, [objNull]], ["_violationType", "", [""]], ["_details", "", [""]]];
	
	if (isNull _player || _violationType == "") exitWith {};
	
	private _playerUID = getPlayerUID _player;
	private _playerName = name _player;
	private _timestamp = [daytime] call EDRP_fnc_timeToString;
	
	// Create violation entry
	private _violation = [_playerUID, _playerName, _violationType, _details, time];
	EDRP_security_logs pushBack _violation;
	EDRP_player_violations pushBack _violation;
	
	// Update statistics
	EDRP_security_stats set ["violations_detected", (EDRP_security_stats get "violations_detected") + 1];
	EDRP_security_stats set [format ["%1_detections", _violationType], (EDRP_security_stats getOrDefault [format ["%1_detections", _violationType], 0]) + 1];
	
	// Log to server
	diag_log format ["[EdenRP Security] %1 (%2): %3 - %4", _playerName, _playerUID, _violationType, _details];
	
	// Send to server for processing
	[_playerUID, _playerName, _violationType, _details] remoteExec ["EDRP_fnc_processViolation", 2];
	
	// Apply penalty
	[_player, _violationType, _details] call EDRP_fnc_applyViolationPenalty;
};

// Apply violation penalty
EDRP_fnc_applyViolationPenalty = {
	params [["_player", objNull, [objNull]], ["_violationType", "", [""]], ["_details", "", [""]]];
	
	if (isNull _player || _violationType == "") exitWith {};
	
	private _penaltyInfo = EDRP_violation_penalties get _violationType;
	if (isNil "_penaltyInfo") exitWith {};
	
	_penaltyInfo params ["_severity", "_action", "_reason"];
	
	// Count recent violations
	private _playerUID = getPlayerUID _player;
	private _recentViolations = 0;
	{
		if ((_x select 0) == _playerUID && time - (_x select 4) < 3600) then { // Last hour
			_recentViolations = _recentViolations + 1;
		};
	} forEach EDRP_player_violations;
	
	// Escalate penalty based on repeat offenses
	if (_recentViolations >= 3) then {
		_action = "ban";
		_reason = format ["%1 (Repeat offender)", _reason];
	} else {
		if (_recentViolations >= 2 && _action == "warning") then {
			_action = "kick";
		};
	};
	
	// Apply penalty
	switch (_action) do {
		case "warning": {
			[format ["SECURITY WARNING: %1", _reason], "warning"] call EDRP_fnc_hint;
		};
		case "teleport": {
			// Teleport to safe location
			private _safePos = [3500, 13000, 0]; // Kavala spawn
			_player setPosATL _safePos;
			[format ["Teleported for security violation: %1", _reason]] call EDRP_fnc_hint;
		};
		case "kick": {
			[format ["You have been kicked: %1", _reason]] call EDRP_fnc_hint;
			[] spawn {
				sleep 5;
				endMission "LOSER";
			};
		};
		case "ban": {
			[format ["You have been banned: %1", _reason]] call EDRP_fnc_hint;
			EDRP_security_stats set ["players_banned", (EDRP_security_stats get "players_banned") + 1];
			[] spawn {
				sleep 5;
				endMission "LOSER";
			};
		};
	};
	
	// Log penalty application
	diag_log format ["[EdenRP Security] Applied %1 to %2: %3", _action, name _player, _reason];
};

// Check if player is admin exempt
EDRP_fnc_isAdminExempt = {
	params [["_uid", "", [""]]];
	
	if (_uid == "") exitWith { false };
	
	// Check if UID is in admin list
	_uid in EDRP_admin_uids
};

// Validate transaction
EDRP_fnc_validateTransaction = {
	params [["_player", objNull, [objNull]], ["_type", "", [""]], ["_amount", 0, [0]], ["_item", "", [""]]];
	
	if (isNull _player) exitWith { false };
	
	private _playerUID = getPlayerUID _player;
	if ([_playerUID] call EDRP_fnc_isAdminExempt) exitWith { true };
	
	switch (_type) do {
		case "money": {
			// Check money transaction limits
			if (_amount > (EDRP_security_thresholds get "max_money_gain")) then {
				[_player, "money", format ["Large money transaction: $%1", [_amount] call EDRP_fnc_numberText]] call EDRP_fnc_logViolation;
				false
			} else {
				true
			};
		};
		case "item": {
			// Check item transaction limits
			if (_amount > (EDRP_security_thresholds get "max_item_gain")) then {
				[_player, "item", format ["Large item transaction: %1 x%2", [_item] call EDRP_fnc_getItemName, _amount]] call EDRP_fnc_logViolation;
				false
			} else {
				true
			};
		};
		default { true };
	};
};

// Security report
EDRP_fnc_generateSecurityReport = {
	private _report = format [
		"=== EdenRP Security Report ===\n" +
		"Violations Detected: %1\n" +
		"Players Banned: %2\n" +
		"Teleport Detections: %3\n" +
		"Speed Detections: %4\n" +
		"Money Violations: %5\n" +
		"Item Violations: %6\n" +
		"Recent Violations:\n",
		EDRP_security_stats get "violations_detected",
		EDRP_security_stats get "players_banned",
		EDRP_security_stats getOrDefault ["teleport_detections", 0],
		EDRP_security_stats getOrDefault ["speed_detections", 0],
		EDRP_security_stats getOrDefault ["money_detections", 0],
		EDRP_security_stats getOrDefault ["item_detections", 0]
	];
	
	// Add recent violations
	private _recentViolations = EDRP_security_logs select [count EDRP_security_logs - 10, 10];
	{
		_x params ["_uid", "_name", "_type", "_details", "_time"];
		_report = _report + format ["%1 (%2): %3 - %4\n", _name, _uid, _type, _details];
	} forEach _recentViolations;
	
	_report
};

// Add security actions for admins
EDRP_fnc_addSecurityActions = {
	if (EDRP_admin_level > 0) then {
		player addAction [
			"<t color='#FF0000'>Security Report</t>",
			{
				private _report = [] call EDRP_fnc_generateSecurityReport;
				hint _report;
			},
			[],
			1,
			false,
			true,
			"",
			"EDRP_admin_level >= 3"
		];
	};
};

// Initialize security system on client
if (hasInterface) then {
	[] call EDRP_fnc_initSecuritySystem;
	
	// Add security actions
	[] call EDRP_fnc_addSecurityActions;
	
	// Setup damage event handler for god mode detection
	player addEventHandler ["HandleDamage", {
		player setVariable ["last_damage_time", time];
		player setVariable ["in_combat", true];
		
		// Clear combat status after 5 minutes
		[] spawn {
			sleep 300;
			player setVariable ["in_combat", false];
		};
	}];
};
