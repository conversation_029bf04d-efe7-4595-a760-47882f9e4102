/*
	EdenRP Altis Life - Server Core Framework (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Main server-side framework and initialization
	Version: 1.0.0
*/

// Initialize server core
EDRP_fnc_initServerCore = {
	// Server state variables
	EDRP_server_uptime = 0;
	EDRP_server_players = [];
	EDRP_server_vehicles = [];
	EDRP_server_houses = [];
	EDRP_server_gangs = [];
	EDRP_server_territories = [];
	EDRP_server_active_robberies = [];
	
	// Server statistics
	EDRP_server_stats = createHashMapFromArray [
		["total_connections", 0],
		["peak_players", 0],
		["total_playtime", 0],
		["vehicles_spawned", 0],
		["houses_purchased", 0],
		["gangs_created", 0],
		["robberies_completed", 0]
	];
	
	// Load server configuration
	[] call EDRP_fnc_loadServerConfig;
	
	// Initialize database connection
	[] call EDRP_fnc_initDatabase;
	
	// Start server monitoring
	[] call EDRP_fnc_startServerMonitoring;
	
	// Setup cleanup routines
	[] call EDRP_fnc_setupCleanupRoutines;
	
	diag_log "[EdenRP] Server core initialized successfully";
};

// Load server configuration
EDRP_fnc_loadServerConfig = {
	// Server settings
	EDRP_server_config = createHashMapFromArray [
		["max_players", 120],
		["restart_interval", 14400], // 4 hours
		["save_interval", 300], // 5 minutes
		["cleanup_interval", 1800], // 30 minutes
		["max_vehicles_per_player", 5],
		["max_houses_per_player", 3],
		["vehicle_cleanup_time", 3600], // 1 hour
		["house_rent_interval", 86400], // 24 hours
		["gang_territory_income", 10000],
		["territory_capture_time", 300] // 5 minutes
	];
	
	// Economy settings
	EDRP_economy_config = createHashMapFromArray [
		["starting_cash", 5000],
		["starting_bank", 25000],
		["welfare_amount", 1000],
		["welfare_interval", 1800], // 30 minutes
		["tax_rate", 0.05], // 5%
		["inflation_rate", 0.001], // 0.1% per day
		["market_volatility", 0.1]
	];
	
	// Security settings
	EDRP_security_config = createHashMapFromArray [
		["enable_anticheat", true],
		["max_money_per_transaction", 1000000],
		["max_items_per_transaction", 100],
		["teleport_detection", true],
		["speed_detection", true],
		["item_duplication_detection", true],
		["money_duplication_detection", true]
	];
	
	diag_log "[EdenRP] Server configuration loaded";
};

// Initialize database connection
EDRP_fnc_initDatabase = {
	// Database connection settings
	EDRP_database_config = createHashMapFromArray [
		["host", "127.0.0.1"],
		["port", 3306],
		["database", "edenrp_altis"],
		["username", "edenrp_user"],
		["password", "secure_password_123"],
		["timeout", 30]
	];
	
	// Test database connection
	private _testQuery = format ["SELECT 1"];
	private _result = [_testQuery, 1] call EDRP_fnc_asyncCall;
	
	if (count _result > 0) then {
		diag_log "[EdenRP] Database connection established successfully";
		
		// Initialize database tables if needed
		[] call EDRP_fnc_initializeDatabaseTables;
		
		// Load persistent data
		[] call EDRP_fnc_loadPersistentData;
	} else {
		diag_log "[EdenRP] ERROR: Failed to connect to database";
	};
};

// Initialize database tables
EDRP_fnc_initializeDatabaseTables = {
	// Create tables if they don't exist
	private _queries = [
		"CREATE TABLE IF NOT EXISTS players (
			uid VARCHAR(17) PRIMARY KEY,
			name VARCHAR(32) NOT NULL,
			cash INT DEFAULT 5000,
			bank INT DEFAULT 25000,
			civilian_level INT DEFAULT 1,
			police_level INT DEFAULT 1,
			medical_level INT DEFAULT 1,
			civilian_xp INT DEFAULT 0,
			police_xp INT DEFAULT 0,
			medical_xp INT DEFAULT 0,
			licenses TEXT,
			inventory TEXT,
			position TEXT,
			last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			total_playtime INT DEFAULT 0,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)",
		
		"CREATE TABLE IF NOT EXISTS vehicles (
			id INT AUTO_INCREMENT PRIMARY KEY,
			owner_uid VARCHAR(17) NOT NULL,
			class_name VARCHAR(64) NOT NULL,
			position TEXT,
			damage FLOAT DEFAULT 0,
			fuel FLOAT DEFAULT 1,
			inventory TEXT,
			color TEXT,
			plate VARCHAR(8),
			insurance BOOLEAN DEFAULT FALSE,
			impounded BOOLEAN DEFAULT FALSE,
			impound_reason TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_owner (owner_uid)
		)",
		
		"CREATE TABLE IF NOT EXISTS houses (
			id INT AUTO_INCREMENT PRIMARY KEY,
			owner_uid VARCHAR(17) NOT NULL,
			position TEXT NOT NULL,
			house_type VARCHAR(32) NOT NULL,
			inventory TEXT,
			upgrades TEXT,
			rent_due TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_owner (owner_uid)
		)",
		
		"CREATE TABLE IF NOT EXISTS gangs (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(32) UNIQUE NOT NULL,
			leader_uid VARCHAR(17) NOT NULL,
			members TEXT,
			bank INT DEFAULT 0,
			level INT DEFAULT 1,
			xp INT DEFAULT 0,
			territories TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)",
		
		"CREATE TABLE IF NOT EXISTS territories (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(32) UNIQUE NOT NULL,
			position TEXT NOT NULL,
			owner_gang_id INT,
			capture_time TIMESTAMP,
			income_rate INT DEFAULT 1000,
			FOREIGN KEY (owner_gang_id) REFERENCES gangs(id)
		)",
		
		"CREATE TABLE IF NOT EXISTS admin_logs (
			id INT AUTO_INCREMENT PRIMARY KEY,
			admin_uid VARCHAR(17) NOT NULL,
			admin_name VARCHAR(32) NOT NULL,
			action VARCHAR(64) NOT NULL,
			target_uid VARCHAR(17),
			target_name VARCHAR(32),
			details TEXT,
			timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_admin (admin_uid),
			INDEX idx_timestamp (timestamp)
		)",
		
		"CREATE TABLE IF NOT EXISTS player_logs (
			id INT AUTO_INCREMENT PRIMARY KEY,
			player_uid VARCHAR(17) NOT NULL,
			player_name VARCHAR(32) NOT NULL,
			action VARCHAR(64) NOT NULL,
			details TEXT,
			position TEXT,
			timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_player (player_uid),
			INDEX idx_timestamp (timestamp)
		)"
	];
	
	{
		private _result = [_x, 1] call EDRP_fnc_asyncCall;
		if (count _result == 0) then {
			diag_log format ["[EdenRP] ERROR: Failed to create table: %1", _x];
		};
	} forEach _queries;
	
	diag_log "[EdenRP] Database tables initialized";
};

// Load persistent data
EDRP_fnc_loadPersistentData = {
	// Load gangs
	private _gangQuery = "SELECT * FROM gangs";
	private _gangs = [_gangQuery, 2] call EDRP_fnc_asyncCall;
	
	{
		_x params ["_id", "_name", "_leaderUID", "_members", "_bank", "_level", "_xp", "_territories", "_createdAt"];
		
		private _gangData = createHashMapFromArray [
			["id", _id],
			["name", _name],
			["leader", _leaderUID],
			["members", parseSimpleArray _members],
			["bank", _bank],
			["level", _level],
			["xp", _xp],
			["territories", parseSimpleArray _territories]
		];
		
		EDRP_server_gangs pushBack _gangData;
	} forEach _gangs;
	
	// Load territories
	private _territoryQuery = "SELECT * FROM territories";
	private _territories = [_territoryQuery, 2] call EDRP_fnc_asyncCall;
	
	{
		_x params ["_id", "_name", "_position", "_ownerGangId", "_captureTime", "_incomeRate"];
		
		private _territoryData = createHashMapFromArray [
			["id", _id],
			["name", _name],
			["position", parseSimpleArray _position],
			["owner_gang", _ownerGangId],
			["capture_time", _captureTime],
			["income_rate", _incomeRate]
		];
		
		EDRP_server_territories pushBack _territoryData;
	} forEach _territories;
	
	diag_log format ["[EdenRP] Loaded %1 gangs and %2 territories", count EDRP_server_gangs, count EDRP_server_territories];
};

// Start server monitoring
EDRP_fnc_startServerMonitoring = {
	// Server uptime counter
	[] spawn {
		while { true } do {
			sleep 60; // Update every minute
			EDRP_server_uptime = EDRP_server_uptime + 1;
			
			// Update peak players
			private _currentPlayers = count allPlayers;
			if (_currentPlayers > (EDRP_server_stats get "peak_players")) then {
				EDRP_server_stats set ["peak_players", _currentPlayers];
			};
		};
	};
	
	// Performance monitoring
	[] spawn {
		while { true } do {
			sleep 300; // Check every 5 minutes
			
			private _fps = diag_fps;
			private _playerCount = count allPlayers;
			private _vehicleCount = count vehicles;
			
			// Log performance metrics
			diag_log format ["[EdenRP] Performance: FPS=%1, Players=%2, Vehicles=%3", _fps, _playerCount, _vehicleCount];
			
			// Performance warnings
			if (_fps < 20) then {
				diag_log "[EdenRP] WARNING: Low server FPS detected";
			};
			
			if (_vehicleCount > 500) then {
				diag_log "[EdenRP] WARNING: High vehicle count detected";
				[] call EDRP_fnc_cleanupVehicles;
			};
		};
	};
	
	diag_log "[EdenRP] Server monitoring started";
};

// Setup cleanup routines
EDRP_fnc_setupCleanupRoutines = {
	// Vehicle cleanup
	[] spawn {
		while { true } do {
			sleep (EDRP_server_config get "cleanup_interval");
			[] call EDRP_fnc_cleanupVehicles;
		};
	};
	
	// Dead body cleanup
	[] spawn {
		while { true } do {
			sleep 600; // Every 10 minutes
			[] call EDRP_fnc_cleanupDeadBodies;
		};
	};
	
	// Database cleanup
	[] spawn {
		while { true } do {
			sleep 3600; // Every hour
			[] call EDRP_fnc_cleanupDatabase;
		};
	};
	
	diag_log "[EdenRP] Cleanup routines started";
};

// Vehicle cleanup
EDRP_fnc_cleanupVehicles = {
	private _cleanupTime = EDRP_server_config get "vehicle_cleanup_time";
	private _vehiclesDeleted = 0;
	
	{
		private _vehicle = _x;
		
		// Skip if vehicle has players nearby or is owned
		if (count (crew _vehicle) == 0 && 
			{_x distance _vehicle < 100} count allPlayers == 0 &&
			isNull (_vehicle getVariable ["vehicle_owner", objNull])) then {
			
			// Check if vehicle is old enough
			private _spawnTime = _vehicle getVariable ["spawn_time", time];
			if (time - _spawnTime > _cleanupTime) then {
				deleteVehicle _vehicle;
				_vehiclesDeleted = _vehiclesDeleted + 1;
			};
		};
	} forEach vehicles;
	
	if (_vehiclesDeleted > 0) then {
		diag_log format ["[EdenRP] Cleaned up %1 abandoned vehicles", _vehiclesDeleted];
	};
};

// Dead body cleanup
EDRP_fnc_cleanupDeadBodies = {
	private _bodiesDeleted = 0;
	
	{
		if (!alive _x && !isPlayer _x) then {
			deleteVehicle _x;
			_bodiesDeleted = _bodiesDeleted + 1;
		};
	} forEach allDeadMen;
	
	if (_bodiesDeleted > 0) then {
		diag_log format ["[EdenRP] Cleaned up %1 dead bodies", _bodiesDeleted];
	};
};

// Database cleanup
EDRP_fnc_cleanupDatabase = {
	// Clean old logs (keep last 30 days)
	private _cleanupQueries = [
		"DELETE FROM admin_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY)",
		"DELETE FROM player_logs WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY)"
	];
	
	{
		[_x, 1] call EDRP_fnc_asyncCall;
	} forEach _cleanupQueries;
	
	diag_log "[EdenRP] Database cleanup completed";
};

// Player connection handler
EDRP_fnc_onPlayerConnected = {
	params ["_uid", "_name"];
	
	// Update statistics
	EDRP_server_stats set ["total_connections", (EDRP_server_stats get "total_connections") + 1];
	
	// Load player data
	[_uid] call EDRP_fnc_loadPlayerData;
	
	// Add to server player list
	EDRP_server_players pushBack [_uid, _name, time];
	
	diag_log format ["[EdenRP] Player connected: %1 (%2)", _name, _uid];
};

// Player disconnection handler
EDRP_fnc_onPlayerDisconnected = {
	params ["_uid", "_name"];
	
	// Save player data
	[_uid] call EDRP_fnc_savePlayerData;
	
	// Remove from server player list
	{
		if ((_x select 0) == _uid) exitWith {
			// Calculate session playtime
			private _sessionTime = time - (_x select 2);
			EDRP_server_stats set ["total_playtime", (EDRP_server_stats get "total_playtime") + _sessionTime];
			
			EDRP_server_players deleteAt _forEachIndex;
		};
	} forEach EDRP_server_players;
	
	diag_log format ["[EdenRP] Player disconnected: %1 (%2)", _name, _uid];
};

// Load player data
EDRP_fnc_loadPlayerData = {
	params ["_uid"];
	
	private _query = format ["SELECT * FROM players WHERE uid = '%1'", _uid];
	private _result = [_query, 2] call EDRP_fnc_asyncCall;
	
	if (count _result > 0) then {
		// Player exists, load data
		(_result select 0) params [
			"_uid", "_name", "_cash", "_bank", "_civLevel", "_policeLevel", "_medLevel",
			"_civXP", "_policeXP", "_medXP", "_licenses", "_inventory", "_position",
			"_lastSeen", "_totalPlaytime", "_createdAt"
		];
		
		// Send data to client
		private _playerData = createHashMapFromArray [
			["cash", _cash],
			["bank", _bank],
			["civilian_level", _civLevel],
			["police_level", _policeLevel],
			["medical_level", _medLevel],
			["civilian_xp", _civXP],
			["police_xp", _policeXP],
			["medical_xp", _medXP],
			["licenses", parseSimpleArray _licenses],
			["inventory", parseSimpleArray _inventory],
			["position", parseSimpleArray _position],
			["total_playtime", _totalPlaytime]
		];
		
		[_playerData] remoteExec ["EDRP_fnc_receivePlayerData", _uid];
	} else {
		// New player, create default data
		private _insertQuery = format [
			"INSERT INTO players (uid, name, cash, bank) VALUES ('%1', '%2', %3, %4)",
			_uid,
			name (call BIS_fnc_getUnitByUID),
			EDRP_economy_config get "starting_cash",
			EDRP_economy_config get "starting_bank"
		];
		
		[_insertQuery, 1] call EDRP_fnc_asyncCall;
		
		// Send default data to client
		private _defaultData = createHashMapFromArray [
			["cash", EDRP_economy_config get "starting_cash"],
			["bank", EDRP_economy_config get "starting_bank"],
			["civilian_level", 1],
			["police_level", 1],
			["medical_level", 1],
			["civilian_xp", 0],
			["police_xp", 0],
			["medical_xp", 0],
			["licenses", []],
			["inventory", []],
			["position", [0, 0, 0]],
			["total_playtime", 0]
		];
		
		[_defaultData] remoteExec ["EDRP_fnc_receivePlayerData", _uid];
		
		diag_log format ["[EdenRP] Created new player: %1", _uid];
	};
};

// Save player data
EDRP_fnc_savePlayerData = {
	params ["_uid"];
	
	// Get player object
	private _player = call BIS_fnc_getUnitByUID;
	if (isNull _player) exitWith {};
	
	// Get player data
	private _cash = _player getVariable ["player_cash", 0];
	private _bank = _player getVariable ["player_bank", 0];
	private _civLevel = _player getVariable ["civilian_level", 1];
	private _policeLevel = _player getVariable ["police_level", 1];
	private _medLevel = _player getVariable ["medical_level", 1];
	private _civXP = _player getVariable ["civilian_xp", 0];
	private _policeXP = _player getVariable ["police_xp", 0];
	private _medXP = _player getVariable ["medical_xp", 0];
	private _licenses = _player getVariable ["player_licenses", []];
	private _inventory = _player getVariable ["player_inventory", []];
	private _position = getPosATL _player;
	
	// Update database
	private _updateQuery = format [
		"UPDATE players SET name='%1', cash=%2, bank=%3, civilian_level=%4, police_level=%5, medical_level=%6, civilian_xp=%7, police_xp=%8, medical_xp=%9, licenses='%10', inventory='%11', position='%12', last_seen=NOW() WHERE uid='%13'",
		name _player,
		_cash,
		_bank,
		_civLevel,
		_policeLevel,
		_medLevel,
		_civXP,
		_policeXP,
		_medXP,
		str _licenses,
		str _inventory,
		str _position,
		_uid
	];
	
	[_updateQuery, 1] call EDRP_fnc_asyncCall;
};

// Initialize server core on server start
if (isServer) then {
	[] call EDRP_fnc_initServerCore;
	
	// Setup event handlers
	addMissionEventHandler ["PlayerConnected", {
		params ["_id", "_uid", "_name", "_jip", "_owner"];
		[_uid, _name] call EDRP_fnc_onPlayerConnected;
	}];
	
	addMissionEventHandler ["PlayerDisconnected", {
		params ["_id", "_uid", "_name", "_jip", "_owner"];
		[_uid, _name] call EDRP_fnc_onPlayerDisconnected;
	}];
};
