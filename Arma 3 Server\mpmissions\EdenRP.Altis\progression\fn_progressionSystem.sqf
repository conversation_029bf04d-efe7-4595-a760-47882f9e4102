/*
	EdenRP Altis Life - Progression and XP System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Faction-based progression and experience system
	Version: 1.0.0
*/

// Initialize progression system
EDRP_fnc_initProgressionSystem = {
	// Progression state variables
	EDRP_player_xp = createHashMapFromArray [
		["civilian", 0],
		["police", 0],
		["medical", 0],
		["total", 0]
	];
	
	EDRP_player_levels = createHashMapFromArray [
		["civilian", 1],
		["police", 1],
		["medical", 1]
	];
	
	EDRP_player_skills = createHashMapFromArray [
		["mining", 1],
		["processing", 1],
		["fishing", 1],
		["hunting", 1],
		["crafting", 1],
		["driving", 1],
		["flying", 1],
		["medical_training", 1],
		["law_enforcement", 1],
		["leadership", 1]
	];
	
	// Progression statistics
	EDRP_progression_stats = createHashMapFromArray [
		["total_xp_earned", 0],
		["levels_gained", 0],
		["skills_maxed", 0],
		["achievements_unlocked", 0]
	];
	
	// Load progression configuration
	[] call EDRP_fnc_loadProgressionConfig;
	
	["Progression system initialized"] call EDRP_fnc_logInfo;
};

// Load progression configuration
EDRP_fnc_loadProgressionConfig = {
	// XP requirements per level (exponential growth)
	EDRP_xp_requirements = [];
	for "_i" from 1 to 100 do {
		private _xpRequired = round(1000 * (_i ^ 1.5));
		EDRP_xp_requirements pushBack _xpRequired;
	};
	
	// Faction level benefits
	EDRP_civilian_benefits = [
		[5, "Increased job payouts (+10%)", 0.1],
		[10, "Reduced market prices (-5%)", 0.05],
		[15, "Faster processing times (-20%)", 0.2],
		[20, "Increased storage capacity (+25%)", 0.25],
		[25, "Vehicle insurance discount (-15%)", 0.15],
		[30, "House upgrade discount (-20%)", 0.2],
		[35, "Exclusive civilian vehicles", 0],
		[40, "VIP civilian areas access", 0],
		[45, "Master civilian status", 0],
		[50, "Legendary civilian perks", 0]
	];
	
	EDRP_police_benefits = [
		[5, "Advanced equipment access", 0],
		[10, "Increased arrest rewards (+20%)", 0.2],
		[15, "Faster backup response", 0],
		[20, "Advanced police vehicles", 0],
		[25, "Detective rank privileges", 0],
		[30, "SWAT team access", 0],
		[35, "Undercover operations", 0],
		[40, "Police chief privileges", 0],
		[45, "Federal agent status", 0],
		[50, "Elite law enforcement", 0]
	];
	
	EDRP_medical_benefits = [
		[5, "Advanced medical equipment", 0],
		[10, "Increased medical payouts (+25%)", 0.25],
		[15, "Faster revival times", 0],
		[20, "Medical helicopter access", 0],
		[25, "Surgeon rank privileges", 0],
		[30, "Emergency response priority", 0],
		[35, "Medical research access", 0],
		[40, "Chief medical officer", 0],
		[45, "Medical director status", 0],
		[50, "Master medical professional", 0]
	];
	
	// Skill categories and XP sources
	EDRP_skill_xp_sources = createHashMapFromArray [
		["mining", [
			["iron_ore", 5],
			["copper_ore", 8],
			["salt", 3],
			["sand", 2],
			["diamond", 25],
			["oil", 15]
		]],
		["processing", [
			["iron_refined", 10],
			["copper_refined", 15],
			["salt_refined", 6],
			["glass", 8],
			["diamond_cut", 50],
			["oil_processed", 30]
		]],
		["fishing", [
			["salema", 3],
			["ornate", 5],
			["mackerel", 4],
			["tuna", 8],
			["mullet", 6],
			["catshark", 12]
		]],
		["hunting", [
			["rabbit", 5],
			["hen", 4],
			["rooster", 4],
			["sheep", 8],
			["goat", 10],
			["cow", 15]
		]],
		["medical_training", [
			["player_revived", 50],
			["player_treated", 25],
			["medical_call_completed", 75],
			["emergency_response", 100]
		]],
		["law_enforcement", [
			["arrest_made", 75],
			["ticket_issued", 25],
			["crime_prevented", 100],
			["backup_provided", 50]
		]]
	];
	
	// Achievement system
	EDRP_achievements = [
		["first_job", "First Day at Work", "Complete your first job", 100],
		["money_maker", "Money Maker", "Earn $100,000", 250],
		["millionaire", "Millionaire", "Earn $1,000,000", 1000],
		["house_owner", "Property Owner", "Buy your first house", 200],
		["vehicle_collector", "Vehicle Collector", "Own 3 vehicles", 300],
		["helpful_citizen", "Helpful Citizen", "Help 10 other players", 500],
		["law_abiding", "Law Abiding Citizen", "Go 24 hours without arrest", 150],
		["master_miner", "Master Miner", "Reach mining skill level 50", 750],
		["skilled_processor", "Skilled Processor", "Reach processing skill level 50", 750],
		["expert_fisherman", "Expert Fisherman", "Reach fishing skill level 50", 750]
	];
	
	// Unlocked achievements
	EDRP_unlocked_achievements = [];
};

// Award XP to player
EDRP_fnc_awardXP = {
	params [
		["_faction", "civilian", [""]],
		["_amount", 0, [0]],
		["_source", "", [""]],
		["_skill", "", [""]]
	];
	
	if (_amount <= 0) exitWith { false };
	
	// Award faction XP
	private _currentXP = EDRP_player_xp get _faction;
	private _newXP = _currentXP + _amount;
	EDRP_player_xp set [_faction, _newXP];
	
	// Award total XP
	private _totalXP = EDRP_player_xp get "total";
	EDRP_player_xp set ["total", _totalXP + _amount];
	
	// Award skill XP if specified
	if (_skill != "") then {
		private _currentSkillXP = EDRP_player_skills get _skill;
		private _newSkillXP = _currentSkillXP + _amount;
		EDRP_player_skills set [_skill, _newSkillXP];
		
		// Check for skill level up
		[_skill] call EDRP_fnc_checkSkillLevelUp;
	};
	
	// Check for faction level up
	[_faction] call EDRP_fnc_checkFactionLevelUp;
	
	// Update statistics
	EDRP_progression_stats set ["total_xp_earned", (EDRP_progression_stats get "total_xp_earned") + _amount];
	
	// Show XP notification
	private _message = format ["+%1 XP", _amount];
	if (_source != "") then {
		_message = format ["+%1 XP (%2)", _amount, _source];
	};
	[_message, "xp"] call EDRP_fnc_showNotification;
	
	// Check achievements
	[] call EDRP_fnc_checkAchievements;
	
	true
};

// Check faction level up
EDRP_fnc_checkFactionLevelUp = {
	params [["_faction", "civilian", [""]]];
	
	private _currentXP = EDRP_player_xp get _faction;
	private _currentLevel = EDRP_player_levels get _faction;
	
	// Calculate new level
	private _newLevel = 1;
	{
		if (_currentXP >= _x) then {
			_newLevel = _forEachIndex + 2;
		} else {
			break;
		};
	} forEach EDRP_xp_requirements;
	
	// Check if level increased
	if (_newLevel > _currentLevel) then {
		EDRP_player_levels set [_faction, _newLevel];
		
		// Show level up notification
		[format ["LEVEL UP! %1 Level %2", _faction, _newLevel], "levelup"] call EDRP_fnc_showNotification;
		
		// Apply level benefits
		[_faction, _newLevel] call EDRP_fnc_applyLevelBenefits;
		
		// Update statistics
		EDRP_progression_stats set ["levels_gained", (EDRP_progression_stats get "levels_gained") + 1];
		
		// Send level up to server
		[getPlayerUID player, _faction, _newLevel] remoteExec ["EDRP_fnc_updatePlayerLevel", 2];
		
		true
	} else {
		false
	};
};

// Check skill level up
EDRP_fnc_checkSkillLevelUp = {
	params [["_skill", "", [""]]];
	
	if (_skill == "") exitWith { false };
	
	private _currentXP = EDRP_player_skills get _skill;
	private _currentLevel = [_skill] call EDRP_fnc_getSkillLevel;
	
	// Calculate new level (skills use different progression)
	private _newLevel = floor(_currentXP / 100) + 1;
	if (_newLevel > 100) then { _newLevel = 100; };
	
	// Check if level increased
	if (_newLevel > _currentLevel) then {
		// Show skill level up notification
		[format ["Skill Up! %1 Level %2", _skill, _newLevel], "skillup"] call EDRP_fnc_showNotification;
		
		// Check if skill maxed
		if (_newLevel >= 100) then {
			EDRP_progression_stats set ["skills_maxed", (EDRP_progression_stats get "skills_maxed") + 1];
			[format ["MASTERED! %1 skill maxed out!", _skill], "mastery"] call EDRP_fnc_showNotification;
		};
		
		true
	} else {
		false
	};
};

// Get skill level
EDRP_fnc_getSkillLevel = {
	params [["_skill", "", [""]]];
	
	if (_skill == "") exitWith { 1 };
	
	private _skillXP = EDRP_player_skills get _skill;
	if (isNil "_skillXP") exitWith { 1 };
	
	private _level = floor(_skillXP / 100) + 1;
	if (_level > 100) then { _level = 100; };
	
	_level
};

// Apply level benefits
EDRP_fnc_applyLevelBenefits = {
	params [["_faction", "civilian", [""]], ["_level", 1, [0]]];
	
	private _benefits = [];
	switch (_faction) do {
		case "civilian": { _benefits = EDRP_civilian_benefits; };
		case "police": { _benefits = EDRP_police_benefits; };
		case "medical": { _benefits = EDRP_medical_benefits; };
	};
	
	// Find benefits for this level
	{
		_x params ["_reqLevel", "_description", "_value"];
		
		if (_level == _reqLevel) then {
			[format ["New Benefit Unlocked: %1", _description], "benefit"] call EDRP_fnc_showNotification;
			
			// Apply specific benefits
			switch (_faction) do {
				case "civilian": {
					switch (_reqLevel) do {
						case 5: { EDRP_job_payout_multiplier = 1.1; };
						case 10: { EDRP_market_discount = 0.05; };
						case 15: { EDRP_processing_speed_bonus = 0.2; };
						case 20: { EDRP_storage_capacity_bonus = 0.25; };
						case 25: { EDRP_insurance_discount = 0.15; };
						case 30: { EDRP_house_upgrade_discount = 0.2; };
					};
				};
				case "police": {
					switch (_reqLevel) do {
						case 10: { EDRP_arrest_reward_multiplier = 1.2; };
						case 25: { EDRP_police_rank = "Detective"; };
						case 30: { EDRP_swat_access = true; };
						case 40: { EDRP_police_rank = "Chief"; };
					};
				};
				case "medical": {
					switch (_reqLevel) do {
						case 10: { EDRP_medical_payout_multiplier = 1.25; };
						case 25: { EDRP_medical_rank = "Surgeon"; };
						case 40: { EDRP_medical_rank = "Chief Medical Officer"; };
					};
				};
			};
		};
	} forEach _benefits;
};

// Check achievements
EDRP_fnc_checkAchievements = {
	{
		_x params ["_id", "_name", "_description", "_xpReward"];
		
		if !(_id in EDRP_unlocked_achievements) then {
			private _unlocked = false;
			
			switch (_id) do {
				case "first_job": {
					_unlocked = (EDRP_progression_stats get "total_xp_earned") > 0;
				};
				case "money_maker": {
					_unlocked = (EDRP_player_bank + EDRP_player_cash) >= 100000;
				};
				case "millionaire": {
					_unlocked = (EDRP_player_bank + EDRP_player_cash) >= 1000000;
				};
				case "house_owner": {
					_unlocked = count EDRP_owned_houses > 0;
				};
				case "vehicle_collector": {
					_unlocked = count EDRP_owned_vehicles >= 3;
				};
				case "master_miner": {
					_unlocked = ([_skill] call EDRP_fnc_getSkillLevel) >= 50;
				};
				case "skilled_processor": {
					_unlocked = (["processing"] call EDRP_fnc_getSkillLevel) >= 50;
				};
				case "expert_fisherman": {
					_unlocked = (["fishing"] call EDRP_fnc_getSkillLevel) >= 50;
				};
			};
			
			if (_unlocked) then {
				// Unlock achievement
				EDRP_unlocked_achievements pushBack _id;
				
				// Award XP
				["civilian", _xpReward, "Achievement"] call EDRP_fnc_awardXP;
				
				// Show achievement notification
				[format ["ACHIEVEMENT UNLOCKED: %1", _name], "achievement"] call EDRP_fnc_showNotification;
				
				// Update statistics
				EDRP_progression_stats set ["achievements_unlocked", (EDRP_progression_stats get "achievements_unlocked") + 1];
			};
		};
	} forEach EDRP_achievements;
};

// Open progression menu
EDRP_fnc_openProgressionMenu = {
	// Create progression dialog
	createDialog "EDRP_ProgressionDialog";
	
	// Update progression display
	[] call EDRP_fnc_updateProgressionMenu;
	
	true
};

// Update progression menu
EDRP_fnc_updateProgressionMenu = {
	private _display = findDisplay 52000;
	if (isNull _display) exitWith {};
	
	// Update faction levels
	private _civilianLevel = EDRP_player_levels get "civilian";
	private _policeLevel = EDRP_player_levels get "police";
	private _medicalLevel = EDRP_player_levels get "medical";
	
	private _civilianXP = EDRP_player_xp get "civilian";
	private _policeXP = EDRP_player_xp get "police";
	private _medicalXP = EDRP_player_xp get "medical";
	
	// Update faction info
	private _factionInfoCtrl = _display displayCtrl 52001;
	private _factionInfo = format [
		"Civilian: Level %1 (%2 XP)\nPolice: Level %3 (%4 XP)\nMedical: Level %5 (%6 XP)\n\nTotal XP: %7",
		_civilianLevel,
		_civilianXP,
		_policeLevel,
		_policeXP,
		_medicalLevel,
		_medicalXP,
		EDRP_player_xp get "total"
	];
	_factionInfoCtrl ctrlSetText _factionInfo;
	
	// Update skills list
	private _skillsList = _display displayCtrl 52002;
	lbClear _skillsList;
	
	{
		private _skill = _x;
		private _skillLevel = [_skill] call EDRP_fnc_getSkillLevel;
		private _skillXP = EDRP_player_skills get _skill;
		
		private _entry = format ["%1: Level %2 (%3 XP)", _skill, _skillLevel, _skillXP];
		
		_skillsList lbAdd _entry;
		_skillsList lbSetData [_forEachIndex, _skill];
		_skillsList lbSetValue [_forEachIndex, _skillLevel];
		
		// Color code by level
		if (_skillLevel >= 75) then {
			_skillsList lbSetColor [_forEachIndex, [1, 0.8, 0, 1]]; // Gold for high level
		} else {
			if (_skillLevel >= 50) then {
				_skillsList lbSetColor [_forEachIndex, [0.8, 0, 1, 1]]; // Purple for medium level
			} else {
				_skillsList lbSetColor [_forEachIndex, [1, 1, 1, 1]]; // White for low level
			};
		};
	} forEach (keys EDRP_player_skills);
	
	// Update achievements list
	private _achievementsList = _display displayCtrl 52003;
	lbClear _achievementsList;
	
	{
		_x params ["_id", "_name", "_description", "_xpReward"];
		
		private _unlocked = _id in EDRP_unlocked_achievements;
		private _status = if (_unlocked) then { "UNLOCKED" } else { "LOCKED" };
		private _entry = format ["%1 [%2] - %3 XP", _name, _status, _xpReward];
		
		_achievementsList lbAdd _entry;
		_achievementsList lbSetData [_forEachIndex, _id];
		_achievementsList lbSetValue [_forEachIndex, _xpReward];
		
		// Color code by status
		if (_unlocked) then {
			_achievementsList lbSetColor [_forEachIndex, [0, 1, 0, 1]]; // Green for unlocked
		} else {
			_achievementsList lbSetColor [_forEachIndex, [0.5, 0.5, 0.5, 1]]; // Gray for locked
		};
	} forEach EDRP_achievements;
};

// Show notification
EDRP_fnc_showNotification = {
	params [["_message", "", [""]], ["_type", "info", [""]]];
	
	if (_message == "") exitWith {};
	
	// Color based on type
	private _color = switch (_type) do {
		case "xp": { [0, 1, 0, 1] }; // Green
		case "levelup": { [1, 0.8, 0, 1] }; // Gold
		case "skillup": { [0, 0.8, 1, 1] }; // Cyan
		case "mastery": { [1, 0, 1, 1] }; // Magenta
		case "achievement": { [1, 0.5, 0, 1] }; // Orange
		case "benefit": { [0.8, 0, 1, 1] }; // Purple
		default { [1, 1, 1, 1] }; // White
	};
	
	// Show notification
	[_message, _color] call EDRP_fnc_hint;
};

// Add progression actions
EDRP_fnc_addProgressionActions = {
	// Progression menu action
	player addAction [
		"<t color='#FFD700'>Progression Menu</t>",
		{
			[] call EDRP_fnc_openProgressionMenu;
		},
		[],
		8,
		false,
		true,
		"",
		"true"
	];
};

// Initialize progression system on client
if (hasInterface) then {
	[] call EDRP_fnc_initProgressionSystem;
	
	// Add progression actions
	[] call EDRP_fnc_addProgressionActions;
};
