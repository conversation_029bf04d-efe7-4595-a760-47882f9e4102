/*
	EdenRP Altis Life - Setup Event Handlers
	Author: EdenRP Development Team
	Description: Sets up client-side event handlers and monitoring
	Version: 1.0.0
*/

// Setup event handlers function
EDRP_fnc_setupEventHandlers = {
	diag_log "EdenRP: Setting up event handlers...";
	
	// Player event handlers
	call EDRP_fnc_setupPlayerEventHandlers;
	
	// Vehicle event handlers
	call EDRP_fnc_setupVehicleEventHandlers;
	
	// Mission event handlers
	call EDRP_fnc_setupMissionEventHandlers;
	
	// Custom event handlers
	call EDRP_fnc_setupCustomEventHandlers;
	
	diag_log "EdenRP: Event handlers setup complete";
};

// Player event handlers
EDRP_fnc_setupPlayerEventHandlers = {
	// Player killed event
	player addEventHandler ["Killed", {
		params ["_unit", "_killer", "_instigator", "_useEffects"];
		
		diag_log format ["EdenRP: Player %1 killed by %2", name _unit, name _killer];
		
		// Handle player death
		[_unit, _killer, _instigator] call EDRP_fnc_handlePlayerDeath;
		
		// Start respawn timer
		[] spawn EDRP_fnc_respawnTimer;
	}];
	
	// Player respawn event
	player addEventHandler ["Respawn", {
		params ["_unit", "_corpse"];
		
		diag_log format ["EdenRP: Player %1 respawned", name _unit];
		
		// Handle respawn
		[_unit, _corpse] call EDRP_fnc_handlePlayerRespawn;
		
		// Reset player state
		call EDRP_fnc_resetPlayerState;
	}];
	
	// Player hit event
	player addEventHandler ["Hit", {
		params ["_unit", "_source", "_damage", "_instigator"];
		
		// Handle player damage
		[_unit, _source, _damage, _instigator] call EDRP_fnc_handlePlayerDamage;
	}];
	
	// Player inventory opened
	player addEventHandler ["InventoryOpened", {
		params ["_unit", "_container"];
		
		// Check if player can access container
		if (!([_container] call EDRP_fnc_canAccessContainer)) then {
			false
		} else {
			true
		};
	}];
	
	// Player inventory closed
	player addEventHandler ["InventoryClosed", {
		params ["_unit", "_container"];
		
		// Update inventory state
		call EDRP_fnc_updateInventoryState;
	}];
	
	// Player weapon fired
	player addEventHandler ["Fired", {
		params ["_unit", "_weapon", "_muzzle", "_mode", "_ammo", "_magazine", "_projectile", "_gunner"];
		
		// Handle weapon fired
		[_unit, _weapon, _ammo] call EDRP_fnc_handleWeaponFired;
	}];
	
	// Player animation changed
	player addEventHandler ["AnimChanged", {
		params ["_unit", "_anim"];
		
		// Update animation state
		EDRP_current_animation = _anim;
	}];
	
	// Player gear changed
	player addEventHandler ["Take", {
		params ["_unit", "_container", "_item"];
		
		// Handle item taken
		[_unit, _item, "take"] call EDRP_fnc_handleInventoryChange;
	}];
	
	player addEventHandler ["Put", {
		params ["_unit", "_container", "_item"];
		
		// Handle item put
		[_unit, _item, "put"] call EDRP_fnc_handleInventoryChange;
	}];
};

// Vehicle event handlers
EDRP_fnc_setupVehicleEventHandlers = {
	// Vehicle get in event
	player addEventHandler ["GetInMan", {
		params ["_unit", "_role", "_vehicle", "_turret"];
		
		diag_log format ["EdenRP: Player entered vehicle %1 as %2", typeOf _vehicle, _role];
		
		// Handle vehicle entry
		[_unit, _vehicle, _role] call EDRP_fnc_handleVehicleEntry;
		
		// Setup vehicle-specific actions
		[_vehicle] call EDRP_fnc_setupVehicleActions;
	}];
	
	// Vehicle get out event
	player addEventHandler ["GetOutMan", {
		params ["_unit", "_role", "_vehicle", "_turret"];
		
		diag_log format ["EdenRP: Player exited vehicle %1", typeOf _vehicle];
		
		// Handle vehicle exit
		[_unit, _vehicle, _role] call EDRP_fnc_handleVehicleExit;
	}];
	
	// Vehicle engine event
	player addEventHandler ["Engine", {
		params ["_vehicle", "_engineState"];
		
		// Handle engine state change
		[_vehicle, _engineState] call EDRP_fnc_handleEngineState;
	}];
};

// Mission event handlers
EDRP_fnc_setupMissionEventHandlers = {
	// Handle disconnect
	addMissionEventHandler ["HandleDisconnect", {
		params ["_unit", "_id", "_uid", "_name"];
		
		// Save player data before disconnect
		call EDRP_fnc_sessionCleanup;
		
		false // Allow disconnect
	}];
	
	// Map single click
	addMissionEventHandler ["Map", {
		params ["_mapIsOpened", "_mapIsForced"];
		
		if (_mapIsOpened) then {
			EDRP_ui_map_open = true;
		} else {
			EDRP_ui_map_open = false;
		};
	}];
	
	// Draw 3D event
	addMissionEventHandler ["Draw3D", {
		// Handle 3D drawing
		call EDRP_fnc_handle3DDraw;
	}];
	
	// EachFrame event
	addMissionEventHandler ["EachFrame", {
		// Handle per-frame updates
		call EDRP_fnc_handlePerFrame;
	}];
};

// Custom event handlers
EDRP_fnc_setupCustomEventHandlers = {
	// Money changed event
	["EDRP_moneyChanged", {
		params ["_oldAmount", "_newAmount", "_type"];
		
		// Update HUD
		call EDRP_fnc_updateMoneyHUD;
		
		// Log transaction
		[_oldAmount, _newAmount, _type] call EDRP_fnc_logTransaction;
	}] call CBA_fnc_addEventHandler;
	
	// Inventory changed event
	["EDRP_inventoryChanged", {
		params ["_item", "_amount", "_action"];
		
		// Update inventory UI
		call EDRP_fnc_updateInventoryUI;
		
		// Check weight limits
		call EDRP_fnc_checkInventoryWeight;
	}] call CBA_fnc_addEventHandler;
	
	// Player arrested event
	["EDRP_playerArrested", {
		params ["_arrestedPlayer", "_officer"];
		
		// Handle arrest
		[_arrestedPlayer, _officer] call EDRP_fnc_handleArrest;
	}] call CBA_fnc_addEventHandler;
	
	// Player revived event
	["EDRP_playerRevived", {
		params ["_revivedPlayer", "_medic"];
		
		// Handle revival
		[_revivedPlayer, _medic] call EDRP_fnc_handleRevival;
	}] call CBA_fnc_addEventHandler;
	
	// Gang message event
	["EDRP_gangMessage", {
		params ["_message", "_sender"];
		
		// Display gang message
		[_message, _sender] call EDRP_fnc_displayGangMessage;
	}] call CBA_fnc_addEventHandler;
	
	// Job completed event
	["EDRP_jobCompleted", {
		params ["_jobType", "_payment", "_xp"];
		
		// Handle job completion
		[_jobType, _payment, _xp] call EDRP_fnc_handleJobCompletion;
	}] call CBA_fnc_addEventHandler;
	
	// Vehicle purchased event
	["EDRP_vehiclePurchased", {
		params ["_vehicle", "_price", "_type"];
		
		// Handle vehicle purchase
		[_vehicle, _price, _type] call EDRP_fnc_handleVehiclePurchase;
	}] call CBA_fnc_addEventHandler;
	
	// House purchased event
	["EDRP_housePurchased", {
		params ["_house", "_price"];
		
		// Handle house purchase
		[_house, _price] call EDRP_fnc_handleHousePurchase;
	}] call CBA_fnc_addEventHandler;
};

// Key event handlers
EDRP_fnc_setupKeyBindings = {
	// Main interaction key (Y)
	EDRP_key_interaction = (findDisplay 46) displayAddEventHandler ["KeyDown", {
		params ["_display", "_key", "_shift", "_ctrl", "_alt"];
		
		if (_key == 21) then { // Y key
			if (!EDRP_ui_menu_open) then {
				call EDRP_fnc_openInteractionMenu;
			};
			true
		} else {
			false
		};
	}];
	
	// Vehicle interaction key (T)
	EDRP_key_vehicle = (findDisplay 46) displayAddEventHandler ["KeyDown", {
		params ["_display", "_key", "_shift", "_ctrl", "_alt"];
		
		if (_key == 20) then { // T key
			if (vehicle player != player) then {
				call EDRP_fnc_openVehicleMenu;
			};
			true
		} else {
			false
		};
	}];
	
	// Phone key (Shift+P)
	EDRP_key_phone = (findDisplay 46) displayAddEventHandler ["KeyDown", {
		params ["_display", "_key", "_shift", "_ctrl", "_alt"];
		
		if (_key == 25 && _shift) then { // Shift+P
			if (!EDRP_ui_phone_open) then {
				call EDRP_fnc_openPhone;
			};
			true
		} else {
			false
		};
	}];
	
	// Inventory key (I)
	EDRP_key_inventory = (findDisplay 46) displayAddEventHandler ["KeyDown", {
		params ["_display", "_key", "_shift", "_ctrl", "_alt"];
		
		if (_key == 23) then { // I key
			if (!EDRP_ui_inventory_open) then {
				call EDRP_fnc_openInventory;
			};
			true
		} else {
			false
		};
	}];
	
	// Holster weapon key (H)
	EDRP_key_holster = (findDisplay 46) displayAddEventHandler ["KeyDown", {
		params ["_display", "_key", "_shift", "_ctrl", "_alt"];
		
		if (_key == 35) then { // H key
			call EDRP_fnc_toggleWeaponHolster;
			true
		} else {
			false
		};
	}];
	
	// Flashlight key (Shift+L)
	EDRP_key_flashlight = (findDisplay 46) displayAddEventHandler ["KeyDown", {
		params ["_display", "_key", "_shift", "_ctrl", "_alt"];
		
		if (_key == 38 && _shift) then { // Shift+L
			call EDRP_fnc_toggleFlashlight;
			true
		} else {
			false
		};
	}];
};

// Cleanup event handlers
EDRP_fnc_cleanupEventHandlers = {
	// Remove key handlers
	{
		(findDisplay 46) displayRemoveEventHandler ["KeyDown", _x];
	} forEach EDRP_key_handlers;
	
	// Clear handler arrays
	EDRP_event_handlers = [];
	EDRP_key_handlers = [];
	EDRP_display_handlers = [];
};

diag_log "EdenRP: Event handlers setup functions loaded";
