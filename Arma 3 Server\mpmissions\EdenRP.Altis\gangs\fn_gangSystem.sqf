/*
	EdenRP Altis Life - Gang System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Core gang system with creation, management, and hierarchy
	Version: 1.0.0
*/

// Initialize gang system
EDRP_fnc_initGangSystem = {
	// Gang state variables
	EDRP_gang_data = []; // [gangID, gangName, rank, memberCount]
	EDRP_gang_bank = 0;
	EDRP_gang_members = [];
	EDRP_gang_territory = [];
	
	// Gang statistics
	EDRP_gang_stats = createHashMapFromArray [
		["members_recruited", 0],
		["territory_captured", 0],
		["gang_wars_won", 0],
		["total_earnings", 0],
		["time_in_gang", 0]
	];
	
	// Load gang configuration
	[] call EDRP_fnc_loadGangConfig;
	
	["Gang system initialized"] call EDRP_fnc_logInfo;
};

// Load gang configuration
EDRP_fnc_loadGangConfig = {
	// Gang rank structure (adapted from Olympus)
	EDRP_gang_ranks = [
		["Recruit", 1, ["View gang info"]],
		["Member", 2, ["View gang info", "Use gang chat"]],
		["Trusted", 3, ["View gang info", "Use gang chat", "Invite players"]],
		["Lieutenant", 4, ["View gang info", "Use gang chat", "Invite players", "Promote/Demote", "Kick members", "Withdraw funds"]],
		["Leader", 5, ["All permissions", "Disband gang", "Change leadership"]]
	];
	
	// Gang creation cost
	EDRP_gang_creation_cost = 75000; // $75,000 to create a gang
	
	// Territory types
	EDRP_territory_types = [
		["Drug Processing", "drug", "Illegal drug processing facility"],
		["Arms Dealing", "arms", "Weapons and ammunition trading"],
		["Money Laundering", "money", "Financial operations center"],
		["Smuggling Hub", "smuggle", "Import/export operations"],
		["Gang Hideout", "hideout", "Safe house and meeting point"]
	];
	
	// Capturable territories
	EDRP_capturable_territories = [
		["Weed Processing", "weed_processing", [4500, 4500, 0], "drug"],
		["Cocaine Processing", "cocaine_processing", [5500, 5500, 0], "drug"],
		["Heroin Processing", "heroin_processing", [6500, 6500, 0], "drug"],
		["Arms Depot", "arms_depot", [7500, 7500, 0], "arms"],
		["Money Laundry", "money_laundry", [8500, 8500, 0], "money"],
		["Smuggling Port", "smuggling_port", [9500, 9500, 0], "smuggle"]
	];
};

// Create gang
EDRP_fnc_createGang = {
	params [["_gangName", "", [""]]];
	
	if (_gangName == "") exitWith {
		["Gang name cannot be empty"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player is already in a gang
	if (count EDRP_gang_data > 0) exitWith {
		["You must leave your current gang before creating a new one"] call EDRP_fnc_hint;
		false
	};
	
	// Validate gang name
	if (count _gangName > 32) exitWith {
		["Gang name cannot exceed 32 characters"] call EDRP_fnc_hint;
		false
	};
	
	// Check for invalid characters
	private _allowedChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_ ";
	private _nameArray = toArray _gangName;
	private _allowedArray = toArray _allowedChars;
	
	{
		if !(_x in _allowedArray) exitWith {
			["Gang name contains invalid characters"] call EDRP_fnc_hint;
			false
		};
	} forEach _nameArray;
	
	// Check if player has enough money
	if (EDRP_player_bank < EDRP_gang_creation_cost) exitWith {
		[format ["You need $%1 in your bank to create a gang", [EDRP_gang_creation_cost] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
		false
	};
	
	// Send creation request to server
	[player, getPlayerUID player, _gangName] remoteExec ["EDRP_fnc_insertGang", 2];
	
	["Sending gang creation request..."] call EDRP_fnc_hint;
	
	true
};

// Gang created (server response)
EDRP_fnc_gangCreated = {
	params [
		["_gangID", 0, [0]],
		["_gangName", "", [""]],
		["_success", true, [true]]
	];
	
	if (!_success) exitWith {
		["Gang creation failed"] call EDRP_fnc_hint;
		false
	};
	
	// Deduct creation cost
	EDRP_player_bank = EDRP_player_bank - EDRP_gang_creation_cost;
	
	// Set gang data
	EDRP_gang_data = [_gangID, _gangName, 5, 1]; // Leader rank (5)
	EDRP_gang_bank = 0;
	EDRP_gang_members = [[getPlayerUID player, name player, 5]];
	
	// Set player variables
	player setVariable ["gang_data", EDRP_gang_data, true];
	player setVariable ["gang_rank", 5, true];
	
	// Show success message
	[format ["Gang '%1' created successfully! Cost: $%2", _gangName, [EDRP_gang_creation_cost] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
	
	// Open gang menu
	[] call EDRP_fnc_openGangMenu;
	
	true
};

// Open gang menu
EDRP_fnc_openGangMenu = {
	if (count EDRP_gang_data == 0) exitWith {
		["You are not in a gang"] call EDRP_fnc_hint;
		false
	};
	
	// Create gang dialog
	createDialog "EDRP_GangDialog";
	
	// Populate gang information
	[] call EDRP_fnc_updateGangMenu;
	
	true
};

// Update gang menu
EDRP_fnc_updateGangMenu = {
	private _display = findDisplay 37000;
	if (isNull _display) exitWith {};
	
	EDRP_gang_data params ["_gangID", "_gangName", "_playerRank", "_memberCount"];
	
	// Update gang info
	private _gangInfoCtrl = _display displayCtrl 37001;
	private _gangInfo = format [
		"Gang: %1\nMembers: %2\nBank: $%3\nYour Rank: %4",
		_gangName,
		_memberCount,
		[EDRP_gang_bank] call EDRP_fnc_numberText,
		[_playerRank] call EDRP_fnc_getGangRankName
	];
	_gangInfoCtrl ctrlSetText _gangInfo;
	
	// Update member list
	private _memberList = _display displayCtrl 37003;
	lbClear _memberList;
	
	{
		_x params ["_uid", "_name", "_rank"];
		private _rankName = [_rank] call EDRP_fnc_getGangRankName;
		private _entry = format ["%1 - %2", _name, _rankName];
		
		_memberList lbAdd _entry;
		_memberList lbSetData [_forEachIndex, str _x];
	} forEach EDRP_gang_members;
	
	// Update button states based on rank
	private _canInvite = _playerRank >= 3;
	private _canManage = _playerRank >= 4;
	private _canLeader = _playerRank >= 5;
	
	(_display displayCtrl 37005) ctrlEnable _canInvite; // Invite button
	(_display displayCtrl 37010) ctrlEnable _canManage; // Promote button
	(_display displayCtrl 37011) ctrlEnable _canManage; // Demote button
	(_display displayCtrl 37006) ctrlEnable _canManage; // Kick button
	(_display displayCtrl 37004) ctrlEnable _canLeader; // Leader button
	(_display displayCtrl 37007) ctrlEnable _canLeader; // Disband button
};

// Invite player to gang
EDRP_fnc_inviteToGang = {
	params [["_targetPlayer", objNull, [objNull]]];
	
	if (isNull _targetPlayer || _targetPlayer == player) exitWith {
		["Invalid target for gang invitation"] call EDRP_fnc_hint;
		false
	};
	
	// Check rank permissions
	if ((EDRP_gang_data select 2) < 3) exitWith {
		["You don't have permission to invite players"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is already in a gang
	private _targetGangData = _targetPlayer getVariable ["gang_data", []];
	if (count _targetGangData > 0) exitWith {
		["Target is already in a gang"] call EDRP_fnc_hint;
		false
	};
	
	// Send invitation
	private _gangName = EDRP_gang_data select 1;
	[player, _gangName, EDRP_gang_data select 0] remoteExec ["EDRP_fnc_receiveGangInvite", _targetPlayer];
	
	[format ["Sent gang invitation to %1", name _targetPlayer], "success"] call EDRP_fnc_hint;
	
	true
};

// Receive gang invitation
EDRP_fnc_receiveGangInvite = {
	params [
		["_inviter", objNull, [objNull]],
		["_gangName", "", [""]],
		["_gangID", 0, [0]]
	];
	
	if (isNull _inviter) exitWith {};
	
	// Check if player is already in a gang
	if (count EDRP_gang_data > 0) exitWith {
		[format ["%1 is already in a gang", name player]] remoteExec ["EDRP_fnc_hint", _inviter];
	};
	
	// Show invitation dialog
	private _message = format ["%1 has invited you to join the gang '%2'", name _inviter, _gangName];
	
	[_message, "Gang Invitation", "Accept", "Decline"] spawn {
		params ["_message", "_title", "_button1", "_button2"];
		
		private _result = [_message, _title, _button1, _button2] call EDRP_fnc_messageBox;
		
		if (_result) then {
			// Accept invitation
			[player, _gangID] remoteExec ["EDRP_fnc_joinGang", 2];
		} else {
			// Decline invitation
			[format ["%1 declined the gang invitation", name player]] remoteExec ["EDRP_fnc_hint", _inviter];
		};
	};
};

// Join gang (server-side processing)
EDRP_fnc_joinGang = {
	params [
		["_player", objNull, [objNull]],
		["_gangID", 0, [0]]
	];
	
	if (isNull _player) exitWith {};
	
	// Set gang data for new member
	private _gangData = [_gangID] call EDRP_fnc_getGangInfo;
	if (count _gangData == 0) exitWith {
		["Gang no longer exists"] remoteExec ["EDRP_fnc_hint", _player];
	};
	
	_gangData params ["_id", "_name", "_memberCount"];
	
	// Add to gang with recruit rank
	private _newGangData = [_id, _name, 1, _memberCount + 1];
	[_newGangData] remoteExec ["EDRP_fnc_setGangData", _player];
	
	// Notify gang members
	private _message = format ["%1 has joined the gang", name _player];
	[_message] remoteExec ["EDRP_fnc_gangMessage", _gangID];
};

// Leave gang
EDRP_fnc_leaveGang = {
	if (count EDRP_gang_data == 0) exitWith {
		["You are not in a gang"] call EDRP_fnc_hint;
		false
	};
	
	private _gangName = EDRP_gang_data select 1;
	private _playerRank = EDRP_gang_data select 2;
	
	// Check if player is the leader
	if (_playerRank >= 5) exitWith {
		["Leaders cannot leave the gang - transfer leadership or disband the gang"] call EDRP_fnc_hint;
		false
	};
	
	// Confirm leaving
	private _message = format ["Are you sure you want to leave the gang '%1'?", _gangName];
	if ([_message, "Leave Gang", true, true] call EDRP_fnc_messageBox) then {
		// Send leave request to server
		[player, EDRP_gang_data select 0] remoteExec ["EDRP_fnc_removeFromGang", 2];
		
		// Clear local gang data
		EDRP_gang_data = [];
		EDRP_gang_bank = 0;
		EDRP_gang_members = [];
		
		player setVariable ["gang_data", nil, true];
		player setVariable ["gang_rank", nil, true];
		
		["You have left the gang"] call EDRP_fnc_hint;
		
		true
	} else {
		false
	};
};

// Kick member from gang
EDRP_fnc_kickFromGang = {
	private _display = findDisplay 37000;
	if (isNull _display) exitWith {};
	
	private _memberList = _display displayCtrl 37003;
	private _selection = lbCurSel _memberList;
	
	if (_selection < 0) exitWith {
		["No member selected"] call EDRP_fnc_hint;
	};
	
	private _memberData = call compile (_memberList lbData _selection);
	_memberData params ["_uid", "_name", "_rank"];
	
	// Check permissions
	if ((EDRP_gang_data select 2) < 4) exitWith {
		["You don't have permission to kick members"] call EDRP_fnc_hint;
	};
	
	if (_uid == getPlayerUID player) exitWith {
		["You cannot kick yourself"] call EDRP_fnc_hint;
	};
	
	if (_rank >= (EDRP_gang_data select 2)) exitWith {
		["You cannot kick members of equal or higher rank"] call EDRP_fnc_hint;
	};
	
	// Confirm kick
	private _message = format ["Are you sure you want to kick %1 from the gang?", _name];
	if ([_message, "Kick Member", true, true] call EDRP_fnc_messageBox) then {
		// Send kick request to server
		[_uid, EDRP_gang_data select 0] remoteExec ["EDRP_fnc_kickGangMember", 2];
	};
};

// Promote/demote gang member
EDRP_fnc_manageGangRank = {
	params [["_mode", 1, [0]]]; // 1 = promote, 2 = demote
	
	private _display = findDisplay 37000;
	if (isNull _display) exitWith {};
	
	private _memberList = _display displayCtrl 37003;
	private _selection = lbCurSel _memberList;
	
	if (_selection < 0) exitWith {
		["No member selected"] call EDRP_fnc_hint;
	};
	
	private _memberData = call compile (_memberList lbData _selection);
	_memberData params ["_uid", "_name", "_rank"];
	
	// Check permissions
	if ((EDRP_gang_data select 2) < 4) exitWith {
		["You don't have permission to manage ranks"] call EDRP_fnc_hint;
	};
	
	if (_uid == getPlayerUID player) exitWith {
		["You cannot modify your own rank"] call EDRP_fnc_hint;
	};
	
	private _newRank = _rank;
	private _action = "";
	
	switch (_mode) do {
		case 1: { // Promote
			if ((_rank + 1) >= (EDRP_gang_data select 2)) exitWith {
				["Insufficient permissions to promote to that rank"] call EDRP_fnc_hint;
			};
			_newRank = _rank + 1;
			_action = "promote";
		};
		case 2: { // Demote
			if (_rank >= (EDRP_gang_data select 2) || (_rank - 1) < 1) exitWith {
				["Cannot demote this member"] call EDRP_fnc_hint;
			};
			_newRank = _rank - 1;
			_action = "demote";
		};
	};
	
	// Send rank change request to server
	[_uid, EDRP_gang_data select 0, _newRank] remoteExec ["EDRP_fnc_updateGangMemberRank", 2];
	
	[format ["Sent %1 request for %2", _action, _name]] call EDRP_fnc_hint;
};

// Get gang rank name
EDRP_fnc_getGangRankName = {
	params [["_rank", 1, [0]]];
	
	if (_rank < 1 || _rank > count EDRP_gang_ranks) exitWith { "Unknown" };
	
	(EDRP_gang_ranks select (_rank - 1)) select 0
};

// Get gang rank permissions
EDRP_fnc_getGangRankPermissions = {
	params [["_rank", 1, [0]]];
	
	if (_rank < 1 || _rank > count EDRP_gang_ranks) exitWith { [] };
	
	(EDRP_gang_ranks select (_rank - 1)) select 2
};

// Gang chat
EDRP_fnc_gangChat = {
	params [["_message", "", [""]]];
	
	if (count EDRP_gang_data == 0) exitWith {
		["You are not in a gang"] call EDRP_fnc_hint;
		false
	};
	
	if ((EDRP_gang_data select 2) < 2) exitWith {
		["You don't have permission to use gang chat"] call EDRP_fnc_hint;
		false
	};
	
	if (_message == "") exitWith {
		["Message cannot be empty"] call EDRP_fnc_hint;
		false
	};
	
	// Send gang chat message
	private _formattedMessage = format ["[GANG] %1: %2", name player, _message];
	[_formattedMessage, EDRP_gang_data select 0] remoteExec ["EDRP_fnc_broadcastGangMessage", 2];
	
	true
};

// Initialize gang system on client
if (hasInterface) then {
	[] call EDRP_fnc_initGangSystem;
	
	// Add gang menu action
	player addAction [
		"<t color='#FF8000'>Gang Menu</t>",
		{
			if (count EDRP_gang_data > 0) then {
				[] call EDRP_fnc_openGangMenu;
			} else {
				[] call EDRP_fnc_openGangCreationMenu;
			};
		},
		[],
		1,
		false,
		true,
		"",
		"true"
	];
};
