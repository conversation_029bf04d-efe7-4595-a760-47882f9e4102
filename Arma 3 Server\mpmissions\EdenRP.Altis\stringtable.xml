<?xml version="1.0" encoding="utf-8"?>
<Project name="EdenRP">
	<Package name="General">
		<Key ID="STR_EDRP_ServerName">
			<English>EdenRP Altis Life</English>
		</Key>
		<Key ID="STR_EDRP_Welcome">
			<English>Welcome to EdenRP!</English>
		</Key>
		<Key ID="STR_EDRP_Loading">
			<English>Loading EdenRP...</English>
		</Key>
		<Key ID="STR_EDRP_Connecting">
			<English>Connecting to EdenRP Database...</English>
		</Key>
		<Key ID="STR_EDRP_Connected">
			<English>Connected to EdenRP!</English>
		</Key>
	</Package>
	
	<Package name="Factions">
		<Key ID="STR_EDRP_Police">
			<English>Eden Police Department</English>
		</Key>
		<Key ID="STR_EDRP_Medical">
			<English>Eden Medical Services</English>
		</Key>
		<Key ID="STR_EDRP_Civilian">
			<English>Civilian</English>
		</Key>
	</Package>
	
	<Package name="Jobs">
		<Key ID="STR_EDRP_JobFarmer">
			<English>Farmer</English>
		</Key>
		<Key ID="STR_EDRP_JobMiner">
			<English>Miner</English>
		</Key>
		<Key ID="STR_EDRP_JobFisherman">
			<English>Fisherman</English>
		</Key>
		<Key ID="STR_EDRP_JobTrucker">
			<English>Trucker</English>
		</Key>
		<Key ID="STR_EDRP_JobOfficer">
			<English>Police Officer</English>
		</Key>
		<Key ID="STR_EDRP_JobParamedic">
			<English>Paramedic</English>
		</Key>
	</Package>
	
	<Package name="Actions">
		<Key ID="STR_EDRP_ActionGather">
			<English>Gather</English>
		</Key>
		<Key ID="STR_EDRP_ActionProcess">
			<English>Process</English>
		</Key>
		<Key ID="STR_EDRP_ActionSell">
			<English>Sell</English>
		</Key>
		<Key ID="STR_EDRP_ActionBuy">
			<English>Buy</English>
		</Key>
		<Key ID="STR_EDRP_ActionArrest">
			<English>Arrest</English>
		</Key>
		<Key ID="STR_EDRP_ActionRevive">
			<English>Revive</English>
		</Key>
		<Key ID="STR_EDRP_ActionRepair">
			<English>Repair</English>
		</Key>
		<Key ID="STR_EDRP_ActionRefuel">
			<English>Refuel</English>
		</Key>
	</Package>
	
	<Package name="UI">
		<Key ID="STR_EDRP_MenuInventory">
			<English>Inventory</English>
		</Key>
		<Key ID="STR_EDRP_MenuPhone">
			<English>Phone</English>
		</Key>
		<Key ID="STR_EDRP_MenuMap">
			<English>Map</English>
		</Key>
		<Key ID="STR_EDRP_MenuAdmin">
			<English>Admin Panel</English>
		</Key>
		<Key ID="STR_EDRP_MenuGang">
			<English>Gang Management</English>
		</Key>
		<Key ID="STR_EDRP_MenuHouse">
			<English>House Management</English>
		</Key>
		<Key ID="STR_EDRP_MenuVehicle">
			<English>Vehicle Management</English>
		</Key>
	</Package>
	
	<Package name="Messages">
		<Key ID="STR_EDRP_MsgInsufficientFunds">
			<English>Insufficient funds!</English>
		</Key>
		<Key ID="STR_EDRP_MsgTransactionComplete">
			<English>Transaction completed successfully!</English>
		</Key>
		<Key ID="STR_EDRP_MsgAccessDenied">
			<English>Access denied!</English>
		</Key>
		<Key ID="STR_EDRP_MsgPlayerNotFound">
			<English>Player not found!</English>
		</Key>
		<Key ID="STR_EDRP_MsgVehicleSpawned">
			<English>Vehicle spawned successfully!</English>
		</Key>
		<Key ID="STR_EDRP_MsgVehicleStored">
			<English>Vehicle stored successfully!</English>
		</Key>
		<Key ID="STR_EDRP_MsgHousePurchased">
			<English>House purchased successfully!</English>
		</Key>
		<Key ID="STR_EDRP_MsgHouseSold">
			<English>House sold successfully!</English>
		</Key>
	</Package>
	
	<Package name="Items">
		<Key ID="STR_EDRP_ItemApple">
			<English>Apple</English>
		</Key>
		<Key ID="STR_EDRP_ItemPeach">
			<English>Peach</English>
		</Key>
		<Key ID="STR_EDRP_ItemWater">
			<English>Water Bottle</English>
		</Key>
		<Key ID="STR_EDRP_ItemBread">
			<English>Bread</English>
		</Key>
		<Key ID="STR_EDRP_ItemMoney">
			<English>Cash</English>
		</Key>
		<Key ID="STR_EDRP_ItemGold">
			<English>Gold Bar</English>
		</Key>
		<Key ID="STR_EDRP_ItemDiamond">
			<English>Diamond</English>
		</Key>
		<Key ID="STR_EDRP_ItemCopper">
			<English>Copper Ore</English>
		</Key>
		<Key ID="STR_EDRP_ItemIron">
			<English>Iron Ore</English>
		</Key>
		<Key ID="STR_EDRP_ItemSalt">
			<English>Salt</English>
		</Key>
		<Key ID="STR_EDRP_ItemOil">
			<English>Oil</English>
		</Key>
		<Key ID="STR_EDRP_ItemHeroin">
			<English>Heroin</English>
		</Key>
		<Key ID="STR_EDRP_ItemCocaine">
			<English>Cocaine</English>
		</Key>
		<Key ID="STR_EDRP_ItemMarijuana">
			<English>Marijuana</English>
		</Key>
	</Package>
	
	<Package name="Vehicles">
		<Key ID="STR_EDRP_VehicleCar">
			<English>Car</English>
		</Key>
		<Key ID="STR_EDRP_VehicleTruck">
			<English>Truck</English>
		</Key>
		<Key ID="STR_EDRP_VehicleHelicopter">
			<English>Helicopter</English>
		</Key>
		<Key ID="STR_EDRP_VehiclePlane">
			<English>Plane</English>
		</Key>
		<Key ID="STR_EDRP_VehicleBoat">
			<English>Boat</English>
		</Key>
		<Key ID="STR_EDRP_VehicleMotorcycle">
			<English>Motorcycle</English>
		</Key>
	</Package>
	
	<Package name="Locations">
		<Key ID="STR_EDRP_LocationKavala">
			<English>Kavala</English>
		</Key>
		<Key ID="STR_EDRP_LocationAthira">
			<English>Athira</English>
		</Key>
		<Key ID="STR_EDRP_LocationPyrgos">
			<English>Pyrgos</English>
		</Key>
		<Key ID="STR_EDRP_LocationSofia">
			<English>Sofia</English>
		</Key>
		<Key ID="STR_EDRP_LocationAirport">
			<English>Airport</English>
		</Key>
		<Key ID="STR_EDRP_LocationHospital">
			<English>Hospital</English>
		</Key>
		<Key ID="STR_EDRP_LocationPoliceStation">
			<English>Police Station</English>
		</Key>
		<Key ID="STR_EDRP_LocationGarage">
			<English>Garage</English>
		</Key>
		<Key ID="STR_EDRP_LocationShop">
			<English>Shop</English>
		</Key>
		<Key ID="STR_EDRP_LocationBank">
			<English>Bank</English>
		</Key>
	</Package>
</Project>
