// Developer info: https://github.com/ConnorAU/SQFDiscordEmbedBuilder/blob/master/README.md

class CfgDiscordEmbedWebhooks {
    // https://discord.com/api/webhooks/1400074571872337981/z9vRZa7iuD7bb8iZBqowFvFtWI-_deeQpoexYX-0umEXC2Q6JcJHlf857ufJrh3Cbs5i
    prod="1400074571872337981/z9vRZa7iuD7bb8iZBqowFvFtWI-_deeQpoexYX-0umEXC2Q6JcJHlf857ufJrh3Cbs5i";
};

class CfgDiscordEmbedTemplate {
    title="";
    description="";
    url="";
    color="";
    timestamp=0;
    thumbnail="";
    image="";
    class Author {
        name="";
        url="";
        image="";
    };
    class Footer {
        text="";
        image="";
    };
    // Maximum 25 fields per embed
    fields[]={
        //{title,content,inline}
    };
};

class CfgDiscordEmbedBuilder {
	#include "\@DiscordEmbedBuilder\external\Example.cpp"
	#include "\@DiscordEmbedBuilder\external\Example2.cpp"
};
