#include "..\..\macro.h"
//	Author: x00
//	File: fn_beatdown.sqf (EDEN_fnc_beatdown)

private["_player","_unit"];

_player = param [0,<PERSON>bj<PERSON><PERSON>,[<PERSON>b<PERSON><PERSON><PERSON>]];
_unit = param [1,<PERSON>b<PERSON><PERSON><PERSON>,[<PERSON>b<PERSON><PERSON><PERSON>]];

if(isNull _player || isNull _unit) exitWith {};
if(!(_unit getVariable["restrained", false])) exitWith {};
if(currentWeapon _player != "") exitWith {hint "Please holster your weapon before smacking the mofo!"};

if (eden_beatdown_active) exitWith {hint "You need to wait at least 2 minutes before smacking the mofo again!"};

if (_player distance _unit < 1) then {
	eden_beatdown_active = true;
	player playMoveNow "AmovPercMstpSnonWnonDnon_AcrgPknlMstpSnonWnonDnon_getInLow";
	sleep 0.3;
	[[_unit,"AinjPfalMstpSnonWnonDf_carried_fallwc"],"EDEN_fnc_animSync",-2,false] spawn EDEN_fnc_MP;
	[[_unit,"kick_balls"],"EDEN_fnc_say3D",-2,false] spawn EDEN_fnc_MP;
	[[_unit], "EDEN_fnc_handleBeatdown", _unit, false] spawn EDEN_fnc_MP;
	//add charge of Assault
	if (side _player isEqualTo civilian) then {
		[[getPlayerUID _player,_player getVariable["realname",name _player],"4",_player],"EDENS_fnc_wantedAdd",false,false] spawn EDEN_fnc_MP;
	};
};
uiSleep 120;
eden_beatdown_active = false;