// File: fn_autoRun.sqf
// Authors: <AUTHORS>
// Description: Starts script to run if Custom action 18 is pressed.
#include "..\..\macro.h"

if(scriptAvailable(5)) exitWith {hint "Please do not spam autorun!";};

params[];

eden_autorun = not eden_autorun;
eden_autoswim = false;
eden_interrupted = false;

if (eden_autorun) then
{
	titleText ["Starting Auto Run... Move or press tab to stop","PLAIN DOWN"];

	if(currentWeapon player != "") then {
		life_curWep_h = currentWeapon player; // Player automatically holsters weapon prior to running
		player action ["SwitchWeapon", player, player, 100];
		player switchCamera cameraView;
	};

		12 cutRsc ["life_autorun","PLAIN DOWN"];
		_uiDisp = uiNamespace getVariable "life_autorun";
		_timer = _uiDisp displayCtrl 38310;
		_timer ctrlSetText "Autorun Active";

	while {eden_autorun} do {
			// Checks before running
		if(eden_interruptedTab) exitWith {
			eden_interruptedTab = false;
			eden_autorun = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if(eden_interrupted) exitWith {
			eden_interrupted = false;
			eden_autorun = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if (eden_action_inUse) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			eden_action_inUse = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if (eden_lockpick) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			eden_action_inUse = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if (eden_is_processing) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if !(alive player) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			12 cutText["","PLAIN DOWN"];
		};

		if ((diag_tickTime - eden_healingTime) <= 8) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			titleText["You cannot run while healing!","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if !(isTouchingGround player || surfaceIsWater position player) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			titleText["You need to be on land or in the water to run!","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if (eden_isDowned) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if (player getVariable["jailed",false]) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			titleText["You cannot auto run in jail!","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if(vehicle player != player) exitWith {
			eden_autorun = false;
			eden_interrupted = false;
			eden_action_inUse = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if (player getHit "legs" >= 0.5) exitWith {
			eden_autorun = false;
			titleText["You cannot run while limping!","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};

		if (getFatigue player isEqualTo 1) then {
			if(surfaceIsWater position player && !(isTouchingGround player)) then {
				if(uniform player in ["U_O_Wetsuit","U_I_Wetsuit","U_B_Wetsuit"]) then {
					player playMoveNow "advepercmrunsnonwnondf";
					eden_autoswim = true;
				} else {
					player playMoveNow "aswmpercmrunsnonwnondf";
					eden_autoswim = true;
				};
			} else {
				player playMoveNow "AmovPercMrunSnonWnonDf"; // Player runs slow when fatigued
			};

			// Player drinks their redgull/coffee/lollypop auto-matically when fatigued
			if((time - eden_redgull_effect) < 10) exitWith {_handled = true;}; // Prevents spamming the key and drink all yo redgulls
			if (player getVariable ["restrained",false]) exitWith {_handled = true;};
			if ((life_inv_redgull > 0) || (life_inv_coffee > 0) || (life_inv_lollypop > 0)) then {
				if ([false,"redgull",1] call EDEN_fnc_handleInv) exitWith {
					["redgull"] spawn EDEN_fnc_eatOrDrink;
					uisleep .2;
				};
				if ([false,"coffee",1] call EDEN_fnc_handleInv) exitWith {
					["coffee"] spawn EDEN_fnc_eatOrDrink;
					uisleep .2;
				};
				if ([false,"lollypop",1] call EDEN_fnc_handleInv) exitWith {
					["lollypop"] spawn EDEN_fnc_eatOrDrink;
					uisleep .2;
				};
			};
		};
		if (getFatigue player < 1) then {
			if(surfaceIsWater position player && !(isTouchingGround player)) then {
				if(uniform player in ["U_O_Wetsuit","U_I_Wetsuit","U_B_Wetsuit"]) then {
					player playMoveNow "advepercmrunsnonwnondf";
					eden_autoswim = true;
				} else {
					player playMoveNow "aswmpercmrunsnonwnondf";
					eden_autoswim = true;
				};
			} else {
				player playMoveNow "AmovPercMevaSnonWnonDf"; // Player runs fast when not fatigued
			};
		};
		if((surfaceIsWater position player && !eden_autoswim) || (!surfaceIsWater position player && eden_autoswim)) then {
			eden_autorun = false;
			eden_interrupted = false;
			eden_action_inUse = false;
			titleText["Auto Run has been cancelled","PLAIN DOWN"];
			player playMoveNow "stop";
			12 cutText["","PLAIN DOWN"];
		};
	};
}
else
{
		titleText["Auto Run has been cancelled","PLAIN DOWN"];
		player switchMove "";
		12 cutText["","PLAIN DOWN"];
};
