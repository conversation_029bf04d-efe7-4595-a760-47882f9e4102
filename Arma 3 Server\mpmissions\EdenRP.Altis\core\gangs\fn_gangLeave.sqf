#include <zmacro.h>
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: 32 hours later...

private["_grp"];
if((eden_gang_data select 2) == 5) exitWith {hint localize "STR_GNOTF_LeaderLeave"};

if(!isNil {(group player) getVariable "gang_id"}) then {
	[player] joinSilent (createGroup civilian);
};

[[2,player,0,eden_gang_data select 1,-1],"EDENS_fnc_updateMember",false,false] spawn EDEN_fnc_MP;
if (count eden_gang_data isEqualTo 4) then {
	[1] call EDEN_fnc_gangBldgDraw;
	eden_gangShedPos = [];
};
eden_gang_data = [];
eden_gang_activeWars = [];
eden_gang_warIDs = [];
eden_gang_warAccptIDs = [];
player setVariable["gang_data",nil,true];

['yMenuCreateGang'] spawn EDEN_fnc_createDialog;
