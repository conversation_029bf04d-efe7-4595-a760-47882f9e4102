/*
	EdenRP Altis Life - License Configuration
	Author: EdenRP Development Team
	Description: Configuration for all licenses and permits
	Version: 1.0.0
*/

class CfgLicenses {
	// Civilian Licenses
	class CivilianLicenses {
		class driver {
			displayName = "Driver's License";
			price = 500;
			illegal = 0;
			side = "civ";
			description = "Allows operation of civilian vehicles on public roads.";
		};

		class boat {
			displayName = "Boat License";
			price = 1000;
			illegal = 0;
			side = "civ";
			description = "Permits operation of civilian watercraft.";
		};

		class pilot {
			displayName = "Pilot License";
			price = 25000;
			illegal = 0;
			side = "civ";
			description = "Authorizes operation of civilian aircraft.";
		};

		class gun {
			displayName = "Firearms License";
			price = 10000;
			illegal = 0;
			side = "civ";
			description = "Permits legal ownership and carry of firearms.";
		};

		class truck {
			displayName = "Commercial Driving License";
			price = 2500;
			illegal = 0;
			side = "civ";
			description = "Required for operating commercial vehicles.";
		};

		class trucking {
			displayName = "Heavy Vehicle License";
			price = 15000;
			illegal = 0;
			side = "civ";
			description = "Permits operation of heavy commercial vehicles.";
		};

		class rebel {
			displayName = "Rebel Training";
			price = 500000;
			illegal = 1;
			side = "civ";
			description = "Access to rebel equipment and training.";
		};

		class home {
			displayName = "Home Owner's License";
			price = 75000;
			illegal = 0;
			side = "civ";
			description = "Permits purchase and ownership of residential property.";
		};

		class business {
			displayName = "Business License";
			price = 50000;
			illegal = 0;
			side = "civ";
			description = "Required for operating commercial businesses.";
		};
	};

	// Professional Licenses
	class ProfessionalLicenses {
		class mining {
			displayName = "Mining Permit";
			price = 7500;
			illegal = 0;
			side = "civ";
			description = "Authorizes mineral extraction operations.";
		};

		class oil {
			displayName = "Oil Extraction License";
			price = 10000;
			illegal = 0;
			side = "civ";
			description = "Permits crude oil extraction and processing.";
		};

		class diamond {
			displayName = "Diamond Mining License";
			price = 35000;
			illegal = 0;
			side = "civ";
			description = "Exclusive permit for diamond mining operations.";
		};

		class salt {
			displayName = "Salt Mining Permit";
			price = 12000;
			illegal = 0;
			side = "civ";
			description = "Authorizes salt extraction and processing.";
		};

		class copper {
			displayName = "Copper Mining License";
			price = 8000;
			illegal = 0;
			side = "civ";
			description = "Permits copper ore extraction.";
		};

		class iron {
			displayName = "Iron Mining License";
			price = 9500;
			illegal = 0;
			side = "civ";
			description = "Authorizes iron ore mining operations.";
		};

		class gold {
			displayName = "Gold Mining License";
			price = 25000;
			illegal = 0;
			side = "civ";
			description = "Exclusive permit for gold mining.";
		};

		class fishing {
			displayName = "Commercial Fishing License";
			price = 2500;
			illegal = 0;
			side = "civ";
			description = "Permits commercial fishing operations.";
		};

		class hunting {
			displayName = "Hunting License";
			price = 15000;
			illegal = 0;
			side = "civ";
			description = "Authorizes hunting of wildlife.";
		};

		class turtle {
			displayName = "Turtle Hunting Permit";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Illegal permit for turtle hunting.";
		};
	};

	// Processing Licenses
	class ProcessingLicenses {
		class heroin {
			displayName = "Heroin Processing";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Illegal heroin processing knowledge.";
		};

		class cocaine {
			displayName = "Cocaine Processing";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Illegal cocaine processing knowledge.";
		};

		class marijuana {
			displayName = "Marijuana Processing";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Illegal marijuana processing knowledge.";
		};

		class moonshine {
			displayName = "Moonshine Distilling";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Illegal alcohol distillation knowledge.";
		};
	};

	// Gang Licenses
	class GangLicenses {
		class gang_leader {
			displayName = "Gang Leadership";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Gang leadership authority.";
		};

		class gang_member {
			displayName = "Gang Membership";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Gang membership status.";
		};

		class territory {
			displayName = "Territory Control";
			price = 0;
			illegal = 1;
			side = "civ";
			description = "Gang territory control rights.";
		};
	};

	// Police Licenses (Certifications)
	class PoliceLicenses {
		class patrol {
			displayName = "Patrol Certification";
			price = 0;
			illegal = 0;
			side = "cop";
			description = "Basic patrol officer certification.";
		};

		class detective {
			displayName = "Detective Certification";
			price = 0;
			illegal = 0;
			side = "cop";
			description = "Criminal investigation certification.";
		};

		class swat {
			displayName = "SWAT Certification";
			price = 0;
			illegal = 0;
			side = "cop";
			description = "Special weapons and tactics certification.";
		};

		class air_unit {
			displayName = "Air Unit Certification";
			price = 0;
			illegal = 0;
			side = "cop";
			description = "Police aviation certification.";
		};

		class k9 {
			displayName = "K9 Handler Certification";
			price = 0;
			illegal = 0;
			side = "cop";
			description = "Police K9 unit handler certification.";
		};

		class traffic {
			displayName = "Traffic Enforcement";
			price = 0;
			illegal = 0;
			side = "cop";
			description = "Traffic enforcement certification.";
		};

		class narcotics {
			displayName = "Narcotics Division";
			price = 0;
			illegal = 0;
			side = "cop";
			description = "Drug enforcement certification.";
		};
	};

	// Medical Licenses (Certifications)
	class MedicalLicenses {
		class emt {
			displayName = "EMT Certification";
			price = 0;
			illegal = 0;
			side = "med";
			description = "Emergency Medical Technician certification.";
		};

		class paramedic {
			displayName = "Paramedic Certification";
			price = 0;
			illegal = 0;
			side = "med";
			description = "Advanced paramedic certification.";
		};

		class flight_medic {
			displayName = "Flight Medic Certification";
			price = 0;
			illegal = 0;
			side = "med";
			description = "Air medical transport certification.";
		};

		class trauma {
			displayName = "Trauma Specialist";
			price = 0;
			illegal = 0;
			side = "med";
			description = "Trauma care specialist certification.";
		};

		class surgeon {
			displayName = "Surgical Certification";
			price = 0;
			illegal = 0;
			side = "med";
			description = "Surgical procedures certification.";
		};

		class pharmacy {
			displayName = "Pharmacy License";
			price = 0;
			illegal = 0;
			side = "med";
			description = "Pharmaceutical dispensing license.";
		};
	};

	// Special Permits
	class SpecialPermits {
		class donator {
			displayName = "Donator Status";
			price = 0;
			illegal = 0;
			side = "civ";
			description = "Server donator benefits and privileges.";
		};

		class vip {
			displayName = "VIP Status";
			price = 0;
			illegal = 0;
			side = "civ";
			description = "VIP member benefits and privileges.";
		};

		class premium {
			displayName = "Premium Status";
			price = 0;
			illegal = 0;
			side = "civ";
			description = "Premium member benefits and privileges.";
		};

		class event {
			displayName = "Event Participant";
			price = 0;
			illegal = 0;
			side = "civ";
			description = "Special event participation permit.";
		};

		class beta_tester {
			displayName = "Beta Tester";
			price = 0;
			illegal = 0;
			side = "civ";
			description = "Beta testing access and privileges.";
		};
	};
};
