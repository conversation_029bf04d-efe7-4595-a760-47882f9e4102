/*
	EdenRP Altis Life - Admin Panel
	Author: EdenRP Development Team
	Description: Administrative tools and management interface
	Version: 1.0.0
*/

class EDRP_AdminPanel: EDRP_MenuBase {
	idd = 6000;
	onLoad = "[] spawn EDRP_fnc_updateAdminPanel;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "EdenRP - Admin Panel";
			colorBackground[] = {0.8, 0.2, 0.2, 1}; // Red for admin
		};
		class Header: BaseHeader {
			text = "Server Administration Tools";
			colorBackground[] = {0.6, 0.1, 0.1, 1};
		};
		
		// Tab backgrounds
		class Tab1Background: BaseTab1Background {};
		class Tab2Background: BaseTab2Background {};
		class Tab3Background: BaseTab3Background {};
		class Tab4Background: BaseTab4Background {};
		class Tab5Background: BaseTab5Background {};
		class Tab6Background: BaseTab6Background {};
		
		class SidePanelBackground: BaseSidePanelBackground {};
		class MainContentBackground: BaseMainContentBackground {};
	};
	
	class controls: controlsBase {
		// Admin tabs
		class PlayersTab: BaseTab1 {
			text = "Players";
			tooltip = "Player management";
			onButtonClick = "[6000, 'players'] call EDRP_fnc_switchAdminTab;";
		};
		
		class ActionsTab: BaseTab2 {
			text = "Actions";
			tooltip = "Administrative actions";
			onButtonClick = "[6000, 'actions'] call EDRP_fnc_switchAdminTab;";
		};
		
		class VehiclesTab: BaseTab3 {
			text = "Vehicles";
			tooltip = "Vehicle management";
			onButtonClick = "[6000, 'vehicles'] call EDRP_fnc_switchAdminTab;";
		};
		
		class ServerTab: BaseTab4 {
			text = "Server";
			tooltip = "Server management";
			onButtonClick = "[6000, 'server'] call EDRP_fnc_switchAdminTab;";
		};
		
		class LogsTab: BaseTab5 {
			text = "Logs";
			tooltip = "Server logs";
			onButtonClick = "[6000, 'logs'] call EDRP_fnc_switchAdminTab;";
		};
		
		class SettingsTab: BaseTab6 {
			text = "Settings";
			tooltip = "Admin settings";
			onButtonClick = "[6000, 'settings'] call EDRP_fnc_switchAdminTab;";
		};
		
		// Player list (side panel)
		class PlayerListTitle: EDRP_RscText {
			idc = -1;
			text = "Online Players";
			x = 0.22;
			y = 0.26;
			w = 0.16;
			h = 0.02;
			style = 2;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			sizeEx = 0.03;
		};
		
		class PlayerList: BaseSideListBox {
			idc = 6001;
			x = 0.22;
			y = 0.28;
			w = 0.16;
			h = 0.45;
			onLBSelChanged = "[] call EDRP_fnc_adminPlayerSelected;";
			onLBDblClick = "[] call EDRP_fnc_adminPlayerDoubleClick;";
		};
		
		class PlayerCountText: EDRP_RscText {
			idc = 6002;
			text = "Players: 0/120";
			x = 0.22;
			y = 0.74;
			w = 0.16;
			h = 0.02;
			style = 2;
			sizeEx = 0.025;
		};
		
		// Main content area
		class PlayerInfoText: EDRP_RscStructuredText {
			idc = 6010;
			text = "Select a player to view information";
			x = 0.41;
			y = 0.28;
			w = 0.37;
			h = 0.15;
			size = 0.03;
		};
		
		class ActionsList: BaseMainListBox {
			idc = 6011;
			x = 0.41;
			y = 0.45;
			w = 0.37;
			h = 0.28;
			onLBDblClick = "[] call EDRP_fnc_executeAdminAction;";
		};
		
		// Quick action buttons
		class KickButton: EDRP_RscButtonMenu {
			idc = 6020;
			text = "Kick";
			x = 0.22;
			y = 0.77;
			w = 0.06;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_kickPlayer;";
		};
		
		class BanButton: EDRP_RscButtonMenu {
			idc = 6021;
			text = "Ban";
			x = 0.29;
			y = 0.77;
			w = 0.06;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "[] call EDRP_fnc_banPlayer;";
		};
		
		class TeleportButton: EDRP_RscButtonMenu {
			idc = 6022;
			text = "Teleport";
			x = 0.41;
			y = 0.77;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_teleportToPlayer;";
		};
		
		class SpectateButton: EDRP_RscButtonMenu {
			idc = 6023;
			text = "Spectate";
			x = 0.50;
			y = 0.77;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			onButtonClick = "[] call EDRP_fnc_spectatePlayer;";
		};
		
		class HealButton: EDRP_RscButtonMenu {
			idc = 6024;
			text = "Heal";
			x = 0.59;
			y = 0.77;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_healPlayer;";
		};
		
		class FreezeButton: EDRP_RscButtonMenu {
			idc = 6025;
			text = "Freeze";
			x = 0.68;
			y = 0.77;
			w = 0.08;
			h = 0.04;
			colorBackground[] = {0.5, 0.5, 1, 0.8};
			onButtonClick = "[] call EDRP_fnc_freezePlayer;";
		};
		
		// Input fields
		class ReasonEdit: EDRP_RscEdit {
			idc = 6030;
			text = "Reason...";
			x = 0.22;
			y = 0.82;
			w = 0.3;
			h = 0.04;
		};
		
		class AmountEdit: EDRP_RscEdit {
			idc = 6031;
			text = "0";
			x = 0.53;
			y = 0.82;
			w = 0.1;
			h = 0.04;
		};
		
		class MessageEdit: EDRP_RscEdit {
			idc = 6032;
			text = "Message...";
			x = 0.64;
			y = 0.82;
			w = 0.14;
			h = 0.04;
		};
		
		// Close button
		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 6000; [] call EDRP_fnc_closeAdminPanel;";
		};
	};
};

// Admin action confirmation dialog
class EDRP_AdminConfirmDialog: EDRP_MenuBaseCompact {
	idd = 6100;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Confirm Action";
			colorBackground[] = EDRP_COLOR_WARNING;
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class ConfirmText: EDRP_RscStructuredText {
			idc = 6101;
			text = "Are you sure you want to perform this action?";
			x = 0.32;
			y = 0.28;
			w = 0.36;
			h = 0.2;
			size = 0.03;
		};
		
		class ReasonEdit: EDRP_RscEdit {
			idc = 6102;
			text = "";
			x = 0.32;
			y = 0.5;
			w = 0.36;
			h = 0.04;
		};
		
		class ReasonLabel: EDRP_RscText {
			idc = -1;
			text = "Reason (required):";
			x = 0.32;
			y = 0.48;
			w = 0.36;
			h = 0.02;
		};
		
		class ConfirmButton: BaseButtonOK {
			text = "Confirm";
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "[] call EDRP_fnc_confirmAdminAction; closeDialog 6100;";
		};
		
		class ButtonClose: BaseButtonClose {};
	};
};

// Server management dialog
class EDRP_ServerManagementDialog: EDRP_MenuBase {
	idd = 6200;
	onLoad = "[] spawn EDRP_fnc_updateServerManagement;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Server Management";
			colorBackground[] = {0.8, 0.2, 0.2, 1};
		};
		class Header: BaseHeader {
			text = "Server Control and Monitoring";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		// Server info
		class ServerInfoText: EDRP_RscStructuredText {
			idc = 6201;
			text = "";
			x = 0.22;
			y = 0.28;
			w = 0.56;
			h = 0.2;
			size = 0.03;
		};
		
		// Server controls
		class RestartButton: EDRP_RscButtonMenu {
			idc = 6202;
			text = "Restart Server";
			x = 0.22;
			y = 0.5;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "[] call EDRP_fnc_restartServer;";
		};
		
		class LockButton: EDRP_RscButtonMenu {
			idc = 6203;
			text = "Lock Server";
			x = 0.35;
			y = 0.5;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_lockServer;";
		};
		
		class MessageAllButton: EDRP_RscButtonMenu {
			idc = 6204;
			text = "Message All";
			x = 0.48;
			y = 0.5;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_messageAllPlayers;";
		};
		
		class SaveDataButton: EDRP_RscButtonMenu {
			idc = 6205;
			text = "Save All Data";
			x = 0.61;
			y = 0.5;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_saveAllData;";
		};
		
		// Economy controls
		class EconomyTitle: EDRP_RscText {
			idc = -1;
			text = "Economy Controls";
			x = 0.22;
			y = 0.56;
			w = 0.56;
			h = 0.03;
			style = 2;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			sizeEx = 0.03;
		};
		
		class ResetEconomyButton: EDRP_RscButtonMenu {
			idc = 6206;
			text = "Reset Economy";
			x = 0.22;
			y = 0.6;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "[] call EDRP_fnc_resetEconomy;";
		};
		
		class UpdatePricesButton: EDRP_RscButtonMenu {
			idc = 6207;
			text = "Update Prices";
			x = 0.35;
			y = 0.6;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_updatePrices;";
		};
		
		// Event controls
		class EventTitle: EDRP_RscText {
			idc = -1;
			text = "Event Controls";
			x = 0.22;
			y = 0.66;
			w = 0.56;
			h = 0.03;
			style = 2;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			sizeEx = 0.03;
		};
		
		class StartEventButton: EDRP_RscButtonMenu {
			idc = 6208;
			text = "Start Event";
			x = 0.22;
			y = 0.7;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_startEvent;";
		};
		
		class EndEventButton: EDRP_RscButtonMenu {
			idc = 6209;
			text = "End Event";
			x = 0.35;
			y = 0.7;
			w = 0.12;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_endEvent;";
		};
		
		class EventCombo: EDRP_RscCombo {
			idc = 6210;
			x = 0.48;
			y = 0.7;
			w = 0.25;
			h = 0.04;
		};
		
		// Close button
		class ButtonClose: BaseButtonClose {
			onButtonClick = "closeDialog 6200;";
		};
	};
};
