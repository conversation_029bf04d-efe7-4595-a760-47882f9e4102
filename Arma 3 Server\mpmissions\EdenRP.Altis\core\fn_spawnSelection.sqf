/*
	EdenRP Altis Life - Spawn Selection System
	Author: EdenRP Development Team
	Description: Handles player spawn location selection and management
	Version: 1.0.0
*/

// Main spawn selection function
EDRP_fnc_spawnSelection = {
	diag_log "EdenRP: Starting spawn selection...";
	
	EDRP_loading_status = "<t color='#FFFF00'>Select spawn location...</t>";
	EDRP_loading_progress = 55;
	
	// Get available spawn locations based on faction
	private _spawnLocations = call EDRP_fnc_getSpawnLocations;
	
	// Show spawn selection dialog
	[_spawnLocations] call EDRP_fnc_showSpawnDialog;
	
	// Wait for spawn selection
	waitUntil {!isNil "EDRP_selected_spawn"};
	
	// Process spawn selection
	[EDRP_selected_spawn] call EDRP_fnc_processSpawnSelection;
	
	diag_log "EdenRP: Spawn selection completed";
};

// Get spawn locations based on faction
EDRP_fnc_getSpawnLocations = {
	private _locations = [];
	
	switch (playerSide) do {
		case west: { // Police
			_locations = [
				["Kavala Police Station", [3560.26, 13724.2, 0.00143909], "kavala_pd"],
				["Pyrgos Police Station", [14677.9, 16678.5, 0.00143909], "pyrgos_pd"],
				["Athira Police Station", [14251.8, 18803.5, 0.00143909], "athira_pd"],
				["Sofia Police Station", [25327.3, 21373.8, 0.00143909], "sofia_pd"],
				["Air HQ", [16049.5, 16952.9, 0.00143909], "air_hq"]
			];
			
			// Filter by rank access
			private _filteredLocations = [];
			{
				_x params ["_name", "_pos", "_code"];
				private _requiredRank = switch (_code) do {
					case "air_hq": {5}; // Sergeant+
					default {1}; // All ranks
				};
				
				if (EDRP_police_rank >= _requiredRank) then {
					_filteredLocations pushBack _x;
				};
			} forEach _locations;
			
			_locations = _filteredLocations;
		};
		
		case independent: { // Medical
			_locations = [
				["Kavala Hospital", [3529.98, 13650.7, 0.00143909], "kavala_hospital"],
				["Pyrgos Hospital", [14677.2, 16759.8, 0.00143909], "pyrgos_hospital"],
				["Athira Hospital", [14251.1, 18803.9, 0.00143909], "athira_hospital"],
				["Sofia Hospital", [25327.8, 21373.2, 0.00143909], "sofia_hospital"],
				["Air Rescue", [16049.1, 16952.3, 0.00143909], "air_rescue"]
			];
			
			// Filter by rank access
			private _filteredLocations = [];
			{
				_x params ["_name", "_pos", "_code"];
				private _requiredRank = switch (_code) do {
					case "air_rescue": {4}; // Field Supervisor+
					default {1}; // All ranks
				};
				
				if (EDRP_medical_rank >= _requiredRank) then {
					_filteredLocations pushBack _x;
				};
			} forEach _locations;
			
			_locations = _filteredLocations;
		};
		
		case civilian: { // Civilian
			_locations = [
				["Kavala", [3560.26, 13724.2, 0.00143909], "kavala"],
				["Pyrgos", [14677.9, 16678.5, 0.00143909], "pyrgos"],
				["Athira", [14251.8, 18803.5, 0.00143909], "athira"],
				["Sofia", [25327.3, 21373.8, 0.00143909], "sofia"],
				["Zaros", [8866.91, 18244.1, 0.00143909], "zaros"],
				["Paros", [23398.4, 19870.3, 0.00143909], "paros"],
				["Neochori", [13414.4, 14638.5, 0.00143909], "neochori"]
			];
			
			// Add gang hideout if player is in a gang
			if (EDRP_player_gang != "") then {
				private _gangHideout = [EDRP_player_gang] call EDRP_fnc_getGangHideout;
				if (count _gangHideout > 0) then {
					_locations pushBack ["Gang Hideout", _gangHideout, "gang_hideout"];
				};
			};
			
			// Add owned houses
			private _houses = EDRP_player_houses;
			{
				_x params ["_houseId", "_houseName", "_housePos"];
				_locations pushBack [format ["House: %1", _houseName], _housePos, format ["house_%1", _houseId]];
			} forEach _houses;
		};
	};
	
	_locations
};

// Show spawn selection dialog
EDRP_fnc_showSpawnDialog = {
	params ["_locations"];
	
	// Create spawn selection dialog
	createDialog "EDRP_SpawnSelection";
	
	private _display = findDisplay 2800;
	private _listbox = _display displayCtrl 2801;
	private _map = _display displayCtrl 2802;
	
	// Clear listbox
	lbClear _listbox;
	
	// Populate spawn locations
	{
		_x params ["_name", "_pos", "_code"];
		private _index = _listbox lbAdd _name;
		_listbox lbSetData [_index, _code];
		_listbox lbSetValue [_index, _forEachIndex];
	} forEach _locations;
	
	// Set default selection
	_listbox lbSetCurSel 0;
	
	// Setup map
	_map ctrlMapAnimAdd [0, 0.1, [3560, 13724, 0]];
	ctrlMapAnimCommit _map;
	
	// Add map markers for spawn locations
	{
		_x params ["_name", "_pos", "_code"];
		private _marker = createMarkerLocal [format ["spawn_%1", _code], _pos];
		_marker setMarkerTypeLocal "mil_dot";
		_marker setMarkerColorLocal "ColorBlue";
		_marker setMarkerTextLocal _name;
		_marker setMarkerSizeLocal [0.8, 0.8];
	} forEach _locations;
	
	// Setup event handlers
	_listbox ctrlAddEventHandler ["LBSelChanged", {
		params ["_control", "_selectedIndex"];
		
		private _locations = missionNamespace getVariable ["EDRP_spawn_locations", []];
		if (_selectedIndex < count _locations) then {
			private _selectedLocation = _locations select _selectedIndex;
			_selectedLocation params ["_name", "_pos", "_code"];
			
			// Center map on selected location
			private _map = (findDisplay 2800) displayCtrl 2802;
			_map ctrlMapAnimAdd [0, 0.1, _pos];
			ctrlMapAnimCommit _map;
		};
	}];
	
	// Store locations for event handler
	missionNamespace setVariable ["EDRP_spawn_locations", _locations];
};

// Process spawn selection
EDRP_fnc_processSpawnSelection = {
	params ["_spawnCode"];
	
	EDRP_loading_status = "<t color='#00FF00'>Spawning player...</t>";
	EDRP_loading_progress = 65;
	
	// Get spawn location data
	private _spawnData = [_spawnCode] call EDRP_fnc_getSpawnData;
	_spawnData params ["_name", "_pos", "_code"];
	
	// Teleport player to spawn location
	player setPos _pos;
	
	// Set spawn-specific variables
	player setVariable ["EDRP_last_spawn", _code, true];
	player setVariable ["EDRP_spawn_time", time, true];
	
	// Handle faction-specific spawn setup
	switch (playerSide) do {
		case west: {
			[_code] call EDRP_fnc_setupPoliceSpawn;
		};
		case independent: {
			[_code] call EDRP_fnc_setupMedicalSpawn;
		};
		case civilian: {
			[_code] call EDRP_fnc_setupCivilianSpawn;
		};
	};
	
	// Close spawn selection dialog
	closeDialog 2800;
	
	// Clean up map markers
	call EDRP_fnc_cleanupSpawnMarkers;
	
	EDRP_loading_progress = 75;
	EDRP_loading_status = "<t color='#00FF00'>Spawn complete</t>";
	
	diag_log format ["EdenRP: Player spawned at %1", _name];
};

// Get spawn data by code
EDRP_fnc_getSpawnData = {
	params ["_code"];
	
	private _allLocations = call EDRP_fnc_getSpawnLocations;
	private _spawnData = [];
	
	{
		_x params ["_name", "_pos", "_locationCode"];
		if (_locationCode == _code) exitWith {
			_spawnData = _x;
		};
	} forEach _allLocations;
	
	_spawnData
};

// Setup police spawn
EDRP_fnc_setupPoliceSpawn = {
	params ["_spawnCode"];
	
	// Set department based on spawn location
	private _department = switch (_spawnCode) do {
		case "air_hq": {"air"};
		case "kavala_pd": {"patrol"};
		case "pyrgos_pd": {"patrol"};
		case "athira_pd": {"patrol"};
		case "sofia_pd": {"patrol"};
		default {"patrol"};
	};
	
	player setVariable ["EDRP_police_department", _department, true];
	
	// Load department-specific gear
	[_department] call EDRP_fnc_loadDepartmentGear;
};

// Setup medical spawn
EDRP_fnc_setupMedicalSpawn = {
	params ["_spawnCode"];
	
	// Set hospital assignment
	private _hospital = switch (_spawnCode) do {
		case "kavala_hospital": {"kavala"};
		case "pyrgos_hospital": {"pyrgos"};
		case "athira_hospital": {"athira"};
		case "sofia_hospital": {"sofia"};
		case "air_rescue": {"air_rescue"};
		default {"kavala"};
	};
	
	player setVariable ["EDRP_medical_hospital", _hospital, true];
	
	// Load hospital-specific gear
	[_hospital] call EDRP_fnc_loadHospitalGear;
};

// Setup civilian spawn
EDRP_fnc_setupCivilianSpawn = {
	params ["_spawnCode"];
	
	// Set spawn location for reference
	player setVariable ["EDRP_civilian_spawn", _spawnCode, true];
	
	// Handle special spawn types
	if (_spawnCode == "gang_hideout") then {
		// Gang member spawn setup
		call EDRP_fnc_setupGangSpawn;
	};
	
	if (_spawnCode find "house_" == 0) then {
		// House spawn setup
		private _houseId = parseNumber (_spawnCode select [6]);
		[_houseId] call EDRP_fnc_setupHouseSpawn;
	};
};

// Cleanup spawn markers
EDRP_fnc_cleanupSpawnMarkers = {
	private _markers = allMapMarkers select {_x find "spawn_" == 0};
	{
		deleteMarkerLocal _x;
	} forEach _markers;
};

// Emergency respawn function
EDRP_fnc_emergencyRespawn = {
	// Force respawn at default location if spawn system fails
	private _defaultPos = switch (playerSide) do {
		case west: {[3560.26, 13724.2, 0.00143909]}; // Kavala PD
		case independent: {[3529.98, 13650.7, 0.00143909]}; // Kavala Hospital
		case civilian: {[3560.26, 13724.2, 0.00143909]}; // Kavala
	};
	
	player setPos _defaultPos;
	
	["Emergency Respawn", "You have been moved to a safe location", "warning"] call EDRP_fnc_notification;
	
	diag_log "EdenRP: Emergency respawn executed";
};

diag_log "EdenRP: Spawn selection system loaded";
