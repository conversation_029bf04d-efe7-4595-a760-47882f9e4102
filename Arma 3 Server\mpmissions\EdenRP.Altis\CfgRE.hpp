class CfgRemoteExec {
	class Commands {
		mode = 1;
		jip = 0;
	};

	class Functions {
		mode = 1;
		jip = 0;

		// Core System Functions
		class EDRP_fnc_briefingSystem { allowedTargets = 2; };
		class EDRP_fnc_systemInit { allowedTargets = 2; };
		class EDRP_fnc_timedMarkerInit { allowedTargets = 2; };
		class EDRP_fnc_initializeCivilian { allowedTargets = 2; };
		class EDRP_fnc_initializePolice { allowedTargets = 2; };
		class EDRP_fnc_initializeMedical { allowedTargets = 2; };
		class EDRP_fnc_initializeSurvival { allowedTargets = 2; };
		class EDRP_fnc_introSequence { allowedTargets = 2; };
		class EDRP_fnc_setupPlayerActions { allowedTargets = 2; };
		class EDRP_fnc_setupEventHandlers { allowedTargets = 2; };
		class EDRP_fnc_loadingScreenManager { allowedTargets = 2; };
		class EDRP_fnc_loadingScreenIcon { allowedTargets = 2; };
		class EDRP_fnc_loadingScreenContent { allowedTargets = 2; };
		class EDRP_fnc_systemLogger { allowedTargets = 2; };
		class EDRP_fnc_monitorDisplays { allowedTargets = 2; };

		// Player Action Functions
		class EDRP_fnc_arrestPlayer { allowedTargets = 2; };
		class EDRP_fnc_playerBeatdown { allowedTargets = 2; };
		class EDRP_fnc_purchaseLicense { allowedTargets = 2; };
		class EDRP_fnc_buyLotteryTicket { allowedTargets = 2; };
		class EDRP_fnc_placeBet { allowedTargets = 2; };
		class EDRP_fnc_captureBlackMarket { allowedTargets = 2; };
		class EDRP_fnc_captureHideout { allowedTargets = 2; };
		class EDRP_fnc_fishingAction { allowedTargets = 2; };
		class EDRP_fnc_turtleHunting { allowedTargets = 2; };
		class EDRP_fnc_claimVehicle { allowedTargets = 2; };
		class EDRP_fnc_claimIllegalGoods { allowedTargets = 2; };
		class EDRP_fnc_claimGangVehicle { allowedTargets = 2; };
		class EDRP_fnc_closeMapAction { allowedTargets = 2; };
		class EDRP_fnc_deliveryComplete { allowedTargets = 2; };
		class EDRP_fnc_deployFishingNet { allowedTargets = 2; };
		class EDRP_fnc_escortPlayer { allowedTargets = 2; };
		class EDRP_fnc_flipVehicle { allowedTargets = 2; };
		class EDRP_fnc_gatherResources { allowedTargets = 2; };
		class EDRP_fnc_enhancedGathering { allowedTargets = 2; };
		class EDRP_fnc_getDeliveryMission { allowedTargets = 2; };
		class EDRP_fnc_processAnimal { allowedTargets = 2; };
		class EDRP_fnc_hackAntiAirSystem { allowedTargets = 2; };
		class EDRP_fnc_hackRadioTower { allowedTargets = 2; };
		class EDRP_fnc_hospitalHealing { allowedTargets = 2; };
		class EDRP_fnc_handleAnimation { allowedTargets = 2; };
		class EDRP_fnc_handleVehicleSpawn { allowedTargets = 2; };
		class EDRP_fnc_handleGangVehicles { allowedTargets = 2; };
		class EDRP_fnc_handlePoliceIsland { allowedTargets = 2; };
		class EDRP_fnc_impoundVehicle { allowedTargets = 2; };
		class EDRP_fnc_loadPlayerLoadout { allowedTargets = 2; };
		class EDRP_fnc_medicalInvoice { allowedTargets = 2; };
		class EDRP_fnc_packupSpikes { allowedTargets = 2; };
		class EDRP_fnc_pickupItem { allowedTargets = 2; };
		class EDRP_fnc_pickupMoney { allowedTargets = 2; };
		class EDRP_fnc_postBail { allowedTargets = 2; };
		class EDRP_fnc_processResources { allowedTargets = 2; };
		class EDRP_fnc_pulloutPlayer { allowedTargets = 2; };
		class EDRP_fnc_pulloutDeadPlayer { allowedTargets = 2; };
		class EDRP_fnc_pushVehicle { allowedTargets = 2; };
		class EDRP_fnc_putPlayerInVehicle { allowedTargets = 2; };
		class EDRP_fnc_refillMagazines { allowedTargets = 2; };
		class EDRP_fnc_repairVehicle { allowedTargets = 2; };
		class EDRP_fnc_restrainPlayer { allowedTargets = 2; };
		class EDRP_fnc_robberyAction { allowedTargets = 2; };
		class EDRP_fnc_robShops { allowedTargets = 2; };
		class EDRP_fnc_savePlayerLoadout { allowedTargets = 2; };
		class EDRP_fnc_searchPlayer { allowedTargets = 2; };
		class EDRP_fnc_searchShipWreck { allowedTargets = 2; };
		class EDRP_fnc_searchVehicle { allowedTargets = 2; };
		class EDRP_fnc_seizeItems { allowedTargets = 2; };
		class EDRP_fnc_sellLicense { allowedTargets = 2; };
		class EDRP_fnc_serviceHelicopter { allowedTargets = 2; };
		class EDRP_fnc_stripPlayerGear { allowedTargets = 2; };
		class EDRP_fnc_stopEscorting { allowedTargets = 2; };
		class EDRP_fnc_storeVehicle { allowedTargets = 2; };
		class EDRP_fnc_suicideBomb { allowedTargets = 2; };
		class EDRP_fnc_surrenderAction { allowedTargets = 2; };
		class EDRP_fnc_issueTicket { allowedTargets = 2; };
		class EDRP_fnc_unrestrainPlayer { allowedTargets = 2; };
		class EDRP_fnc_repairObject { allowedTargets = 2; };
		class EDRP_fnc_removeKidney { allowedTargets = 2; };
		class EDRP_fnc_conquestAction { allowedTargets = 2; };
		class EDRP_fnc_buyDopamineCrate { allowedTargets = 2; };
		class EDRP_fnc_dopamineCrateAction { allowedTargets = 2; };
		class EDRP_fnc_refillDopeMag { allowedTargets = 2; };

		// Admin System Functions
		class EDRP_fnc_adminMenu { allowedTargets = 2; };
		class EDRP_fnc_adminSpectate { allowedTargets = 2; };
		class EDRP_fnc_adminTeleport { allowedTargets = 2; };
		class EDRP_fnc_adminGodMode { allowedTargets = 2; };
		class EDRP_fnc_adminInvisible { allowedTargets = 2; };
		class EDRP_fnc_adminFreeze { allowedTargets = 2; };
		class EDRP_fnc_adminKick { allowedTargets = 2; };
		class EDRP_fnc_adminBan { allowedTargets = 2; };
		class EDRP_fnc_adminUnban { allowedTargets = 2; };
		class EDRP_fnc_adminMute { allowedTargets = 2; };
		class EDRP_fnc_adminUnmute { allowedTargets = 2; };
		class EDRP_fnc_adminJail { allowedTargets = 2; };
		class EDRP_fnc_adminUnjail { allowedTargets = 2; };
		class EDRP_fnc_adminHeal { allowedTargets = 2; };
		class EDRP_fnc_adminRevive { allowedTargets = 2; };
		class EDRP_fnc_adminGiveMoney { allowedTargets = 2; };
		class EDRP_fnc_adminTakeMoney { allowedTargets = 2; };
		class EDRP_fnc_adminGiveItem { allowedTargets = 2; };
		class EDRP_fnc_adminTakeItem { allowedTargets = 2; };
		class EDRP_fnc_adminSpawnVehicle { allowedTargets = 2; };
		class EDRP_fnc_adminDeleteVehicle { allowedTargets = 2; };
		class EDRP_fnc_adminRepairVehicle { allowedTargets = 2; };
		class EDRP_fnc_adminRefuelVehicle { allowedTargets = 2; };
		class EDRP_fnc_adminFlipVehicle { allowedTargets = 2; };
		class EDRP_fnc_adminTeleportTo { allowedTargets = 2; };
		class EDRP_fnc_adminTeleportHere { allowedTargets = 2; };
		class EDRP_fnc_adminTeleportToCoords { allowedTargets = 2; };
		class EDRP_fnc_adminSetTime { allowedTargets = 2; };
		class EDRP_fnc_adminSetWeather { allowedTargets = 2; };

		// Server-side Functions
		class EDRP_fnc_updatePlayerData { allowedTargets = 2; };
		class EDRP_fnc_savePlayerData { allowedTargets = 2; };
		class EDRP_fnc_loadPlayerData { allowedTargets = 2; };
		class EDRP_fnc_updateVehicleData { allowedTargets = 2; };
		class EDRP_fnc_saveVehicleData { allowedTargets = 2; };
		class EDRP_fnc_loadVehicleData { allowedTargets = 2; };
		class EDRP_fnc_updateHouseData { allowedTargets = 2; };
		class EDRP_fnc_saveHouseData { allowedTargets = 2; };
		class EDRP_fnc_loadHouseData { allowedTargets = 2; };
		class EDRP_fnc_updateGangData { allowedTargets = 2; };
		class EDRP_fnc_saveGangData { allowedTargets = 2; };
		class EDRP_fnc_loadGangData { allowedTargets = 2; };
		class EDRP_fnc_updateMarketData { allowedTargets = 2; };
		class EDRP_fnc_saveMarketData { allowedTargets = 2; };
		class EDRP_fnc_loadMarketData { allowedTargets = 2; };

		// Communication Functions
		class EDRP_fnc_sendMessage { allowedTargets = 2; };
		class EDRP_fnc_receiveMessage { allowedTargets = 2; };
		class EDRP_fnc_sendDispatch { allowedTargets = 2; };
		class EDRP_fnc_receiveDispatch { allowedTargets = 2; };
		class EDRP_fnc_sendRadio { allowedTargets = 2; };
		class EDRP_fnc_receiveRadio { allowedTargets = 2; };
		class EDRP_fnc_sendPhone { allowedTargets = 2; };
		class EDRP_fnc_receivePhone { allowedTargets = 2; };

		// Economy Functions
		class EDRP_fnc_updateEconomy { allowedTargets = 2; };
		class EDRP_fnc_processTransaction { allowedTargets = 2; };
		class EDRP_fnc_updateBankAccount { allowedTargets = 2; };
		class EDRP_fnc_processPayment { allowedTargets = 2; };
		class EDRP_fnc_calculateTax { allowedTargets = 2; };
		class EDRP_fnc_processLoan { allowedTargets = 2; };
		class EDRP_fnc_updateCreditScore { allowedTargets = 2; };

		// Security Functions
		class EDRP_fnc_validateClient { allowedTargets = 2; };
		class EDRP_fnc_checkPermissions { allowedTargets = 2; };
		class EDRP_fnc_logAction { allowedTargets = 2; };
		class EDRP_fnc_reportSuspiciousActivity { allowedTargets = 2; };
		class EDRP_fnc_banPlayer { allowedTargets = 2; };
		class EDRP_fnc_kickPlayer { allowedTargets = 2; };
		class EDRP_fnc_mutePlayer { allowedTargets = 2; };
		class EDRP_fnc_freezePlayer { allowedTargets = 2; };

		// Utility Functions
		class EDRP_fnc_hint { allowedTargets = 2; };
		class EDRP_fnc_notification { allowedTargets = 2; };
		class EDRP_fnc_progressBar { allowedTargets = 2; };
		class EDRP_fnc_playSound { allowedTargets = 2; };
		class EDRP_fnc_createMarker { allowedTargets = 2; };
		class EDRP_fnc_deleteMarker { allowedTargets = 2; };
		class EDRP_fnc_updateMarker { allowedTargets = 2; };
		class EDRP_fnc_createObject { allowedTargets = 2; };
		class EDRP_fnc_deleteObject { allowedTargets = 2; };
		class EDRP_fnc_moveObject { allowedTargets = 2; };

		// Event Functions
		class EDRP_fnc_eventStart { allowedTargets = 2; };
		class EDRP_fnc_eventEnd { allowedTargets = 2; };
		class EDRP_fnc_eventUpdate { allowedTargets = 2; };
		class EDRP_fnc_eventNotification { allowedTargets = 2; };
		class EDRP_fnc_eventReward { allowedTargets = 2; };
		class EDRP_fnc_eventPenalty { allowedTargets = 2; };

		// Gang Functions
		class EDRP_fnc_createGang { allowedTargets = 2; };
		class EDRP_fnc_disbandGang { allowedTargets = 2; };
		class EDRP_fnc_joinGang { allowedTargets = 2; };
		class EDRP_fnc_leaveGang { allowedTargets = 2; };
		class EDRP_fnc_promoteGangMember { allowedTargets = 2; };
		class EDRP_fnc_demoteGangMember { allowedTargets = 2; };
		class EDRP_fnc_kickGangMember { allowedTargets = 2; };
		class EDRP_fnc_inviteToGang { allowedTargets = 2; };
		class EDRP_fnc_acceptGangInvite { allowedTargets = 2; };
		class EDRP_fnc_declineGangInvite { allowedTargets = 2; };

		// Housing Functions
		class EDRP_fnc_buyHouse { allowedTargets = 2; };
		class EDRP_fnc_sellHouse { allowedTargets = 2; };
		class EDRP_fnc_rentHouse { allowedTargets = 2; };
		class EDRP_fnc_evictTenant { allowedTargets = 2; };
		class EDRP_fnc_lockHouse { allowedTargets = 2; };
		class EDRP_fnc_unlockHouse { allowedTargets = 2; };
		class EDRP_fnc_giveHouseKey { allowedTargets = 2; };
		class EDRP_fnc_revokeHouseKey { allowedTargets = 2; };
		class EDRP_fnc_upgradeHouse { allowedTargets = 2; };
		class EDRP_fnc_downgradeHouse { allowedTargets = 2; };

		// Vehicle Functions
		class EDRP_fnc_buyVehicle { allowedTargets = 2; };
		class EDRP_fnc_sellVehicle { allowedTargets = 2; };
		class EDRP_fnc_rentVehicle { allowedTargets = 2; };
		class EDRP_fnc_returnRental { allowedTargets = 2; };
		class EDRP_fnc_lockVehicle { allowedTargets = 2; };
		class EDRP_fnc_unlockVehicle { allowedTargets = 2; };
		class EDRP_fnc_giveVehicleKey { allowedTargets = 2; };
		class EDRP_fnc_revokeVehicleKey { allowedTargets = 2; };
		class EDRP_fnc_upgradeVehicle { allowedTargets = 2; };
		class EDRP_fnc_downgradeVehicle { allowedTargets = 2; };

		// Medical Functions
		class EDRP_fnc_revivePlayer { allowedTargets = 2; };
		class EDRP_fnc_healPlayer { allowedTargets = 2; };
		class EDRP_fnc_stabilizePlayer { allowedTargets = 2; };
		class EDRP_fnc_treatWounds { allowedTargets = 2; };
		class EDRP_fnc_administerMedicine { allowedTargets = 2; };
		class EDRP_fnc_performSurgery { allowedTargets = 2; };
		class EDRP_fnc_checkVitals { allowedTargets = 2; };
		class EDRP_fnc_transportPatient { allowedTargets = 2; };

		// Police Functions
		class EDRP_fnc_arrestSuspect { allowedTargets = 2; };
		class EDRP_fnc_releaseSuspect { allowedTargets = 2; };
		class EDRP_fnc_issueCitation { allowedTargets = 2; };
		class EDRP_fnc_searchSuspect { allowedTargets = 2; };
		class EDRP_fnc_seizeEvidence { allowedTargets = 2; };
		class EDRP_fnc_impoundVehicle { allowedTargets = 2; };
		class EDRP_fnc_releaseVehicle { allowedTargets = 2; };
		class EDRP_fnc_setWanted { allowedTargets = 2; };
		class EDRP_fnc_removeWanted { allowedTargets = 2; };
		class EDRP_fnc_updateBounty { allowedTargets = 2; };
	};
};
