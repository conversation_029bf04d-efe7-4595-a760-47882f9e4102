/*
	EdenRP Altis Life - Menu Base Class
	Author: EdenRP Development Team
	Description: Base class for all EdenRP menu dialogs with consistent styling
	Version: 1.0.0
*/

class EDRP_MenuBase {
	idd = -1;
	location = "center";
	movingEnable = 1;
	enableSimulation = 1;
	
	class controlsBackgroundBase {
		class BaseBackground: EDRP_RscText {
			idc = -1;
			x = 0.2;
			y = 0.1;
			w = 0.6;
			h = 0.8;
			colorBackground[] = EDRP_COLOR_BACKGROUND;
		};
		
		class BaseTitle: EDRP_RscText {
			idc = -1;
			text = "EdenRP Menu";
			x = 0.2;
			y = 0.1;
			w = 0.6;
			h = 0.05;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			style = 2; // Center aligned
			sizeEx = 0.04;
			font = "RobotoCondensedBold";
		};
		
		class BaseHeader: EDRP_RscText {
			idc = -1;
			x = 0.2;
			y = 0.15;
			w = 0.6;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			style = 2;
		};
		
		// Tab backgrounds for tabbed interfaces
		class BaseTab1Background: EDRP_RscText {
			idc = -1;
			x = 0.21;
			y = 0.19;
			w = 0.08;
			h = 0.06;
			colorBackground[] = {0.1, 0.1, 0.1, 0.8};
		};
		
		class BaseTab2Background: BaseTab1Background {
			x = 0.30;
		};
		
		class BaseTab3Background: BaseTab1Background {
			x = 0.39;
		};
		
		class BaseTab4Background: BaseTab1Background {
			x = 0.48;
		};
		
		class BaseTab5Background: BaseTab1Background {
			x = 0.57;
		};
		
		class BaseTab6Background: BaseTab1Background {
			x = 0.66;
		};
		
		// Content area background
		class BaseContentBackground: EDRP_RscText {
			idc = -1;
			x = 0.21;
			y = 0.26;
			w = 0.58;
			h = 0.58;
			colorBackground[] = {0.05, 0.05, 0.05, 0.9};
		};
		
		// Side panel background
		class BaseSidePanelBackground: EDRP_RscText {
			idc = -1;
			x = 0.21;
			y = 0.26;
			w = 0.18;
			h = 0.58;
			colorBackground[] = {0.08, 0.08, 0.08, 0.9};
		};
		
		// Main content background (when using side panel)
		class BaseMainContentBackground: EDRP_RscText {
			idc = -1;
			x = 0.40;
			y = 0.26;
			w = 0.39;
			h = 0.58;
			colorBackground[] = {0.05, 0.05, 0.05, 0.9};
		};
	};
	
	class controlsBase {
		// Tab buttons
		class BaseTab1: EDRP_RscButton {
			idc = 2001;
			text = "";
			x = 0.21;
			y = 0.19;
			w = 0.08;
			h = 0.06;
			colorBackground[] = {0.2, 0.2, 0.2, 0.8};
			colorBackgroundActive[] = EDRP_COLOR_PRIMARY;
			colorFocused[] = EDRP_COLOR_SECONDARY;
			sizeEx = 0.03;
		};
		
		class BaseTab2: BaseTab1 {
			idc = 2002;
			x = 0.30;
		};
		
		class BaseTab3: BaseTab1 {
			idc = 2003;
			x = 0.39;
		};
		
		class BaseTab4: BaseTab1 {
			idc = 2004;
			x = 0.48;
		};
		
		class BaseTab5: BaseTab1 {
			idc = 2005;
			x = 0.57;
		};
		
		class BaseTab6: BaseTab1 {
			idc = 2006;
			x = 0.66;
		};
		
		// Standard buttons
		class BaseButtonClose: EDRP_RscButtonMenu {
			idc = 2099;
			text = "Close";
			x = 0.71;
			y = 0.86;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "closeDialog 0;";
		};
		
		class BaseButtonOK: EDRP_RscButtonMenu {
			idc = 2098;
			text = "OK";
			x = 0.62;
			y = 0.86;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
		};
		
		class BaseButtonCancel: EDRP_RscButtonMenu {
			idc = 2097;
			text = "Cancel";
			x = 0.53;
			y = 0.86;
			w = 0.08;
			h = 0.04;
			colorBackground[] = {0.5, 0.5, 0.5, 0.8};
			onButtonClick = "closeDialog 0;";
		};
		
		// Navigation buttons
		class BaseButtonPrev: EDRP_RscButtonMenu {
			idc = 2096;
			text = "< Previous";
			x = 0.21;
			y = 0.86;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SECONDARY;
		};
		
		class BaseButtonNext: EDRP_RscButtonMenu {
			idc = 2095;
			text = "Next >";
			x = 0.32;
			y = 0.86;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SECONDARY;
		};
		
		// Common list boxes
		class BaseListBox: EDRP_RscListBox {
			idc = 2010;
			x = 0.22;
			y = 0.28;
			w = 0.56;
			h = 0.5;
			sizeEx = 0.035;
		};
		
		class BaseSideListBox: EDRP_RscListBox {
			idc = 2011;
			x = 0.22;
			y = 0.28;
			w = 0.16;
			h = 0.5;
			sizeEx = 0.03;
		};
		
		class BaseMainListBox: EDRP_RscListBox {
			idc = 2012;
			x = 0.41;
			y = 0.28;
			w = 0.37;
			h = 0.5;
			sizeEx = 0.035;
		};
		
		// Common edit boxes
		class BaseEdit: EDRP_RscEdit {
			idc = 2020;
			x = 0.22;
			y = 0.8;
			w = 0.2;
			h = 0.04;
		};
		
		class BaseEditLarge: EDRP_RscEdit {
			idc = 2021;
			x = 0.22;
			y = 0.8;
			w = 0.4;
			h = 0.04;
		};
		
		// Common combo boxes
		class BaseCombo: EDRP_RscCombo {
			idc = 2030;
			x = 0.22;
			y = 0.8;
			w = 0.2;
			h = 0.04;
		};
		
		// Common text displays
		class BaseInfoText: EDRP_RscStructuredText {
			idc = 2040;
			x = 0.22;
			y = 0.28;
			w = 0.56;
			h = 0.1;
			text = "Information will be displayed here";
		};
		
		class BaseStatusText: EDRP_RscText {
			idc = 2041;
			x = 0.22;
			y = 0.82;
			w = 0.4;
			h = 0.03;
			text = "Status: Ready";
			colorBackground[] = {0, 0, 0, 0};
		};
		
		// Progress bar
		class BaseProgressBar: EDRP_RscProgress {
			idc = 2050;
			x = 0.22;
			y = 0.82;
			w = 0.4;
			h = 0.02;
			colorBar[] = EDRP_COLOR_SUCCESS;
		};
		
		// Map control
		class BaseMapControl: EDRP_RscMapControl {
			idc = 2060;
			x = 0.22;
			y = 0.28;
			w = 0.56;
			h = 0.5;
		};
		
		// Picture controls
		class BasePicture: EDRP_RscPicture {
			idc = 2070;
			x = 0.22;
			y = 0.28;
			w = 0.1;
			h = 0.1;
		};
		
		class BaseIconSmall: EDRP_RscPicture {
			idc = 2071;
			x = 0.22;
			y = 0.28;
			w = 0.05;
			h = 0.05;
		};
		
		class BaseIconMedium: EDRP_RscPicture {
			idc = 2072;
			x = 0.22;
			y = 0.28;
			w = 0.08;
			h = 0.08;
		};
		
		class BaseIconLarge: EDRP_RscPicture {
			idc = 2073;
			x = 0.22;
			y = 0.28;
			w = 0.12;
			h = 0.12;
		};
	};
};

// Compact menu base for smaller dialogs
class EDRP_MenuBaseCompact {
	idd = -1;
	location = "center";
	movingEnable = 1;
	enableSimulation = 1;
	
	class controlsBackgroundBase {
		class BaseBackground: EDRP_RscText {
			idc = -1;
			x = 0.3;
			y = 0.2;
			w = 0.4;
			h = 0.6;
			colorBackground[] = EDRP_COLOR_BACKGROUND;
		};
		
		class BaseTitle: EDRP_RscText {
			idc = -1;
			text = "EdenRP";
			x = 0.3;
			y = 0.2;
			w = 0.4;
			h = 0.05;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			style = 2;
			sizeEx = 0.04;
		};
		
		class BaseContentBackground: EDRP_RscText {
			idc = -1;
			x = 0.31;
			y = 0.26;
			w = 0.38;
			h = 0.48;
			colorBackground[] = {0.05, 0.05, 0.05, 0.9};
		};
	};
	
	class controlsBase {
		class BaseButtonClose: EDRP_RscButtonMenu {
			idc = 2099;
			text = "Close";
			x = 0.61;
			y = 0.76;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "closeDialog 0;";
		};
		
		class BaseButtonOK: EDRP_RscButtonMenu {
			idc = 2098;
			text = "OK";
			x = 0.52;
			y = 0.76;
			w = 0.08;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SUCCESS;
		};
		
		class BaseListBox: EDRP_RscListBox {
			idc = 2010;
			x = 0.32;
			y = 0.28;
			w = 0.36;
			h = 0.4;
			sizeEx = 0.035;
		};
		
		class BaseEdit: EDRP_RscEdit {
			idc = 2020;
			x = 0.32;
			y = 0.7;
			w = 0.25;
			h = 0.04;
		};
		
		class BaseInfoText: EDRP_RscStructuredText {
			idc = 2040;
			x = 0.32;
			y = 0.28;
			w = 0.36;
			h = 0.4;
		};
	};
};
