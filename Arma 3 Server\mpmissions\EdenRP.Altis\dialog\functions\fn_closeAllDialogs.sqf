/*
	EdenRP Altis Life - Close All Dialogs Function
	Author: EdenRP Development Team
	Description: Closes all open dialogs and resets dialog state
	Version: 1.0.0
*/

// Close all possible dialogs
private _dialogsToClose = [
	3000, 3100, 3200, 3500, 3600, 3700,  // Main menu system
	4000, 4100, 4200, 4300,              // Shop system
	5000, 5100, 5200, 5300,              // Phone system
	6000, 6100, 6200                     // Admin system
];

{
	private _display = findDisplay _x;
	if (!isNull _display) then {
		_display closeDisplay 1;
	};
} forEach _dialogsToClose;

// Reset dialog state variables
EDRP_current_dialog = "";
EDRP_current_dialog_idd = -1;
EDRP_dialog_params = [];
EDRP_dialog_type = "";

// Reset menu-specific variables
EDRP_mainmenu_current_tab = "main";
EDRP_inventory_selected_item = "";
EDRP_shop_current_category = "";
EDRP_phone_current_tab = "contacts";
EDRP_admin_selected_player = "";

// Play close sound
playSound "EDRP_ui_close";

true
