/*
	EdenRP Altis Life - Arrest System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Handles player arrests, wanted system, and jail processing
	Version: 1.0.0
*/

// Arrest player
EDRP_fnc_arrestPlayer = {
	params [
		["_target", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
		["_officer", player, [obj<PERSON><PERSON>]]
	];
	
	if (isNull _target || _target == _officer) exitWith {
		["Invalid arrest target"] call EDRP_fnc_hint;
		false
	};
	
	// Check if officer is on duty
	if (!EDRP_police_active) exitWith {
		["You must be on duty to make arrests"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is already restrained
	if !(_target getVariable ["isRestrained", false]) exitWith {
		["Target must be restrained first"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is civilian
	if (side _target != civilian) exitWith {
		["You can only arrest civilians"] call EDRP_fnc_hint;
		false
	};
	
	// Get target's bounty and crimes
	private _targetUID = getPlayerUID _target;
	private _targetBounty = _target getVariable ["bounty", 0];
	private _targetCrimes = _target getVariable ["crimes", []];
	
	if (_targetBounty <= 0 && count _targetCrimes == 0) exitWith {
		["Target has no crimes or bounty"] call EDRP_fnc_hint;
		false
	};
	
	// Calculate jail time based on bounty
	private _jailTime = [_targetBounty] call EDRP_fnc_calculateJailTime;
	
	// Show arrest confirmation dialog
	private _confirmText = format [
		"Arrest %1?\n\nBounty: $%2\nJail Time: %3 minutes\nCrimes: %4",
		name _target,
		[_targetBounty] call EDRP_fnc_numberText,
		_jailTime,
		count _targetCrimes
	];
	
	if ([_confirmText, "Confirm Arrest", true, true] call EDRP_fnc_messageBox) then {
		// Process arrest
		[_target, _officer, _jailTime, _targetBounty] call EDRP_fnc_processArrest;
		true
	} else {
		false
	};
};

// Process arrest
EDRP_fnc_processArrest = {
	params [
		["_target", objNull, [objNull]],
		["_officer", player, [objNull]],
		["_jailTime", 5, [0]],
		["_bounty", 0, [0]]
	];
	
	// Remove restraints
	_target setVariable ["isRestrained", false, true];
	
	// Clear wanted status
	_target setVariable ["bounty", 0, true];
	_target setVariable ["crimes", [], true];
	
	// Send to jail
	[_target, _jailTime] call EDRP_fnc_sendToJail;
	
	// Pay officer bounty reward
	private _reward = _bounty * 0.1; // 10% of bounty
	EDRP_player_cash = EDRP_player_cash + _reward;
	
	// Update statistics
	EDRP_police_stats set ["arrests_made", (EDRP_police_stats get "arrests_made") + 1];
	
	// Broadcast arrest
	private _message = format ["%1 has been arrested by %2 for $%3 bounty", name _target, name _officer, [_bounty] call EDRP_fnc_numberText];
	[_message] remoteExec ["EDRP_fnc_globalMessage", -2];
	
	// Show success message to officer
	[format ["Arrested %1 - Reward: $%2", name _target, [_reward] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
	
	// Log arrest
	[format ["ARREST: %1 arrested %2 for $%3 bounty, %4 minutes jail time", name _officer, name _target, _bounty, _jailTime]] call EDRP_fnc_logInfo;
};

// Calculate jail time based on bounty
EDRP_fnc_calculateJailTime = {
	params [["_bounty", 0, [0]]];
	
	private _baseTime = 5; // 5 minutes minimum
	private _timePerThousand = 0.5; // 30 seconds per $1000 bounty
	
	private _calculatedTime = _baseTime + ((_bounty / 1000) * _timePerThousand);
	private _maxTime = 30; // 30 minutes maximum
	
	(_calculatedTime min _maxTime) max _baseTime
};

// Send player to jail
EDRP_fnc_sendToJail = {
	params [
		["_player", objNull, [objNull]],
		["_time", 5, [0]]
	];
	
	if (isNull _player) exitWith {};
	
	// Set jail variables
	_player setVariable ["isJailed", true, true];
	_player setVariable ["jailTime", _time, true];
	_player setVariable ["jailStartTime", time, true];
	
	// Teleport to jail
	private _jailPos = [3688.11, 13742.5, 0.00143909]; // Kavala jail
	_player setPos _jailPos;
	
	// Strip weapons and items
	removeAllWeapons _player;
	removeAllItems _player;
	removeAllAssignedItems _player;
	
	// Add prison uniform
	removeUniform _player;
	_player forceAddUniform "U_Competitor";
	
	// Add basic items
	_player addItem "ItemMap";
	_player assignItem "ItemMap";
	_player addItem "ItemCompass";
	_player assignItem "ItemCompass";
	_player addItem "ItemWatch";
	_player assignItem "ItemWatch";
	
	// Start jail timer
	[_player, _time] spawn EDRP_fnc_jailTimer;
	
	// Show jail message
	if (_player == player) then {
		[format ["You have been jailed for %1 minutes", _time], "warning"] call EDRP_fnc_hint;
	};
};

// Jail timer
EDRP_fnc_jailTimer = {
	params [
		["_player", objNull, [objNull]],
		["_time", 5, [0]]
	];
	
	if (isNull _player) exitWith {};
	
	private _endTime = time + (_time * 60);
	
	while {time < _endTime && _player getVariable ["isJailed", false]} do {
		private _remaining = ceil((_endTime - time) / 60);
		
		if (_player == player) then {
			// Show remaining time
			private _timeText = format ["<t size='2' color='#FF0000'>JAILED</t><br/>Time Remaining: %1 minutes", _remaining];
			[_timeText, 0, 0, 1, 0, 0, 1] spawn BIS_fnc_dynamicText;
		};
		
		sleep 60; // Update every minute
	};
	
	// Release from jail
	[_player] call EDRP_fnc_releaseFromJail;
};

// Release player from jail
EDRP_fnc_releaseFromJail = {
	params [["_player", objNull, [objNull]]];
	
	if (isNull _player) exitWith {};
	
	// Clear jail variables
	_player setVariable ["isJailed", false, true];
	_player setVariable ["jailTime", nil, true];
	_player setVariable ["jailStartTime", nil, true];
	
	// Teleport to jail exit
	private _exitPos = [3681.85, 13769.1, 0.00143909];
	_player setPos _exitPos;
	
	// Restore civilian gear
	if (_player == player) then {
		[] call EDRP_fnc_loadCivilianGear;
		["You have been released from jail", "success"] call EDRP_fnc_hint;
	};
};

// Add crime to wanted list
EDRP_fnc_addCrime = {
	params [
		["_player", objNull, [objNull]],
		["_crimeType", 1, [0]],
		["_customBounty", -1, [0]]
	];
	
	if (isNull _player) exitWith {};
	
	// Get crime data
	private _crimeData = EDRP_crime_types select (_crimeType - 1);
	if (isNil "_crimeData") exitWith {
		["Invalid crime type"] call EDRP_fnc_logError;
	};
	
	_crimeData params ["_id", "_name", "_bounty"];
	
	// Use custom bounty if provided
	if (_customBounty > 0) then {
		_bounty = _customBounty;
	};
	
	// Get current crimes and bounty
	private _currentCrimes = _player getVariable ["crimes", []];
	private _currentBounty = _player getVariable ["bounty", 0];
	
	// Check if crime already exists
	private _existingCrime = -1;
	{
		if ((_x select 0) == _id) exitWith {
			_existingCrime = _forEachIndex;
		};
	} forEach _currentCrimes;
	
	if (_existingCrime >= 0) then {
		// Increment existing crime
		private _crime = _currentCrimes select _existingCrime;
		_crime set [2, (_crime select 2) + 1];
	} else {
		// Add new crime
		_currentCrimes pushBack [_id, _name, 1];
	};
	
	// Update bounty
	_currentBounty = _currentBounty + _bounty;
	
	// Set variables
	_player setVariable ["crimes", _currentCrimes, true];
	_player setVariable ["bounty", _currentBounty, true];
	
	// Broadcast to police
	private _message = format ["%1 is wanted for %2 - Bounty: $%3", name _player, _name, [_currentBounty] call EDRP_fnc_numberText];
	[_message] remoteExec ["EDRP_fnc_policeMessage", west];
	
	// Log crime
	[format ["CRIME: %1 committed %2 - New bounty: $%3", name _player, _name, _currentBounty]] call EDRP_fnc_logInfo;
};

// Search player
EDRP_fnc_searchPlayer = {
	params [
		["_target", objNull, [objNull]],
		["_officer", player, [objNull]]
	];
	
	if (isNull _target || _target == _officer) exitWith {
		["Invalid search target"] call EDRP_fnc_hint;
		false
	};
	
	// Check if officer is on duty
	if (!EDRP_police_active) exitWith {
		["You must be on duty to search players"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is restrained or consents
	if !(_target getVariable ["isRestrained", false]) then {
		// Ask for consent (simplified - in practice would use dialog)
		if (random 100 > 30) exitWith { // 70% refuse consent
			["Target refuses to consent to search"] call EDRP_fnc_hint;
			false
		};
	};
	
	// Perform search
	private _illegalItems = [] call EDRP_fnc_getIllegalItems;
	private _targetInventory = [_target] call EDRP_fnc_getPlayerInventory;
	private _foundItems = [];
	
	{
		if (_x in _illegalItems) then {
			_foundItems pushBack _x;
		};
	} forEach _targetInventory;
	
	// Update statistics
	EDRP_police_stats set ["searches_conducted", (EDRP_police_stats get "searches_conducted") + 1];
	
	if (count _foundItems > 0) then {
		// Found illegal items
		private _itemList = _foundItems joinString ", ";
		[format ["Search found illegal items: %1", _itemList], "warning"] call EDRP_fnc_hint;
		
		// Add crime for possession
		[_target, 5] call EDRP_fnc_addCrime; // Possession of illegal items
		
		// Confiscate items
		{
			[_target, _x, -1] call EDRP_fnc_removePlayerItem; // Remove all of this item
		} forEach _foundItems;
		
		true
	} else {
		// No illegal items found
		["Search completed - no illegal items found"] call EDRP_fnc_hint;
		false
	};
};

// Get list of illegal items
EDRP_fnc_getIllegalItems = {
	[
		"marijuana", "cocaine", "heroin", "meth",
		"lockpick", "boltcutter", "explosives",
		"goldbar", "diamondcut" // Processed items without license
	]
};

// Restrain/unrestrain player
EDRP_fnc_restrainPlayer = {
	params [
		["_target", objNull, [objNull]],
		["_officer", player, [objNull]]
	];
	
	if (isNull _target || _target == _officer) exitWith {
		["Invalid restrain target"] call EDRP_fnc_hint;
		false
	};
	
	// Check if officer is on duty
	if (!EDRP_police_active) exitWith {
		["You must be on duty to restrain players"] call EDRP_fnc_hint;
		false
	};
	
	private _isRestrained = _target getVariable ["isRestrained", false];
	
	if (_isRestrained) then {
		// Unrestrain
		_target setVariable ["isRestrained", false, true];
		[format ["Unrestrained %1", name _target]] call EDRP_fnc_hint;
		
		if (_target == player) then {
			["You have been unrestrained"] call EDRP_fnc_hint;
		};
	} else {
		// Restrain
		_target setVariable ["isRestrained", true, true];
		[format ["Restrained %1", name _target]] call EDRP_fnc_hint;
		
		if (_target == player) then {
			["You have been restrained"] call EDRP_fnc_hint;
		};
	};
	
	true
};

// Initialize arrest system
if (hasInterface) then {
	// Add restrain action for police
	if (EDRP_player_faction == "police") then {
		player addAction [
			"<t color='#FF8000'>Restrain/Unrestrain</t>",
			{
				private _target = cursorTarget;
				[_target, player] call EDRP_fnc_restrainPlayer;
			},
			[],
			7,
			true,
			true,
			"",
			"EDRP_police_active && cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 3"
		];
	};
};
