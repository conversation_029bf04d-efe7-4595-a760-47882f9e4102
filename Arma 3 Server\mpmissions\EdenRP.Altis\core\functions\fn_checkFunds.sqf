#include "..\..\macro.h"
//  File: fn_checkFunds.sqf
//	Author: Fusah
//	Modifications: TheCmdrRex
//	Description: Checks if player has funds for transfer (anti-cheat)

params [
	["_mode",-1,[0]],
	["_value",-1,[0]],
	["_eventPay",false,[false]]
];

if((eden_cash + (eden_random_cash_val - 5000)) > eden_cache_cash || (eden_atmcash + (eden_random_cash_val - 5000)) > eden_cache_atmcash) exitWith {
	[["event","Hacked Cash"],["player",name player],["player_id",getPlayerUID player],["hacked_cash",eden_cash - (eden_cache_cash - eden_random_cash_val)],["hacked_bank",eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)],["position",getPosATL player]] call EDEN_fnc_logIt;
	[profileName,format["Hacked Cash Detected! (Cash Hacked In = %1) (Bank Hacked In = %2)",eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]] remoteExec ["EDEN_fnc_notifyAdmins",-2];
	[1,player,[eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]] remoteExec ["EDENS_fnc_handleDisc",2];
	["HackedMoney",false,false] call compile PreProcessFileLineNumbers "\a3\functions_f\Misc\fn_endMission.sqf";
};

private _ret = false;

if (_mode < 0 || _value < 0) exitWith {};

private _tax = 0;
if (_mode == 0) then {
	_tax = [_value] call EDEN_fnc_taxRate;
	_value = _value + _tax;
};

switch (_mode) do {
	case 0: {
		//Bank
		if (_eventPay && (__GETC__(life_adminlevel) >= 1)) exitWith {
			_ret = true;
		};
		if (eden_atmcash >= _value) exitWith {
			_ret = true;
			eden_atmcash = eden_atmcash - _value;
			eden_cache_atmcash = eden_cache_atmcash - _value;
			[1] call EDEN_fnc_ClupdatePartial;
		};
	};
	case 1: {
		//Cash -- Not used
		if (eden_cash >= _value) exitWith {
			_ret = true;
			eden_cash = eden_cash - _value;
			eden_cache_cash = eden_cache_cash - _value;
			[0] call EDEN_fnc_ClupdatePartial;
		};
	};
	case 2: {
		//Warpoints
		eden_warpts_count = -999;
		[0,0,player] remoteExec ["EDENS_fnc_warGetSetPts",2];
		waitUntil {!(eden_warpts_count isEqualTo -999)};
		uiSleep 0.5;
		if (_eventPay && (__GETC__(life_adminlevel) >= 1)) exitWith {
			_ret = true;
		};
		if (eden_warpts_count >= _value) exitWith {
			_ret = true;
			eden_warpts_count = eden_warpts_count - _value;
			[13] call EDEN_fnc_ClupdatePartial;
		};
	};
};

if !(isNull (finddisplay 2700)) then {
	[objNull, objNull, -1, "", eden_bankMode] call EDEN_fnc_atmMenu;
	ctrlEnable[2705,true];
};

if(__GETC__(life_adminlevel) >= 1) then {_ret = true;};

if (remoteExecutedOwner isEqualTo 0) exitWith {};
["eden_status", _ret] remoteExec ["EDEN_fnc_netSetVar", remoteExecutedOwner,false];
