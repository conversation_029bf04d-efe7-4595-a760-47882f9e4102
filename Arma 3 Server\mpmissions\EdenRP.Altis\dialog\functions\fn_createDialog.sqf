/*
	EdenRP Altis Life - Create Dialog Function
	Author: EdenRP Development Team
	Description: Universal dialog creation function with animations and validation
	Version: 1.0.0
	
	Parameters:
		0: STRING - Dialog name/type
		1: ARRAY - Optional parameters for dialog initialization
		
	Returns:
		BOOL - True if dialog was created successfully
		
	Example:
		["mainMenu"] call EDRP_fnc_createDialog;
		["shop", ["general"]] call EDRP_fnc_createDialog;
*/

params [
	["_dialogType", "", [""]],
	["_params", [], [[]]]
];

// Validate parameters
if (_dialogType == "") exitWith {
	["Invalid dialog type specified"] call EDRP_fnc_logError;
	false
};

// Check if player is allowed to open dialogs
if (!alive player || EDRP_player_restrained || EDRP_player_incapacitated) exitWith {
	["Cannot open dialog in current state"] call EDRP_fnc_hint;
	false
};

// Close any existing dialogs first
call EDRP_fnc_closeAllDialogs;

// Dialog type mapping
private _dialogMap = createHashMapFromArray [
	// Main system dialogs
	["mainMenu", 3000],
	["quickAction", 3100],
	["giveMoney", 3200],
	["inventory", 3500],
	["itemTransfer", 3600],
	["itemDetails", 3700],
	
	// Shop dialogs
	["shop", 4000],
	["vehicleShop", 4100],
	["clothingShop", 4200],
	["transactionHistory", 4300],
	
	// Communication dialogs
	["phone", 5000],
	["message", 5100],
	["contact", 5200],
	["call", 5300],
	
	// Admin dialogs
	["adminPanel", 6000],
	["adminConfirm", 6100],
	["serverManagement", 6200]
];

// Get dialog IDD
private _dialogIDD = _dialogMap getOrDefault [_dialogType, -1];

if (_dialogIDD == -1) exitWith {
	[format ["Unknown dialog type: %1", _dialogType]] call EDRP_fnc_logError;
	false
};

// Store dialog parameters for initialization
EDRP_dialog_params = _params;
EDRP_dialog_type = _dialogType;

// Create the dialog
private _success = createDialog format ["EDRP_%1", _dialogType];

if (!_success) exitWith {
	[format ["Failed to create dialog: %1", _dialogType]] call EDRP_fnc_logError;
	false
};

// Log dialog creation
[format ["Dialog created: %1 (IDD: %2)", _dialogType, _dialogIDD]] call EDRP_fnc_logInfo;

// Play dialog open sound
playSound "EDRP_ui_open";

// Set dialog variables
EDRP_current_dialog = _dialogType;
EDRP_current_dialog_idd = _dialogIDD;

// Initialize dialog-specific data
switch (_dialogType) do {
	case "mainMenu": {
		[] spawn EDRP_fnc_updateMainMenu;
	};
	
	case "inventory": {
		[] spawn EDRP_fnc_updateInventory;
	};
	
	case "shop": {
		_params spawn EDRP_fnc_updateShop;
	};
	
	case "vehicleShop": {
		_params spawn EDRP_fnc_updateVehicleShop;
	};
	
	case "clothingShop": {
		_params spawn EDRP_fnc_updateClothingShop;
	};
	
	case "phone": {
		[] spawn EDRP_fnc_updatePhone;
	};
	
	case "adminPanel": {
		// Check admin permissions
		if (!(call EDRP_fnc_isAdmin)) exitWith {
			closeDialog _dialogIDD;
			["Access denied - insufficient permissions"] call EDRP_fnc_hint;
		};
		[] spawn EDRP_fnc_updateAdminPanel;
	};
	
	default {
		// Generic dialog initialization
		[_dialogType, _params] spawn EDRP_fnc_initializeDialog;
	};
};

true
