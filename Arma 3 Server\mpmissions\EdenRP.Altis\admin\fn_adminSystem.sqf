/*
	EdenRP Altis Life - Administrative System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Comprehensive admin tools and staff management
	Version: 1.0.0
*/

// Initialize admin system
EDRP_fnc_initAdminSystem = {
	// Admin state variables
	EDRP_admin_level = 0;
	EDRP_admin_mode = false;
	EDRP_admin_tools_open = false;
	EDRP_spectator_mode = false;
	EDRP_admin_logs = [];
	
	// Admin statistics
	EDRP_admin_stats = createHashMapFromArray [
		["actions_performed", 0],
		["players_helped", 0],
		["bans_issued", 0],
		["kicks_issued", 0],
		["warnings_issued", 0]
	];
	
	// Load admin configuration
	[] call EDRP_fnc_loadAdminConfig;
	
	// Check admin permissions
	[] call EDRP_fnc_checkAdminPermissions;
	
	["Admin system initialized"] call EDRP_fnc_logInfo;
};

// Load admin configuration
EDRP_fnc_loadAdminConfig = {
	// Admin levels and permissions
	EDRP_admin_levels = createHashMapFromArray [
		[1, ["Helper", ["teleport_self", "view_player_info", "spectate", "admin_chat"]]],
		[2, ["Moderator", ["teleport_self", "teleport_others", "view_player_info", "spectate", "admin_chat", "kick", "temp_ban", "god_mode", "invisible"]]],
		[3, ["Administrator", ["teleport_self", "teleport_others", "view_player_info", "spectate", "admin_chat", "kick", "temp_ban", "permanent_ban", "god_mode", "invisible", "spawn_items", "spawn_vehicles", "modify_money"]]],
		[4, ["Senior Admin", ["teleport_self", "teleport_others", "view_player_info", "spectate", "admin_chat", "kick", "temp_ban", "permanent_ban", "god_mode", "invisible", "spawn_items", "spawn_vehicles", "modify_money", "server_management", "database_access"]]],
		[5, ["Owner", ["all_permissions"]]]
	];
	
	// Quick action shortcuts
	EDRP_admin_shortcuts = [
		["F1", "Toggle Admin Mode"],
		["F2", "Open Admin Menu"],
		["F3", "Toggle Spectator"],
		["F4", "Teleport to Cursor"],
		["F5", "God Mode Toggle"],
		["F6", "Invisible Toggle"],
		["F7", "Admin Chat"],
		["F8", "Player List"],
		["F9", "Vehicle Spawn"],
		["F10", "Item Spawn"]
	];
	
	// Punishment templates
	EDRP_punishment_templates = [
		["RDM", "Random Death Match - Killing without proper roleplay"],
		["VDM", "Vehicle Death Match - Using vehicles as weapons"],
		["Combat Logging", "Disconnecting during combat or roleplay"],
		["Metagaming", "Using out-of-character information in-game"],
		["Fail RP", "Poor quality roleplay or breaking character"],
		["Exploiting", "Abusing game mechanics or bugs"],
		["Trolling", "Intentionally disrupting gameplay"],
		["Harassment", "Verbal abuse or harassment of other players"]
	];
};

// Check admin permissions
EDRP_fnc_checkAdminPermissions = {
	// Get admin level from server
	[getPlayerUID player] remoteExec ["EDRP_fnc_getAdminLevel", 2];
};

// Set admin level (called from server)
EDRP_fnc_setAdminLevel = {
	params [["_level", 0, [0]]];
	
	EDRP_admin_level = _level;
	
	if (_level > 0) then {
		private _levelInfo = EDRP_admin_levels get _level;
		if (!isNil "_levelInfo") then {
			_levelInfo params ["_title", "_permissions"];
			[format ["Admin permissions loaded: %1 (Level %2)", _title, _level], "success"] call EDRP_fnc_hint;
			
			// Add admin actions
			[] call EDRP_fnc_addAdminActions;
			
			// Setup admin key bindings
			[] call EDRP_fnc_setupAdminKeyBindings;
		};
	};
};

// Toggle admin mode
EDRP_fnc_toggleAdminMode = {
	if (EDRP_admin_level == 0) exitWith {
		["You don't have admin permissions"] call EDRP_fnc_hint;
		false
	};
	
	EDRP_admin_mode = !EDRP_admin_mode;
	
	if (EDRP_admin_mode) then {
		// Enable admin mode
		player setVariable ["admin_mode", true, true];
		
		// Visual indicators
		titleText ["ADMIN MODE ENABLED", "PLAIN DOWN", 2];
		
		// Admin overlay
		[] call EDRP_fnc_showAdminOverlay;
		
		["Admin mode enabled"] call EDRP_fnc_hint;
	} else {
		// Disable admin mode
		player setVariable ["admin_mode", false, true];
		
		// Remove visual indicators
		titleText ["ADMIN MODE DISABLED", "PLAIN DOWN", 2];
		
		// Hide admin overlay
		[] call EDRP_fnc_hideAdminOverlay;
		
		["Admin mode disabled"] call EDRP_fnc_hint;
	};
	
	true
};

// Open admin menu
EDRP_fnc_openAdminMenu = {
	if (EDRP_admin_level == 0) exitWith {
		["You don't have admin permissions"] call EDRP_fnc_hint;
		false
	};
	
	// Create admin menu dialog
	createDialog "EDRP_AdminMenuDialog";
	
	// Update admin menu
	[] call EDRP_fnc_updateAdminMenu;
	
	EDRP_admin_tools_open = true;
	
	true
};

// Update admin menu
EDRP_fnc_updateAdminMenu = {
	private _display = findDisplay 50000;
	if (isNull _display) exitWith {};
	
	// Update admin info
	private _adminInfoCtrl = _display displayCtrl 50001;
	private _levelInfo = EDRP_admin_levels get EDRP_admin_level;
	
	if (!isNil "_levelInfo") then {
		_levelInfo params ["_title", "_permissions"];
		
		private _adminInfo = format [
			"Admin: %1\nLevel: %2 (%3)\nMode: %4\nOnline Players: %5",
			name player,
			EDRP_admin_level,
			_title,
			if (EDRP_admin_mode) then { "ENABLED" } else { "DISABLED" },
			count allPlayers
		];
		_adminInfoCtrl ctrlSetText _adminInfo;
	};
	
	// Update player list
	private _playerList = _display displayCtrl 50002;
	lbClear _playerList;
	
	{
		private _playerName = name _x;
		private _playerUID = getPlayerUID _x;
		private _playerSide = side _x;
		private _distance = round(player distance _x);
		
		private _sideText = switch (_playerSide) do {
			case west: { "Police" };
			case independent: { "Medical" };
			case civilian: { "Civilian" };
			default { "Unknown" };
		};
		
		private _entry = format ["%1 [%2] - %3m", _playerName, _sideText, _distance];
		
		_playerList lbAdd _entry;
		_playerList lbSetData [_forEachIndex, _playerUID];
		_playerList lbSetValue [_forEachIndex, _distance];
		
		// Color code by side
		switch (_playerSide) do {
			case west: { _playerList lbSetColor [_forEachIndex, [0, 0, 1, 1]]; };
			case independent: { _playerList lbSetColor [_forEachIndex, [0, 1, 0, 1]]; };
			case civilian: { _playerList lbSetColor [_forEachIndex, [1, 1, 1, 1]]; };
		};
	} forEach allPlayers;
	
	// Update recent actions
	private _actionList = _display displayCtrl 50003;
	lbClear _actionList;
	
	private _recentActions = EDRP_admin_logs select [count EDRP_admin_logs - 10, 10];
	{
		_actionList lbAdd _x;
	} forEach _recentActions;
};

// Teleport to player
EDRP_fnc_teleportToPlayer = {
	params [["_targetUID", "", [""]]];
	
	if (_targetUID == "") exitWith {
		["No player selected"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "teleport_self"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Teleport to player
	player setPosATL (getPosATL _targetPlayer vectorAdd [2, 2, 0]);
	
	// Log action
	private _logEntry = format ["[%1] %2 teleported to %3", [daytime] call EDRP_fnc_timeToString, name player, name _targetPlayer];
	EDRP_admin_logs pushBack _logEntry;
	
	// Update statistics
	EDRP_admin_stats set ["actions_performed", (EDRP_admin_stats get "actions_performed") + 1];
	
	[format ["Teleported to %1", name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Teleport player to location
EDRP_fnc_teleportPlayerTo = {
	params [["_targetUID", "", [""]], ["_position", [0,0,0], [[]]]];
	
	if (_targetUID == "" || count _position == 0) exitWith {
		["Invalid parameters"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "teleport_others"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send teleport command to target
	[_position] remoteExec ["EDRP_fnc_adminTeleport", _targetPlayer];
	
	// Log action
	private _logEntry = format ["[%1] %2 teleported %3 to %4", [daytime] call EDRP_fnc_timeToString, name player, name _targetPlayer, mapGridPosition _position];
	EDRP_admin_logs pushBack _logEntry;
	
	// Update statistics
	EDRP_admin_stats set ["actions_performed", (EDRP_admin_stats get "actions_performed") + 1];
	
	[format ["Teleported %1 to location", name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Admin teleport (executed on target)
EDRP_fnc_adminTeleport = {
	params [["_position", [0,0,0], [[]]]];
	
	if (count _position == 0) exitWith {};
	
	player setPosATL _position;
	["You have been teleported by an administrator"] call EDRP_fnc_hint;
};

// Kick player
EDRP_fnc_kickPlayer = {
	params [["_targetUID", "", [""]], ["_reason", "No reason provided", [""]]];
	
	if (_targetUID == "") exitWith {
		["No player selected"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "kick"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Confirm kick
	private _message = format [
		"Kick %1?\n\nReason: %2",
		name _targetPlayer,
		_reason
	];
	
	if ([_message, "Kick Player", true, true] call EDRP_fnc_messageBox) then {
		// Send kick command to server
		[_targetUID, _reason, getPlayerUID player] remoteExec ["EDRP_fnc_serverKickPlayer", 2];
		
		// Log action
		private _logEntry = format ["[%1] %2 kicked %3 - Reason: %4", [daytime] call EDRP_fnc_timeToString, name player, name _targetPlayer, _reason];
		EDRP_admin_logs pushBack _logEntry;
		
		// Update statistics
		EDRP_admin_stats set ["kicks_issued", (EDRP_admin_stats get "kicks_issued") + 1];
		EDRP_admin_stats set ["actions_performed", (EDRP_admin_stats get "actions_performed") + 1];
		
		[format ["Kicked %1", name _targetPlayer]] call EDRP_fnc_hint;
		
		true
	} else {
		false
	};
};

// Ban player
EDRP_fnc_banPlayer = {
	params [["_targetUID", "", [""]], ["_reason", "No reason provided", [""]], ["_duration", 0, [0]]];
	
	if (_targetUID == "") exitWith {
		["No player selected"] call EDRP_fnc_hint;
		false
	};
	
	private _permissionNeeded = if (_duration == 0) then { "permanent_ban" } else { "temp_ban" };
	if !([EDRP_admin_level, _permissionNeeded] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Confirm ban
	private _durationText = if (_duration == 0) then { "PERMANENT" } else { format ["%1 hours", _duration] };
	private _message = format [
		"Ban %1?\n\nDuration: %2\nReason: %3",
		name _targetPlayer,
		_durationText,
		_reason
	];
	
	if ([_message, "Ban Player", true, true] call EDRP_fnc_messageBox) then {
		// Send ban command to server
		[_targetUID, _reason, _duration, getPlayerUID player] remoteExec ["EDRP_fnc_serverBanPlayer", 2];
		
		// Log action
		private _logEntry = format ["[%1] %2 banned %3 (%4) - Reason: %5", [daytime] call EDRP_fnc_timeToString, name player, name _targetPlayer, _durationText, _reason];
		EDRP_admin_logs pushBack _logEntry;
		
		// Update statistics
		EDRP_admin_stats set ["bans_issued", (EDRP_admin_stats get "bans_issued") + 1];
		EDRP_admin_stats set ["actions_performed", (EDRP_admin_stats get "actions_performed") + 1];
		
		[format ["Banned %1", name _targetPlayer]] call EDRP_fnc_hint;
		
		true
	} else {
		false
	};
};

// Toggle god mode
EDRP_fnc_toggleGodMode = {
	if !([EDRP_admin_level, "god_mode"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _godMode = player getVariable ["god_mode", false];
	
	if (_godMode) then {
		// Disable god mode
		player setVariable ["god_mode", false, true];
		player allowDamage true;
		["God mode disabled"] call EDRP_fnc_hint;
	} else {
		// Enable god mode
		player setVariable ["god_mode", true, true];
		player allowDamage false;
		player setDamage 0;
		["God mode enabled"] call EDRP_fnc_hint;
	};
	
	// Log action
	private _logEntry = format ["[%1] %2 toggled god mode (%3)", [daytime] call EDRP_fnc_timeToString, name player, if (_godMode) then { "OFF" } else { "ON" }];
	EDRP_admin_logs pushBack _logEntry;
	
	true
};

// Toggle invisibility
EDRP_fnc_toggleInvisibility = {
	if !([EDRP_admin_level, "invisible"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _invisible = player getVariable ["invisible", false];
	
	if (_invisible) then {
		// Disable invisibility
		player setVariable ["invisible", false, true];
		player setCaptive false;
		player setObjectTexture [0, ""];
		["Invisibility disabled"] call EDRP_fnc_hint;
	} else {
		// Enable invisibility
		player setVariable ["invisible", true, true];
		player setCaptive true;
		player setObjectTexture [0, "#(argb,8,8,3)color(0,0,0,0.5)"];
		["Invisibility enabled"] call EDRP_fnc_hint;
	};
	
	// Log action
	private _logEntry = format ["[%1] %2 toggled invisibility (%3)", [daytime] call EDRP_fnc_timeToString, name player, if (_invisible) then { "OFF" } else { "ON" }];
	EDRP_admin_logs pushBack _logEntry;
	
	true
};

// Check admin permission
EDRP_fnc_hasAdminPermission = {
	params [["_level", 0, [0]], ["_permission", "", [""]]];
	
	if (_level == 0) exitWith { false };
	if (_level >= 5) exitWith { true }; // Owner has all permissions
	
	private _levelInfo = EDRP_admin_levels get _level;
	if (isNil "_levelInfo") exitWith { false };
	
	private _permissions = _levelInfo select 1;
	_permission in _permissions
};

// Show admin overlay
EDRP_fnc_showAdminOverlay = {
	// Create admin overlay display
	disableSerialization;
	
	private _display = findDisplay 46;
	private _adminOverlay = _display ctrlCreate ["RscStructuredText", 99999];
	
	_adminOverlay ctrlSetPosition [0.8, 0.02, 0.2, 0.15];
	_adminOverlay ctrlSetBackgroundColor [0, 0, 0, 0.7];
	_adminOverlay ctrlSetTextColor [1, 0, 0, 1];
	_adminOverlay ctrlCommit 0;
	
	// Update overlay periodically
	[] spawn {
		while { EDRP_admin_mode } do {
			private _display = findDisplay 46;
			private _overlay = _display displayCtrl 99999;
			
			if (!isNull _overlay) then {
				private _levelInfo = EDRP_admin_levels get EDRP_admin_level;
				private _title = if (!isNil "_levelInfo") then { _levelInfo select 0 } else { "Admin" };
				
				private _text = format [
					"<t align='center' size='1.2' color='#ff0000'>ADMIN MODE</t><br/>
					<t align='center' size='0.9'>%1 (Level %2)</t><br/>
					<t align='center' size='0.8'>Players: %3</t><br/>
					<t align='center' size='0.8'>Time: %4</t>",
					_title,
					EDRP_admin_level,
					count allPlayers,
					[daytime] call EDRP_fnc_timeToString
				];
				
				_overlay ctrlSetStructuredText parseText _text;
			};
			
			sleep 1;
		};
	};
};

// Hide admin overlay
EDRP_fnc_hideAdminOverlay = {
	private _display = findDisplay 46;
	private _overlay = _display displayCtrl 99999;
	
	if (!isNull _overlay) then {
		ctrlDelete _overlay;
	};
};

// Setup admin key bindings
EDRP_fnc_setupAdminKeyBindings = {
	// Add key event handler for admin shortcuts
	(findDisplay 46) displayAddEventHandler ["KeyDown", {
		params ["_display", "_key", "_shift", "_ctrl", "_alt"];
		
		if (!EDRP_admin_mode || EDRP_admin_level == 0) exitWith { false };
		
		switch (_key) do {
			case 59: { // F1 - Toggle Admin Mode
				[] call EDRP_fnc_toggleAdminMode;
				true
			};
			case 60: { // F2 - Open Admin Menu
				[] call EDRP_fnc_openAdminMenu;
				true
			};
			case 61: { // F3 - Toggle Spectator
				[] call EDRP_fnc_toggleSpectator;
				true
			};
			case 62: { // F4 - Teleport to Cursor
				if ([EDRP_admin_level, "teleport_self"] call EDRP_fnc_hasAdminPermission) then {
					private _pos = screenToWorld [0.5, 0.5];
					player setPosATL _pos;
					["Teleported to cursor position"] call EDRP_fnc_hint;
				};
				true
			};
			case 63: { // F5 - God Mode Toggle
				[] call EDRP_fnc_toggleGodMode;
				true
			};
			case 64: { // F6 - Invisible Toggle
				[] call EDRP_fnc_toggleInvisibility;
				true
			};
			default { false };
		};
	}];
};

// Add admin actions
EDRP_fnc_addAdminActions = {
	if (EDRP_admin_level == 0) exitWith {};
	
	// Admin menu action
	player addAction [
		"<t color='#FF0000'>Admin Menu</t>",
		{
			[] call EDRP_fnc_openAdminMenu;
		},
		[],
		10,
		false,
		true,
		"",
		"EDRP_admin_level > 0"
	];
	
	// Toggle admin mode action
	player addAction [
		"<t color='#FF8000'>Toggle Admin Mode</t>",
		{
			[] call EDRP_fnc_toggleAdminMode;
		},
		[],
		9,
		false,
		true,
		"",
		"EDRP_admin_level > 0"
	];
};

// Initialize admin system on client
if (hasInterface) then {
	[] call EDRP_fnc_initAdminSystem;
};
