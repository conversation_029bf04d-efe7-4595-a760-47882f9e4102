//  File: fn_pickupMoney.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Picks up money
if((count (player nearEntities ["Man", 10])) > 1) then {
	uiSleep (random(4));
};
if((time - eden_action_delay) < 1.5) exitWith {
	hint "You can't rapidly use action keys!";
	if(getPlayerUID (_obj getVariable["inUse", ObjNull]) != (getPlayerUID player)) exitWith{closeDialog 0;};
};

if(call eden_restrictions) exitWith {hint "You are under player restrictions and cannot perform this action! Contact an administrator if you feel this is an error.";};
[[player],"EDENS_fnc_internetCheck",false,false] spawn EDEN_fnc_MP;
eden_didServerRespond = false;
private _maxDelayTime = time + 5;
waitUntil{time > _maxDelayTime || eden_didServerRespond};
if(time > _maxDelayTime) exitWith {hint "Pickup failed, try again.";};

params [
	["_obj",Ob<PERSON><PERSON><PERSON>,[<PERSON>b<PERSON><PERSON><PERSON>]],
    ["_var","",[""]],
    ["_val",0,[0]]
];
if(isNil {_val}) exitWith {};
if(isNull _obj || player distance _obj > 3) exitWith {};

if(!isNil {_val}) then {
	deleteVehicle _obj;
	//waitUntil {isNull _obj};

	player playmove "AinvPknlMstpSlayWrflDnon";
	titleText[format[localize "STR_NOTF_PickedMoney",[_val] call EDEN_fnc_numberText],"PLAIN DOWN"];
	[
		["event","Picked up Cash"],
		["player",name player],
		["player_id",getPlayerUID player],
		["value",[_val] call EDEN_fnc_numberText],
		["location",getPosATL player]
	] call EDEN_fnc_logIt;
	eden_cash = eden_cash + _val;
	eden_cache_cash = eden_cache_cash + _val;
	eden_action_delay = time;
};
