/*
	EdenRP Altis Life - Session Handler
	Author: EdenRP Development Team
	Description: Handles player session management and data synchronization
	Version: 1.0.0
*/

// Session variables
EDRP_session_active = false;
EDRP_session_tries = 0;
EDRP_session_max_tries = 3;
EDRP_session_timeout = 30;

// Data request function
EDRP_fnc_dataRequest = {
	if (EDRP_session_completed) exitWith {};
	
	private _uid = getPlayerUID player;
	private _side = playerSide;
	private _name = profileName;
	
	if (_uid isEqualTo "") exitWith {
		EDRP_loading_status = "<t color='#FF0000'>Invalid player UID - Please restart Arma 3</t>";
		diag_log "EdenRP Session: Invalid player UID";
	};
	
	EDRP_loading_status = format ["<t color='#FF0000'>Requesting player data for UID: %1</t>", _uid];
	diag_log format ["EdenRP Session: Requesting data for %1 (%2)", _name, _uid];
	
	// Send data request to server
	[_uid, _side, _name] remoteExec ["EDRP_fnc_playerDataRequest", 2];
	
	// Start timeout timer
	[{
		if (!EDRP_session_completed) then {
			EDRP_session_tries = EDRP_session_tries + 1;
			
			if (EDRP_session_tries >= EDRP_session_max_tries) then {
				EDRP_loading_status = "<t color='#FF0000'>Failed to load player data - Contact server administrator</t>";
				diag_log "EdenRP Session: Maximum retry attempts reached";
			} else {
				diag_log format ["EdenRP Session: Retry attempt %1/%2", EDRP_session_tries, EDRP_session_max_tries];
				call EDRP_fnc_dataRequest;
			};
		};
	}, [], EDRP_session_timeout] call CBA_fnc_waitAndExecute;
};

// Data received function
EDRP_fnc_dataReceived = {
	params ["_uid", "_playerData"];
	
	if (EDRP_session_completed) exitWith {};
	if (getPlayerUID player != _uid) exitWith {
		diag_log "EdenRP Session: UID mismatch in data response";
		call EDRP_fnc_dataRequest;
	};
	
	EDRP_loading_status = "<t color='#00FF00'>Player data received - Processing...</t>";
	diag_log "EdenRP Session: Player data received successfully";
	
	// Process player data
	if (count _playerData > 0) then {
		// Existing player
		call {
			_playerData params [
				"_name", "_cash", "_bank", "_licenses", "_gear", "_position",
				"_wanted", "_admin_level", "_police_rank", "_medical_rank",
				"_gang_id", "_houses", "_vehicles", "_stats", "_skills"
			];
			
			// Set basic data
			EDRP_player_cash = _cash;
			EDRP_player_bank = _bank;
			EDRP_player_licenses = _licenses;
			EDRP_player_gear = _gear;
			EDRP_player_last_position = _position;
			EDRP_player_wanted = _wanted;
			
			// Set faction ranks
			switch (playerSide) do {
				case west: {
					EDRP_police_rank = _police_rank;
					EDRP_player_rank = _police_rank;
				};
				case independent: {
					EDRP_medical_rank = _medical_rank;
					EDRP_player_rank = _medical_rank;
				};
				case civilian: {
					EDRP_player_gang = _gang_id;
				};
			};
			
			// Set admin level
			EDRP_admin_level = _admin_level;
			player setVariable ["EDRP_admin_level", _admin_level, true];
			
			// Set properties
			EDRP_player_houses = _houses;
			EDRP_player_vehicles = _vehicles;
			
			// Set statistics
			if (!isNil "_stats") then {
				EDRP_player_stats = _stats;
			};
			
			// Set skills
			if (!isNil "_skills") then {
				EDRP_player_skills = _skills;
			};
			
			diag_log "EdenRP Session: Existing player data loaded";
		};
	} else {
		// New player
		EDRP_player_cash = 0;
		EDRP_player_bank = 5000;
		EDRP_player_licenses = [];
		EDRP_player_gear = [];
		EDRP_player_wanted = false;
		EDRP_admin_level = 0;
		EDRP_player_houses = [];
		EDRP_player_vehicles = [];
		EDRP_player_stats = createHashMap;
		EDRP_player_skills = createHashMap;
		
		// Set faction-specific defaults
		switch (playerSide) do {
			case west: {
				EDRP_police_rank = 1;
				EDRP_player_rank = 1;
			};
			case independent: {
				EDRP_medical_rank = 1;
				EDRP_player_rank = 1;
			};
			case civilian: {
				EDRP_player_gang = "";
			};
		};
		
		diag_log "EdenRP Session: New player initialized with default data";
	};
	
	// Apply data to player
	call EDRP_fnc_applyPlayerData;
	
	// Mark session as completed
	EDRP_session_completed = true;
	EDRP_player_data_loaded = true;
	
	EDRP_loading_status = "<t color='#00FF00'>Player data loaded successfully</t>";
	diag_log "EdenRP Session: Session completed successfully";
};

// Apply player data function
EDRP_fnc_applyPlayerData = {
	// Set player variables
	player setVariable ["EDRP_player_cash", EDRP_player_cash, true];
	player setVariable ["EDRP_player_bank", EDRP_player_bank, true];
	player setVariable ["EDRP_player_licenses", EDRP_player_licenses, true];
	player setVariable ["EDRP_player_wanted", EDRP_player_wanted, true];
	player setVariable ["EDRP_player_gang", EDRP_player_gang, true];
	player setVariable ["EDRP_player_rank", EDRP_player_rank, true];
	
	// Apply faction-specific data
	switch (playerSide) do {
		case west: {
			player setVariable ["EDRP_police_rank", EDRP_police_rank, true];
			player setVariable ["EDRP_police_on_duty", false, true];
		};
		case independent: {
			player setVariable ["EDRP_medical_rank", EDRP_medical_rank, true];
			player setVariable ["EDRP_medical_on_duty", false, true];
		};
		case civilian: {
			player setVariable ["EDRP_civilian_occupation", "unemployed", true];
		};
	};
	
	// Load gear if available
	if (count EDRP_player_gear > 0) then {
		[EDRP_player_gear] call EDRP_fnc_loadGear;
	};
};

// Data synchronization function
EDRP_fnc_syncData = {
	if (!EDRP_session_completed) exitWith {};
	if (time - EDRP_last_sync < 60) exitWith {
		["Sync Cooldown", "Please wait before syncing again", "warning"] call EDRP_fnc_notification;
	};
	
	EDRP_last_sync = time;
	
	// Collect current player data
	private _syncData = [
		getPlayerUID player,
		EDRP_player_cash,
		EDRP_player_bank,
		EDRP_player_licenses,
		call EDRP_fnc_saveGear,
		getPosATL player,
		EDRP_player_wanted,
		EDRP_player_gang,
		EDRP_player_rank,
		EDRP_player_stats,
		EDRP_player_skills
	];
	
	// Send to server
	[_syncData] remoteExec ["EDRP_fnc_playerDataSync", 2];
	
	["Data Sync", "Player data synchronized with server", "success"] call EDRP_fnc_notification;
	diag_log "EdenRP Session: Manual data sync completed";
};

// Auto-sync function
EDRP_fnc_autoSync = {
	[] spawn {
		while {EDRP_session_completed} do {
			sleep EDRP_sync_interval;
			
			if (alive player && !isNull player) then {
				// Collect data for auto-sync
				private _autoSyncData = [
					getPlayerUID player,
					EDRP_player_cash,
					EDRP_player_bank,
					getPosATL player,
					time
				];
				
				// Send lightweight sync to server
				[_autoSyncData] remoteExec ["EDRP_fnc_playerAutoSync", 2];
				
				diag_log "EdenRP Session: Auto-sync completed";
			};
		};
	};
};

// Session cleanup function
EDRP_fnc_sessionCleanup = {
	if (!EDRP_session_completed) exitWith {};
	
	// Final data sync before disconnect
	private _finalData = [
		getPlayerUID player,
		EDRP_player_cash,
		EDRP_player_bank,
		EDRP_player_licenses,
		call EDRP_fnc_saveGear,
		getPosATL player,
		EDRP_player_wanted,
		EDRP_player_gang,
		EDRP_player_rank,
		EDRP_player_stats,
		EDRP_player_skills,
		time
	];
	
	[_finalData] remoteExec ["EDRP_fnc_playerDataSave", 2];
	
	diag_log "EdenRP Session: Final data save completed";
};

// Initialize session handler
[] spawn {
	// Wait for system initialization
	waitUntil {EDRP_system_initialized};
	
	// Start data request
	call EDRP_fnc_dataRequest;
	
	// Wait for session completion
	waitUntil {EDRP_session_completed};
	
	// Start auto-sync
	call EDRP_fnc_autoSync;
	
	// Setup disconnect handler
	addMissionEventHandler ["HandleDisconnect", {
		call EDRP_fnc_sessionCleanup;
	}];
};

diag_log "EdenRP Session: Session handler initialized";
