#include "..\..\macro.h"
//  File: fn_poLethals.sqf
//	Author: ikiled
//	Description: Authorize PO lethals around the map

(_this select 3) params [
	["_mode",-1,[0]]
];
if (_mode isEqualTo -1) exitWith {};
if (playerSide != west) exitWith {};
if (__GETC__(life_coplevel) < 6) exitWith {};

if (eden_actions_cooldown > time) exitWith {hint "Please wait a few seconds before trying this action again.";};
private _type = "";

if (_mode isEqualTo 0) then {
	_type = "authorize";
	} else {
	_type = "deauthorize";
};

_areYouSure = [
	format ["Are you sure you want to %1 lethals for Patrol Officers across the map?",_type],
	"Confirm Lethals Toggle",
	"Yes",
	"No"
] call BIS_fnc_guiMessage;

if !(_areYouSure) exitWith {};
if (_mode isEqualTo 0) then {
	private _nearUnits = (nearestObjects[player,["Man"],100]) arrayIntersect playableUnits;
	private _officerUIDs = [];
	 if(count _nearUnits > 1) then {
	 	{
	  		if (((side _x) isEqualTo west) && ((_x getVariable "rank") isEqualTo 2)) then {
				_x setVariable ["lethalsPO", true, true];
				_officerUIDs pushBack (getPlayerUID _x);
			};
	 	} forEach _nearUnits;
 	};
	[
		["event","Auth PO Lethals"],
		["player",name player],
		["player_id",getPlayerUID player],
		["officer_count",count _nearUnits],
		["officers_id",_officerUIDs],
		["position",getPosATL player]
	] call EDEN_fnc_logIt;

	[0,format["%1 has authorized lethals for Patrol Officers within a 500 meter radius from them.",name player]] remoteExecCall ["EDEN_fnc_broadcast",-2];
};

if (_mode isEqualTo 1) then {
	{
		if ((side _x) isEqualTo west) then {
			_x setVariable ["lethalsPO", false, true];
		};
	} forEach playableUnits;
	[
		["event","Deauth PO Lethals"],
		["player",name player],
		["player_id",getPlayerUID player],
		["position",getPosATL player]
	] call EDEN_fnc_logIt;
	[0,format["%1 has de-authorized lethals for Patrol Officers.",name player]] remoteExecCall ["EDEN_fnc_broadcast",-2];
};

hint format ["You have %1d lethals for all Patrol Officers.", _type];

eden_actions_cooldown = (time + 35);
