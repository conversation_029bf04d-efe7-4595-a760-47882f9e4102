//  File: fn_addGangInv.sqf
//	Author: <PERSON> "tkc<PERSON>" Schultz
//  Modifications: Fusah
//	Description: Upgrades the gang buildings virtual storage limit

params [
	["_building",objNull,[objNull]],
	["_isVirtual",true,[true]]
];
if (isNull _building) exitWith {};
if (!license_civ_home) exitWith {hint localize "STR_House_License"};
if !(typeOf _building isEqualTo "Land_i_Shed_Ind_F") exitWith {};
if ((eden_gang_data select 2) < 4) exitWith {};
if (eden_action_inUse) exitWith {};

private _currentInv = _building getVariable ["storageCapacity",10000];
if (_currentInv >= 10000) exitWith {hint "This gang building has the maximum amount of item storage!";};

eden_action_inUse = true;
eden_gangfund_ready = false;
eden_gang_funds = -1;
[[0,eden_gang_data select 0,player],"EDENS_fnc_gangBank",false,false] spawn EDEN_fnc_MP;

waitUntil{eden_gangfund_ready};
uiSleep 0.5;
if (life_donation_house) then{
	if (eden_gang_funds < 1275000) exitWith {hint "You don't have enough money in your gang bank to make this purchase!"; eden_action_inUse = false;};
	private _action = false;
	if (_isVirtual) then {
		_action = [
			"This building supports up to 10,000 storage space. Would you like to add an additional 1,000 inventory space for $1,275,000?",
			"Purchase Storage Space",
			localize "STR_Global_Buy",
			localize "STR_Global_Cancel"
		] call BIS_fnc_GUImessage;
	} else {
		_action = [
			"This building supports up to 900 physical storage space. Would you like to add an additional 150 inventory space for $1,275,000?",
			"Purchase Storage Space",
			localize "STR_Global_Buy",
			localize "STR_Global_Cancel"
		] call BIS_fnc_GUImessage;
	};
	if (_action) then {
		[[_building,player,_isVirtual],"EDENS_fnc_updateGangBldg",false,false] spawn EDEN_fnc_MP;
		hint "Renovating gang building...";
	};
	} else {
	if (eden_gang_funds < 1500000) exitWith {hint "You don't have enough money in your gang bank to make this purchase!"; eden_action_inUse = false;};

	private _action = false;
	if (_isVirtual) then {
		_action = [
			"This building supports up to 10,000 storage space. Would you like to add an additional 1,000 inventory space for $1,500,000?",
			"Purchase Storage Space",
			localize "STR_Global_Buy",
			localize "STR_Global_Cancel"
		] call BIS_fnc_GUImessage;
	} else {
		_action = [
			"This building supports up to 900 physical storage space. Would you like to add an additional 150 inventory space for $1,500,000?",
			"Purchase Storage Space",
			localize "STR_Global_Buy",
			localize "STR_Global_Cancel"
		] call BIS_fnc_GUImessage;
	};

	if (_action) then {
		[[_building,player,_isVirtual],"EDENS_fnc_updateGangBldg",false,false] spawn EDEN_fnc_MP;
		hint "Renovating gang building...";
	};
};
