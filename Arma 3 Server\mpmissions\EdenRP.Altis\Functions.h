class EdenRP_Client_Core {
	tag = "EDRP";

	class CoreSystem {
		file = "core";
		class briefingSystem {};
		class systemInit {preInit=1;};
		class timedMarkerInit {preInit = 1;};
		class initializeCivilian {};
		class initializePolice {};
		class initializeMedical {};
		class initializeSurvival {};
		class introSequence {};
		class setupPlayerActions {};
		class setupEventHandlers {};
		class loadingScreenManager {};
		class loadingScreenIcon {};
		class loadingScreenContent {};
		class systemLogger {};
		class monitorDisplays {};
		class sessionHandler {};
		class playerSetup {};
		class factionSetup {};
		class spawnSelection {};
		class loadingScreen {};
	};

	class DatabaseSystem {
		file = "core\database";
		class asyncCall {};
		class initDatabase {preInit=1;};
	};

	class DialogSystem {
		file = "dialog\functions";
		class createDialog {};
		class updateMainMenu {};
		class switchMenuTab {};
		class closeAllDialogs {};
		class updateInventory {};
		class updateShop {};
		class updatePhone {};
		class updateAdminPanel {};
		class initializeDialog {};
		class keyHandler {};
	};

	class CivilianJobs {
		file = "civilian";
		class jobSystem {};
		class gatheringSystem {};
		class processingSystem {};
		class marketSystem {};
		class truckingSystem {};
		class fishingSystem {};
		class gather {};
		class processAction {};
		class sellAtMarket {};
		class startTruckingJob {};
		class startFishing {};
	};

	class PoliceSystem {
		file = "police";
		class policeSystem {};
		class arrestSystem {};
		class ticketSystem {};
		class goOnDuty {};
		class goOffDuty {};
		class arrestPlayer {};
		class searchPlayer {};
		class restrainPlayer {};
		class issueTicket {};
		class payTicket {};
		class addCrime {};
		class toggleLethals {};
	};

	class MedicalSystem {
		file = "medical";
		class medicalSystem {};
		class reviveSystem {};
		class invoiceSystem {};
		class goOnDutyMedical {};
		class goOffDutyMedical {};
		class revivePlayer {};
		class treatPlayer {};
		class requestMedic {};
		class issueMedicalInvoice {};
		class payMedicalInvoice {};
		class medicQuickGive {};
		class onPlayerKilled {};
		class respawnPlayer {};
	};

	class GangSystem {
		file = "gangs";
		class gangSystem {};
		class territorySystem {};
		class warSystem {};
		class createGang {};
		class joinGang {};
		class leaveGang {};
		class inviteToGang {};
		class kickFromGang {};
		class manageGangRank {};
		class gangChat {};
		class captureTerritory {};
		class declareWar {};
		class warKill {};
		class openGangMenu {};
		class openWarMenu {};
	};

	class VehicleSystem {
		file = "vehicles";
		class vehicleSystem {};
		class vehicleGarage {};
	};

	class AdminSystem {
		file = "admin";
		class adminSystem {};
		class adminTools {};
	};

	class ProgressionSystem {
		file = "progression";
		class progressionSystem {};
	};

	class CommunicationSystem {
		file = "communication";
		class communicationSystem {};
	};

	class RobberySystem {
		file = "robbery";
		class robberySystem {};
	};

	class ShopSystem {
		file = "shops";
		class shopSystem {};
	};

	class SecuritySystem {
		file = "security";
		class securitySystem {};
	};

	class TestingSystem {
		file = "testing";
		class testSuite {};
	};

	class PlayerActions {
		file = "core\actions";
		class arrestPlayer {};
		class playerBeatdown {};
		class purchaseLicense {};
		class buyLotteryTicket {};
		class placeBet {};
		class captureBlackMarket {};
		class captureHideout {};
		class fishingAction {};
		class turtleHunting {};
		class claimVehicle {};
		class claimIllegalGoods {};
		class claimGangVehicle {};
		class closeMapAction {};
		class deliveryComplete {};
		class deployFishingNet {};
		class escortPlayer {};
		class flipVehicle {};
		class gatherResources {};
		class enhancedGathering {};
		class getDeliveryMission {};
		class processAnimal {};
		class hackAntiAirSystem {};
		class hackRadioTower {};
		class hospitalHealing {};
		class handleAnimation {};
		class handleVehicleSpawn {};
		class handleGangVehicles {};
		class handlePoliceIsland {};
		class impoundVehicle {};
		class loadPlayerLoadout {};
		class medicalInvoice {};
		class packupSpikes {};
		class pickupItem {};
		class pickupMoney {};
		class postBail {};
		class processResources {};
		class pulloutPlayer {};
		class pulloutDeadPlayer {};
		class pushVehicle {};
		class putPlayerInVehicle {};
		class refillMagazines {};
		class repairVehicle {};
		class restrainPlayer {};
		class robberyAction {};
		class robShops {};
		class savePlayerLoadout {};
		class searchPlayer {};
		class searchShipWreck {};
		class searchVehicle {};
		class seizeItems {};
		class sellLicense {};
		class serviceHelicopter {};
		class stripPlayerGear {};
		class stopEscorting {};
		class storeVehicle {};
		class suicideBomb {};
		class surrenderAction {};
		class issueTicket {};
		class unrestrainPlayer {};
		class repairObject {};
		class removeKidney {};
		class conquestAction {};
		class buyDopamineCrate {};
		class dopamineCrateAction {};
		class refillDopeMag {};
	};

	class AdminSystem {
		file = "core\admin";
		class adminMenu {};
		class adminSpectate {};
		class adminTeleport {};
		class adminGodMode {};
		class adminInvisible {};
		class adminFreeze {};
		class adminKick {};
		class adminBan {};
		class adminUnban {};
		class adminMute {};
		class adminUnmute {};
		class adminJail {};
		class adminUnjail {};
		class adminHeal {};
		class adminRevive {};
		class adminGiveMoney {};
		class adminTakeMoney {};
		class adminGiveItem {};
		class adminTakeItem {};
		class adminSpawnVehicle {};
		class adminDeleteVehicle {};
		class adminRepairVehicle {};
		class adminRefuelVehicle {};
		class adminFlipVehicle {};
		class adminTeleportTo {};
		class adminTeleportHere {};
		class adminTeleportToCoords {};
		class adminSetTime {};
		class adminSetWeather {};
	};
};