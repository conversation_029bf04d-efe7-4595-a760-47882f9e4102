/*
	EdenRP Altis Life - Ticket System
	Author: EdenRP Development Team
	Description: Handles traffic tickets and minor violations
	Version: 1.0.0
*/

// Issue ticket to player
EDRP_fnc_issueTicket = {
	params [
		["_target", obj<PERSON>ull, [obj<PERSON><PERSON>]],
		["_officer", player, [obj<PERSON><PERSON>]]
	];
	
	if (isNull _target || _target == _officer) exitWith {
		["Invalid ticket target"] call EDRP_fnc_hint;
		false
	};
	
	// Check if officer is on duty
	if (!EDRP_police_active) exitWith {
		["You must be on duty to issue tickets"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is civilian
	if (side _target != civilian) exitWith {
		["You can only ticket civilians"] call EDRP_fnc_hint;
		false
	};
	
	// Open ticket dialog
	[] call EDRP_fnc_openTicketDialog;
	
	true
};

// Open ticket selection dialog
EDRP_fnc_openTicketDialog = {
	// Create ticket dialog
	createDialog "EDRP_TicketDialog";
	
	// Populate violation list
	private _violationList = _display displayCtrl 1500;
	lbClear _violationList;
	
	// Traffic violations
	_violationList lbAdd "Speeding - $500";
	_violationList lbSetData [0, "1"];
	
	_violationList lbAdd "Reckless Driving - $1000";
	_violationList lbSetData [1, "2"];
	
	_violationList lbAdd "Running Red Light - $750";
	_violationList lbSetData [2, "3"];
	
	_violationList lbAdd "Illegal Parking - $250";
	_violationList lbSetData [3, "4"];
	
	_violationList lbAdd "No License - $1500";
	_violationList lbSetData [4, "5"];
	
	_violationList lbAdd "Vehicle Registration - $800";
	_violationList lbSetData [5, "6"];
	
	// Minor violations
	_violationList lbAdd "Public Disturbance - $300";
	_violationList lbSetData [6, "7"];
	
	_violationList lbAdd "Loitering - $200";
	_violationList lbSetData [7, "8"];
	
	_violationList lbAdd "Littering - $150";
	_violationList lbSetData [8, "9"];
	
	_violationList lbAdd "Jaywalking - $100";
	_violationList lbSetData [9, "10"];
	
	// Custom violation option
	_violationList lbAdd "Custom Violation";
	_violationList lbSetData [10, "custom"];
	
	_violationList lbSetCurSel 0;
};

// Process ticket issuance
EDRP_fnc_processTicket = {
	params [
		["_target", objNull, [objNull]],
		["_violationType", 1, [0]],
		["_customAmount", 0, [0]],
		["_customReason", "", [""]]
	];
	
	if (isNull _target) exitWith {
		["Invalid ticket target"] call EDRP_fnc_hint;
		false
	};
	
	// Get violation data
	private _violationData = [_violationType] call EDRP_fnc_getViolationData;
	if (_violationData isEqualTo []) exitWith {
		["Invalid violation type"] call EDRP_fnc_hint;
		false
	};
	
	_violationData params ["_reason", "_amount"];
	
	// Use custom values if provided
	if (_customAmount > 0) then { _amount = _customAmount; };
	if (_customReason != "") then { _reason = _customReason; };
	
	// Create ticket
	private _ticketID = [] call EDRP_fnc_generateTicketID;
	private _ticketData = [
		_ticketID,
		name _target,
		getPlayerUID _target,
		name player,
		getPlayerUID player,
		_reason,
		_amount,
		time,
		false // not paid
	];
	
	// Send ticket to target
	[_ticketData] remoteExec ["EDRP_fnc_receiveTicket", _target];
	
	// Update officer statistics
	EDRP_police_stats set ["tickets_issued", (EDRP_police_stats get "tickets_issued") + 1];
	
	// Show success message
	[format ["Issued ticket to %1 for %2 - Amount: $%3", name _target, _reason, [_amount] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
	
	// Log ticket
	[format ["TICKET: %1 issued ticket to %2 for %3 - $%4", name player, name _target, _reason, _amount]] call EDRP_fnc_logInfo;
	
	true
};

// Get violation data
EDRP_fnc_getViolationData = {
	params [["_type", 1, [0]]];
	
	private _violations = [
		["Speeding", 500],
		["Reckless Driving", 1000],
		["Running Red Light", 750],
		["Illegal Parking", 250],
		["No License", 1500],
		["Vehicle Registration", 800],
		["Public Disturbance", 300],
		["Loitering", 200],
		["Littering", 150],
		["Jaywalking", 100]
	];
	
	if (_type < 1 || _type > count _violations) exitWith { [] };
	
	_violations select (_type - 1)
};

// Generate unique ticket ID
EDRP_fnc_generateTicketID = {
	private _timestamp = round time;
	private _random = round (random 9999);
	format ["TKT-%1-%2", _timestamp, _random]
};

// Receive ticket (client-side)
EDRP_fnc_receiveTicket = {
	params [["_ticketData", [], [[]]]];
	
	if (count _ticketData < 9) exitWith {};
	
	_ticketData params [
		"_ticketID",
		"_targetName",
		"_targetUID",
		"_officerName",
		"_officerUID",
		"_reason",
		"_amount",
		"_timestamp",
		"_paid"
	];
	
	// Add to player's ticket list
	if (isNil "EDRP_player_tickets") then {
		EDRP_player_tickets = [];
	};
	EDRP_player_tickets pushBack _ticketData;
	
	// Show ticket notification
	private _message = format [
		"TRAFFIC TICKET ISSUED\n\nTicket ID: %1\nOfficer: %2\nViolation: %3\nFine: $%4\n\nYou have 30 minutes to pay this ticket.",
		_ticketID,
		_officerName,
		_reason,
		[_amount] call EDRP_fnc_numberText
	];
	
	[_message, "Traffic Ticket", "OK", ""] call EDRP_fnc_messageBox;
	
	// Start ticket timer
	[_ticketID, _amount] spawn EDRP_fnc_ticketTimer;
};

// Ticket payment timer
EDRP_fnc_ticketTimer = {
	params [
		["_ticketID", "", [""]],
		["_amount", 0, [0]]
	];
	
	private _endTime = time + 1800; // 30 minutes
	
	while {time < _endTime} do {
		// Check if ticket was paid
		private _ticketIndex = -1;
		{
			if ((_x select 0) == _ticketID && (_x select 8)) exitWith {
				_ticketIndex = _forEachIndex;
			};
		} forEach EDRP_player_tickets;
		
		if (_ticketIndex >= 0) exitWith {
			// Ticket was paid
			["Ticket payment confirmed"] call EDRP_fnc_hint;
		};
		
		sleep 60; // Check every minute
	};
	
	// Check if ticket is still unpaid
	private _unpaidTicket = false;
	{
		if ((_x select 0) == _ticketID && !(_x select 8)) exitWith {
			_unpaidTicket = true;
		};
	} forEach EDRP_player_tickets;
	
	if (_unpaidTicket) then {
		// Convert to warrant
		[player, 1, _amount * 2] call EDRP_fnc_addCrime; // Double the fine as bounty
		["Unpaid ticket converted to arrest warrant"] call EDRP_fnc_hint;
	};
};

// Pay ticket
EDRP_fnc_payTicket = {
	params [["_ticketID", "", [""]]];
	
	if (_ticketID == "") exitWith {
		["Invalid ticket ID"] call EDRP_fnc_hint;
		false
	};
	
	// Find ticket
	private _ticketIndex = -1;
	private _ticketData = [];
	{
		if ((_x select 0) == _ticketID) exitWith {
			_ticketIndex = _forEachIndex;
			_ticketData = _x;
		};
	} forEach EDRP_player_tickets;
	
	if (_ticketIndex < 0) exitWith {
		["Ticket not found"] call EDRP_fnc_hint;
		false
	};
	
	if (_ticketData select 8) exitWith {
		["Ticket already paid"] call EDRP_fnc_hint;
		false
	};
	
	private _amount = _ticketData select 6;
	
	// Check if player has enough money
	if (EDRP_player_cash < _amount) exitWith {
		["Insufficient funds to pay ticket"] call EDRP_fnc_hint;
		false
	};
	
	// Pay ticket
	EDRP_player_cash = EDRP_player_cash - _amount;
	
	// Mark as paid
	_ticketData set [8, true];
	EDRP_player_tickets set [_ticketIndex, _ticketData];
	
	// Show confirmation
	[format ["Paid ticket %1 for $%2", _ticketID, [_amount] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
	
	// Log payment
	[format ["TICKET PAID: %1 paid ticket %2 for $%3", name player, _ticketID, _amount]] call EDRP_fnc_logInfo;
	
	true
};

// View unpaid tickets
EDRP_fnc_viewTickets = {
	if (isNil "EDRP_player_tickets" || count EDRP_player_tickets == 0) exitWith {
		["You have no tickets"] call EDRP_fnc_hint;
	};
	
	private _unpaidTickets = [];
	{
		if !(_x select 8) then {
			_unpaidTickets pushBack _x;
		};
	} forEach EDRP_player_tickets;
	
	if (count _unpaidTickets == 0) exitWith {
		["All tickets have been paid"] call EDRP_fnc_hint;
	};
	
	// Create ticket list dialog
	createDialog "EDRP_TicketListDialog";
	
	private _ticketList = _display displayCtrl 1500;
	lbClear _ticketList;
	
	{
		_x params [
			"_ticketID",
			"_targetName",
			"_targetUID",
			"_officerName",
			"_officerUID",
			"_reason",
			"_amount",
			"_timestamp",
			"_paid"
		];
		
		private _timeStr = [_timestamp] call EDRP_fnc_formatTime;
		private _entry = format ["%1 - %2 - $%3 - %4", _ticketID, _reason, [_amount] call EDRP_fnc_numberText, _timeStr];
		
		_ticketList lbAdd _entry;
		_ticketList lbSetData [_forEachIndex, _ticketID];
	} forEach _unpaidTickets;
};

// Pay selected ticket from dialog
EDRP_fnc_paySelectedTicket = {
	private _display = findDisplay -1;
	private _ticketList = _display displayCtrl 1500;
	
	private _selection = lbCurSel _ticketList;
	if (_selection < 0) exitWith {
		["No ticket selected"] call EDRP_fnc_hint;
	};
	
	private _ticketID = _ticketList lbData _selection;
	[_ticketID] call EDRP_fnc_payTicket;
	
	// Refresh list
	[] call EDRP_fnc_viewTickets;
};

// Initialize ticket system
if (hasInterface) then {
	// Initialize player tickets array
	if (isNil "EDRP_player_tickets") then {
		EDRP_player_tickets = [];
	};
	
	// Add ticket payment action at ATMs
	player addAction [
		"<t color='#00FF00'>Pay Tickets</t>",
		{
			[] call EDRP_fnc_viewTickets;
		},
		[],
		1,
		false,
		true,
		"",
		"cursorTarget isKindOf 'Land_Atm_01_F' && cursorTarget distance player < 5"
	];
};
