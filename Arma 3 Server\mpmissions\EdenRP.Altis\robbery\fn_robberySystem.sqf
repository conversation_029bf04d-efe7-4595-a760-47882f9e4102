/*
	EdenRP Altis Life - Robbery and Criminal System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Bank robberies, store robberies, and criminal activities
	Version: 1.0.0
*/

// Initialize robbery system
EDRP_fnc_initRobberySystem = {
	// Robbery state variables
	EDRP_robbery_active = false;
	EDRP_robbery_type = "";
	EDRP_robbery_location = "";
	EDRP_robbery_progress = 0;
	EDRP_robbery_participants = [];
	EDRP_last_robbery_time = 0;
	
	// Criminal statistics
	EDRP_criminal_stats = createHashMapFromArray [
		["banks_robbed", 0],
		["stores_robbed", 0],
		["players_robbed", 0],
		["total_stolen", 0],
		["times_caught", 0]
	];
	
	// Load robbery configuration
	[] call EDRP_fnc_loadRobberyConfig;
	
	["Robbery system initialized"] call EDRP_fnc_logInfo;
};

// Load robbery configuration
EDRP_fnc_loadRobberyConfig = {
	// Bank robbery locations
	EDRP_bank_locations = [
		["Kavala Bank", [3692.97, 13205.5, 0], 500000, 900, 5, true],
		["Pyrgos Bank", [16009.8, 16943.9, 0], 350000, 720, 4, true],
		["Athira Bank", [14693.5, 16818.2, 0], 250000, 600, 3, true]
	];
	
	// Store robbery locations
	EDRP_store_locations = [
		["Kavala Market", [3665.39, 13220.1, 0], 25000, 180, 1, false],
		["Kavala Gas Station", [3245.86, 13003.2, 0], 15000, 120, 1, false],
		["Pyrgos Market", [16019.5, 16952.9, 0], 20000, 150, 1, false],
		["Pyrgos Gas Station", [15688.7, 16926.5, 0], 12000, 120, 1, false],
		["Athira Market", [14707.1, 16835.5, 0], 18000, 140, 1, false],
		["Sofia Market", [4022.15, 11669.5, 0], 22000, 160, 1, false],
		["Zaros Market", [8688.33, 15834.2, 0], 16000, 130, 1, false]
	];
	
	// Federal Reserve (high-security target)
	EDRP_federal_reserve = [
		["Federal Reserve", [16019.5, 16952.9, 0], 2000000, 1800, 8, true]
	];
	
	// Robbery requirements
	EDRP_robbery_requirements = createHashMapFromArray [
		["bank", [
			["bolt_cutter", 1],
			["drill", 1],
			["explosive_charge", 2],
			["minimum_players", 3]
		]],
		["store", [
			["lockpick", 1],
			["minimum_players", 1]
		]],
		["federal", [
			["bolt_cutter", 2],
			["drill", 2],
			["explosive_charge", 5],
			["hacking_device", 1],
			["minimum_players", 6]
		]]
	];
	
	// Robbery cooldowns (in seconds)
	EDRP_robbery_cooldowns = createHashMapFromArray [
		["bank", 3600], // 1 hour
		["store", 1800], // 30 minutes
		["federal", 7200], // 2 hours
		["player", 300] // 5 minutes
	];
	
	// Police response levels
	EDRP_police_response = createHashMapFromArray [
		["store", ["Low priority", 2, 300]], // 2 units, 5 min response
		["bank", ["High priority", 4, 180]], // 4 units, 3 min response
		["federal", ["Maximum priority", 8, 120]] // 8 units, 2 min response
	];
	
	// Wanted levels
	EDRP_wanted_levels = [
		[1, "Petty Theft", 5000, 300],
		[2, "Robbery", 15000, 600],
		[3, "Armed Robbery", 35000, 900],
		[4, "Bank Robbery", 75000, 1800],
		[5, "Federal Crime", 150000, 3600]
	];
};

// Start bank robbery
EDRP_fnc_startBankRobbery = {
	params [["_bankIndex", 0, [0]]];
	
	if (_bankIndex < 0 || _bankIndex >= count EDRP_bank_locations) exitWith {
		["Invalid bank location"] call EDRP_fnc_hint;
		false
	};
	
	// Check cooldown
	if (time - EDRP_last_robbery_time < (EDRP_robbery_cooldowns get "bank")) exitWith {
		private _remaining = (EDRP_robbery_cooldowns get "bank") - (time - EDRP_last_robbery_time);
		[format ["Bank robbery cooldown: %1 minutes remaining", ceil(_remaining / 60)]] call EDRP_fnc_hint;
		false
	};
	
	// Check if another robbery is active
	if (EDRP_robbery_active) exitWith {
		["Another robbery is already in progress"] call EDRP_fnc_hint;
		false
	};
	
	// Check requirements
	if !([player, "bank"] call EDRP_fnc_checkRobberyRequirements) exitWith {
		false
	};
	
	EDRP_bank_locations select _bankIndex params ["_name", "_position", "_reward", "_duration", "_minPlayers", "_alarmed"];
	
	// Check minimum players
	private _nearbyPlayers = [];
	{
		if (_x distance _position < 50 && _x != player) then {
			_nearbyPlayers pushBack _x;
		};
	} forEach allPlayers;
	
	if (count _nearbyPlayers < (_minPlayers - 1)) exitWith {
		[format ["Need at least %1 players to rob this bank", _minPlayers]] call EDRP_fnc_hint;
		false
	};
	
	// Start robbery
	EDRP_robbery_active = true;
	EDRP_robbery_type = "bank";
	EDRP_robbery_location = _name;
	EDRP_robbery_progress = 0;
	EDRP_robbery_participants = [player] + _nearbyPlayers;
	EDRP_last_robbery_time = time;
	
	// Consume items
	["bolt_cutter", 1] call EDRP_fnc_removeItem;
	["drill", 1] call EDRP_fnc_removeItem;
	["explosive_charge", 2] call EDRP_fnc_removeItem;
	
	// Alert police
	if (_alarmed) then {
		[_position, "bank", _name] remoteExec ["EDRP_fnc_alertPolice", 2];
	};
	
	// Start robbery progress
	[_duration, _reward, _position] spawn EDRP_fnc_processRobbery;
	
	// Notify participants
	{
		[format ["Bank robbery started at %1!", _name]] remoteExec ["EDRP_fnc_hint", _x];
	} forEach EDRP_robbery_participants;
	
	// Add wanted level
	[player, 4] call EDRP_fnc_addWantedLevel;
	
	true
};

// Start store robbery
EDRP_fnc_startStoreRobbery = {
	params [["_storeIndex", 0, [0]]];
	
	if (_storeIndex < 0 || _storeIndex >= count EDRP_store_locations) exitWith {
		["Invalid store location"] call EDRP_fnc_hint;
		false
	};
	
	// Check cooldown
	if (time - EDRP_last_robbery_time < (EDRP_robbery_cooldowns get "store")) exitWith {
		private _remaining = (EDRP_robbery_cooldowns get "store") - (time - EDRP_last_robbery_time);
		[format ["Store robbery cooldown: %1 minutes remaining", ceil(_remaining / 60)]] call EDRP_fnc_hint;
		false
	};
	
	// Check if another robbery is active
	if (EDRP_robbery_active) exitWith {
		["Another robbery is already in progress"] call EDRP_fnc_hint;
		false
	};
	
	// Check requirements
	if !([player, "store"] call EDRP_fnc_checkRobberyRequirements) exitWith {
		false
	};
	
	EDRP_store_locations select _storeIndex params ["_name", "_position", "_reward", "_duration", "_minPlayers", "_alarmed"];
	
	// Start robbery
	EDRP_robbery_active = true;
	EDRP_robbery_type = "store";
	EDRP_robbery_location = _name;
	EDRP_robbery_progress = 0;
	EDRP_robbery_participants = [player];
	EDRP_last_robbery_time = time;
	
	// Consume items
	["lockpick", 1] call EDRP_fnc_removeItem;
	
	// Alert police (delayed for stores)
	if (_alarmed) then {
		[] spawn {
			sleep (60 + random 120); // 1-3 minute delay
			[_position, "store", _name] remoteExec ["EDRP_fnc_alertPolice", 2];
		};
	};
	
	// Start robbery progress
	[_duration, _reward, _position] spawn EDRP_fnc_processRobbery;
	
	[format ["Store robbery started at %1!", _name]] call EDRP_fnc_hint;
	
	// Add wanted level
	[player, 2] call EDRP_fnc_addWantedLevel;
	
	true
};

// Process robbery progress
EDRP_fnc_processRobbery = {
	params [["_duration", 300, [0]], ["_reward", 50000, [0]], ["_position", [0,0,0], [[]]]];
	
	private _startTime = time;
	private _endTime = _startTime + _duration;
	
	while { EDRP_robbery_active && time < _endTime } do {
		// Check if player is still near
		if (player distance _position > 100) then {
			["You moved too far from the robbery location!"] call EDRP_fnc_hint;
			[] call EDRP_fnc_cancelRobbery;
			exitWith {};
		};
		
		// Update progress
		EDRP_robbery_progress = ((time - _startTime) / _duration) * 100;
		
		// Show progress
		private _remaining = ceil(_endTime - time);
		private _minutes = floor(_remaining / 60);
		private _seconds = _remaining mod 60;
		
		hintSilent format [
			"Robbery Progress: %1%%\nTime Remaining: %2:%3",
			round(EDRP_robbery_progress),
			if (_minutes < 10) then { format ["0%1", _minutes] } else { str _minutes },
			if (_seconds < 10) then { format ["0%1", _seconds] } else { str _seconds }
		];
		
		sleep 1;
	};
	
	// Check if robbery completed successfully
	if (EDRP_robbery_active && time >= _endTime) then {
		[] call EDRP_fnc_completeRobbery;
	};
};

// Complete robbery
EDRP_fnc_completeRobbery = {
	if (!EDRP_robbery_active) exitWith { false };
	
	// Calculate reward based on participants
	private _baseReward = switch (EDRP_robbery_type) do {
		case "bank": { 
			private _bankData = EDRP_bank_locations select 0; // Find correct bank
			{
				if ((_x select 0) == EDRP_robbery_location) exitWith {
					_bankData = _x;
				};
			} forEach EDRP_bank_locations;
			_bankData select 2
		};
		case "store": {
			private _storeData = EDRP_store_locations select 0; // Find correct store
			{
				if ((_x select 0) == EDRP_robbery_location) exitWith {
					_storeData = _x;
				};
			} forEach EDRP_store_locations;
			_storeData select 2
		};
		default { 50000 };
	};
	
	private _participantCount = count EDRP_robbery_participants;
	private _individualReward = floor(_baseReward / _participantCount);
	
	// Distribute rewards
	{
		if (!isNull _x && alive _x) then {
			private _playerReward = _individualReward + (random (_individualReward * 0.2)); // +/- 20% variation
			
			// Add money to player
			_x setVariable ["player_cash", (_x getVariable ["player_cash", 0]) + _playerReward, true];
			
			[format ["Robbery complete! You earned $%1", [_playerReward] call EDRP_fnc_numberText]] remoteExec ["EDRP_fnc_hint", _x];
		};
	} forEach EDRP_robbery_participants;
	
	// Update statistics
	switch (EDRP_robbery_type) do {
		case "bank": {
			EDRP_criminal_stats set ["banks_robbed", (EDRP_criminal_stats get "banks_robbed") + 1];
		};
		case "store": {
			EDRP_criminal_stats set ["stores_robbed", (EDRP_criminal_stats get "stores_robbed") + 1];
		};
	};
	EDRP_criminal_stats set ["total_stolen", (EDRP_criminal_stats get "total_stolen") + _baseReward];
	
	// Award XP
	["civilian", 100, "Robbery"] call EDRP_fnc_awardXP;
	
	// Reset robbery state
	[] call EDRP_fnc_resetRobberyState;
	
	// Global notification
	[format ["BREAKING NEWS: %1 has been robbed!", EDRP_robbery_location]] remoteExec ["EDRP_fnc_hint", -2];
	
	true
};

// Cancel robbery
EDRP_fnc_cancelRobbery = {
	if (!EDRP_robbery_active) exitWith { false };
	
	// Notify participants
	{
		if (!isNull _x && alive _x) then {
			["Robbery cancelled!"] remoteExec ["EDRP_fnc_hint", _x];
		};
	} forEach EDRP_robbery_participants;
	
	// Reset state
	[] call EDRP_fnc_resetRobberyState;
	
	true
};

// Reset robbery state
EDRP_fnc_resetRobberyState = {
	EDRP_robbery_active = false;
	EDRP_robbery_type = "";
	EDRP_robbery_location = "";
	EDRP_robbery_progress = 0;
	EDRP_robbery_participants = [];
	
	hintSilent "";
};

// Check robbery requirements
EDRP_fnc_checkRobberyRequirements = {
	params [["_player", objNull, [objNull]], ["_robberyType", "store", [""]]];
	
	if (isNull _player) exitWith { false };
	
	private _requirements = EDRP_robbery_requirements get _robberyType;
	if (isNil "_requirements") exitWith { false };
	
	private _missingItems = [];
	
	{
		_x params ["_item", "_quantity"];
		
		if (_item == "minimum_players") then {
			// Check minimum players separately
			continue;
		};
		
		if !([_item, _quantity] call EDRP_fnc_hasItem) then {
			_missingItems pushBack format ["%1 x%2", [_item] call EDRP_fnc_getItemName, _quantity];
		};
	} forEach _requirements;
	
	if (count _missingItems > 0) then {
		[format ["Missing required items: %1", _missingItems joinString ", "]] call EDRP_fnc_hint;
		false
	} else {
		true
	};
};

// Rob player
EDRP_fnc_robPlayer = {
	params [["_target", objNull, [objNull]]];
	
	if (isNull _target || _target == player) exitWith {
		["Invalid target"] call EDRP_fnc_hint;
		false
	};
	
	// Check distance
	if (player distance _target > 5) exitWith {
		["Target is too far away"] call EDRP_fnc_hint;
		false
	};
	
	// Check if target is restrained or unconscious
	if !(_target getVariable ["restrained", false] || _target getVariable ["unconscious", false]) exitWith {
		["Target must be restrained or unconscious"] call EDRP_fnc_hint;
		false
	};
	
	// Check cooldown
	private _lastPlayerRobbery = player getVariable ["last_player_robbery", 0];
	if (time - _lastPlayerRobbery < (EDRP_robbery_cooldowns get "player")) exitWith {
		private _remaining = (EDRP_robbery_cooldowns get "player") - (time - _lastPlayerRobbery);
		[format ["Player robbery cooldown: %1 seconds remaining", ceil(_remaining)]] call EDRP_fnc_hint;
		false
	};
	
	// Get target's cash
	private _targetCash = _target getVariable ["player_cash", 0];
	
	if (_targetCash <= 0) exitWith {
		["Target has no cash to steal"] call EDRP_fnc_hint;
		false
	};
	
	// Calculate stolen amount (50-100% of cash)
	private _stolenAmount = floor(_targetCash * (0.5 + random 0.5));
	
	// Transfer money
	_target setVariable ["player_cash", _targetCash - _stolenAmount, true];
	EDRP_player_cash = EDRP_player_cash + _stolenAmount;
	
	// Set cooldown
	player setVariable ["last_player_robbery", time];
	
	// Notify players
	[format ["You robbed $%1 from %2", [_stolenAmount] call EDRP_fnc_numberText, name _target]] call EDRP_fnc_hint;
	[format ["You were robbed of $%1 by %2", [_stolenAmount] call EDRP_fnc_numberText, name player]] remoteExec ["EDRP_fnc_hint", _target];
	
	// Update statistics
	EDRP_criminal_stats set ["players_robbed", (EDRP_criminal_stats get "players_robbed") + 1];
	EDRP_criminal_stats set ["total_stolen", (EDRP_criminal_stats get "total_stolen") + _stolenAmount];
	
	// Add wanted level
	[player, 1] call EDRP_fnc_addWantedLevel;
	
	// Award XP
	["civilian", 25, "Player Robbery"] call EDRP_fnc_awardXP;
	
	true
};

// Add wanted level
EDRP_fnc_addWantedLevel = {
	params [["_player", objNull, [objNull]], ["_level", 1, [0]]];
	
	if (isNull _player) exitWith { false };
	
	private _currentWanted = _player getVariable ["wanted_level", 0];
	private _newWanted = _currentWanted + _level;
	
	if (_newWanted > 5) then { _newWanted = 5; };
	
	_player setVariable ["wanted_level", _newWanted, true];
	
	// Get wanted info
	private _wantedInfo = EDRP_wanted_levels select (_newWanted - 1);
	_wantedInfo params ["_wantedLevel", "_crime", "_bounty", "_duration"];
	
	// Set wanted timer
	_player setVariable ["wanted_timer", time + _duration];
	
	[format ["WANTED: %1 - Bounty: $%2", _crime, [_bounty] call EDRP_fnc_numberText]] remoteExec ["EDRP_fnc_hint", _player];
	
	// Alert police
	[getPlayerUID _player, name _player, _crime, _bounty] remoteExec ["EDRP_fnc_addWantedPlayer", 2];
	
	true
};

// Add robbery actions
EDRP_fnc_addRobberyActions = {
	// Rob player action
	player addAction [
		"<t color='#FF0000'>Rob Player</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_robPlayer;
		},
		[],
		5,
		true,
		true,
		"",
		"cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 5 && (cursorTarget getVariable ['restrained', false] || cursorTarget getVariable ['unconscious', false])"
	];
	
	// Bank robbery actions (add at bank locations)
	{
		_x params ["_name", "_position", "_reward", "_duration", "_minPlayers", "_alarmed"];
		
		private _bankObj = createSimpleObject ["Land_Atm_02_F", _position];
		_bankObj addAction [
			format ["<t color='#FF8000'>Rob %1</t>", _name],
			{
				params ["_target", "_caller", "_actionId", "_arguments"];
				_arguments params ["_bankIndex"];
				[_bankIndex] call EDRP_fnc_startBankRobbery;
			},
			[_forEachIndex],
			3,
			true,
			true,
			"",
			"!EDRP_robbery_active && ['bolt_cutter'] call EDRP_fnc_hasItem && ['drill'] call EDRP_fnc_hasItem"
		];
	} forEach EDRP_bank_locations;
	
	// Store robbery actions (add at store locations)
	{
		_x params ["_name", "_position", "_reward", "_duration", "_minPlayers", "_alarmed"];
		
		private _storeObj = createSimpleObject ["Land_CashDesk_01_F", _position];
		_storeObj addAction [
			format ["<t color='#FFA500'>Rob %1</t>", _name],
			{
				params ["_target", "_caller", "_actionId", "_arguments"];
				_arguments params ["_storeIndex"];
				[_storeIndex] call EDRP_fnc_startStoreRobbery;
			},
			[_forEachIndex],
			3,
			true,
			true,
			"",
			"!EDRP_robbery_active && ['lockpick'] call EDRP_fnc_hasItem"
		];
	} forEach EDRP_store_locations;
};

// Initialize robbery system on client
if (hasInterface) then {
	[] call EDRP_fnc_initRobberySystem;
	
	// Add robbery actions
	[] call EDRP_fnc_addRobberyActions;
};
