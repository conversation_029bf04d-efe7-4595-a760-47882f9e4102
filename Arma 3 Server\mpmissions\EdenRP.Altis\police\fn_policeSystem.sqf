/*
	EdenRP Altis Life - Police System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Core police system with ranks, equipment, and actions
	Version: 1.0.0
*/

// Initialize police system
EDRP_fnc_initPoliceSystem = {
	// Police state variables
	EDRP_police_active = false;
	EDRP_police_rank = 1;
	EDRP_police_department = "patrol";
	EDRP_police_badge_number = 0;
	EDRP_police_callsign = "";
	
	// Police equipment state
	EDRP_police_lethals_authorized = false;
	EDRP_police_equipment_level = 1;
	EDRP_police_vehicle = objNull;
	
	// Police statistics
	EDRP_police_stats = createHashMapFromArray [
		["arrests_made", 0],
		["tickets_issued", 0],
		["searches_conducted", 0],
		["evidence_collected", 0],
		["vehicles_impounded", 0],
		["time_on_duty", 0]
	];
	
	// Load police configuration
	[] call EDRP_fnc_loadPoliceConfig;
	
	["Police system initialized"] call EDRP_fnc_logInfo;
};

// Load police configuration
EDRP_fnc_loadPoliceConfig = {
	// Police rank structure (adapted from Olympus)
	EDRP_police_ranks = [
		["Cadet", 1, 25000, ["hgun_P07_F"]],
		["Officer", 2, 30000, ["hgun_P07_F", "SMG_02_F"]],
		["Senior Officer", 3, 35000, ["hgun_P07_F", "SMG_02_F"]],
		["Corporal", 4, 40000, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F"]],
		["Sergeant", 5, 45000, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F"]],
		["Staff Sergeant", 6, 50000, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F", "arifle_MX_F"]],
		["Lieutenant", 7, 55000, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F", "arifle_MX_F"]],
		["Captain", 8, 60000, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F", "arifle_MX_F"]],
		["Major", 9, 65000, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F", "arifle_MX_F"]],
		["Chief", 10, 70000, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F", "arifle_MX_F"]]
	];
	
	// Police departments
	EDRP_police_departments = [
		["Patrol", "patrol", "General patrol duties"],
		["Traffic", "traffic", "Traffic enforcement and highway patrol"],
		["Detective", "detective", "Criminal investigations"],
		["SWAT", "swat", "Special weapons and tactics"],
		["Air Unit", "air", "Aerial support and surveillance"],
		["K9 Unit", "k9", "Canine operations"]
	];
	
	// Crime types and bounties (adapted from Olympus)
	EDRP_crime_types = [
		[1, "Speeding", 500],
		[2, "Reckless Driving", 1000],
		[3, "Hit and Run", 2500],
		[4, "Vehicle Theft", 5000],
		[5, "Possession of Illegal Items", 2000],
		[6, "Drug Possession", 3000],
		[7, "Drug Trafficking", 8000],
		[8, "Assault", 3500],
		[9, "Armed Robbery", 12000],
		[10, "Bank Robbery", 25000],
		[11, "Murder", 50000],
		[12, "Terrorism", 75000],
		[13, "Kidnapping", 15000],
		[14, "Extortion", 8000],
		[15, "Money Laundering", 10000]
	];
	
	// Police equipment by rank
	EDRP_police_equipment = createHashMapFromArray [
		[1, ["hgun_P07_F", "16Rnd_9x21_Mag", "FirstAidKit"]],
		[2, ["hgun_P07_F", "SMG_02_F", "16Rnd_9x21_Mag", "30Rnd_9x21_Mag_SMG_02", "FirstAidKit"]],
		[3, ["hgun_P07_F", "SMG_02_F", "16Rnd_9x21_Mag", "30Rnd_9x21_Mag_SMG_02", "FirstAidKit", "Medikit"]],
		[4, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F", "16Rnd_9x21_Mag", "30Rnd_9x21_Mag_SMG_02", "20Rnd_556x45_UW_mag", "FirstAidKit", "Medikit"]],
		[5, ["hgun_P07_F", "SMG_02_F", "arifle_SDAR_F", "16Rnd_9x21_Mag", "30Rnd_9x21_Mag_SMG_02", "20Rnd_556x45_UW_mag", "FirstAidKit", "Medikit"]]
	];
};

// Go on duty as police
EDRP_fnc_goOnDuty = {
	if (EDRP_police_active) exitWith {
		["You are already on duty"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player is police faction
	if (EDRP_player_faction != "police") exitWith {
		["You are not a police officer"] call EDRP_fnc_hint;
		false
	};
	
	// Set on duty
	EDRP_police_active = true;
	
	// Load police gear
	[] call EDRP_fnc_loadPoliceGear;
	
	// Set police skin
	[] call EDRP_fnc_setPoliceUniform;
	
	// Add police actions
	[] call EDRP_fnc_addPoliceActions;
	
	// Show on duty message
	["You are now on duty", "success"] call EDRP_fnc_hint;
	
	// Broadcast to other police
	[format ["%1 is now on duty", name player]] remoteExec ["EDRP_fnc_policeMessage", west];
	
	// Start duty timer
	EDRP_police_duty_start = time;
	
	true
};

// Go off duty
EDRP_fnc_goOffDuty = {
	if (!EDRP_police_active) exitWith {
		["You are not on duty"] call EDRP_fnc_hint;
		false
	};
	
	// Set off duty
	EDRP_police_active = false;
	
	// Remove police actions
	[] call EDRP_fnc_removePoliceActions;
	
	// Load civilian gear
	[] call EDRP_fnc_loadCivilianGear;
	
	// Update duty time statistics
	if (!isNil "EDRP_police_duty_start") then {
		private _dutyTime = time - EDRP_police_duty_start;
		EDRP_police_stats set ["time_on_duty", (EDRP_police_stats get "time_on_duty") + _dutyTime];
	};
	
	// Show off duty message
	["You are now off duty"] call EDRP_fnc_hint;
	
	// Broadcast to other police
	[format ["%1 is now off duty", name player]] remoteExec ["EDRP_fnc_policeMessage", west];
	
	true
};

// Load police gear based on rank
EDRP_fnc_loadPoliceGear = {
	// Strip current gear
	removeAllWeapons player;
	removeAllItems player;
	removeAllAssignedItems player;
	removeUniform player;
	removeVest player;
	removeBackpack player;
	removeHeadgear player;
	removeGoggles player;
	
	// Add police uniform
	player forceAddUniform "U_Rangemaster_F";
	player addVest "V_TacVest_blk_POLICE";
	player addHeadgear "H_Cap_police";
	
	// Add basic items
	player addItem "ItemMap";
	player assignItem "ItemMap";
	player addItem "ItemCompass";
	player assignItem "ItemCompass";
	player addItem "ItemWatch";
	player assignItem "ItemWatch";
	player addItem "ItemRadio";
	player assignItem "ItemRadio";
	player addItem "ItemGPS";
	player assignItem "ItemGPS";
	
	// Add rank-specific equipment
	private _equipment = EDRP_police_equipment getOrDefault [EDRP_police_rank, []];
	{
		if (isClass (configFile >> "CfgWeapons" >> _x)) then {
			player addWeapon _x;
		} else {
			player addItem _x;
		};
	} forEach _equipment;
	
	// Add police-specific items
	player addItem "Binocular";
	player addItem "ToolKit"; // For vehicle repairs
	
	// Set police variables
	player setVariable ["isPolice", true, true];
	player setVariable ["rank", EDRP_police_rank, true];
	player setVariable ["department", EDRP_police_department, true];
};

// Set police uniform based on rank and department
EDRP_fnc_setPoliceUniform = {
	private _uniform = "U_Rangemaster_F";
	private _vest = "V_TacVest_blk_POLICE";
	private _headgear = "H_Cap_police";
	
	// Department-specific uniforms
	switch (EDRP_police_department) do {
		case "swat": {
			_uniform = "U_B_CombatUniform_mcam_tshirt";
			_vest = "V_PlateCarrier2_rgr";
			_headgear = "H_HelmetB_grass";
		};
		case "traffic": {
			_uniform = "U_Rangemaster_F";
			_vest = "V_TacVest_blk_POLICE";
			_headgear = "H_Cap_police";
		};
		case "detective": {
			_uniform = "U_I_C_Soldier_Bandit_4_F";
			_vest = "";
			_headgear = "";
		};
		case "air": {
			_uniform = "U_B_HeliPilotCoveralls";
			_vest = "V_TacVest_blk_POLICE";
			_headgear = "H_PilotHelmetHeli_B";
		};
	};
	
	// Apply uniform
	if (_uniform != "") then { player forceAddUniform _uniform; };
	if (_vest != "") then { player addVest _vest; };
	if (_headgear != "") then { player addHeadgear _headgear; };
};

// Add police actions to scroll wheel
EDRP_fnc_addPoliceActions = {
	// Arrest action
	player addAction [
		"<t color='#FF0000'>Arrest</t>",
		{
			private _target = cursorTarget;
			if (isNull _target || !(_target isKindOf "Man") || _target == player) exitWith {
				["Invalid target for arrest"] call EDRP_fnc_hint;
			};
			[_target] call EDRP_fnc_arrestPlayer;
		},
		[],
		6,
		true,
		true,
		"",
		"EDRP_police_active && cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 5"
	];
	
	// Search action
	player addAction [
		"<t color='#FFFF00'>Search</t>",
		{
			private _target = cursorTarget;
			if (isNull _target || !(_target isKindOf "Man") || _target == player) exitWith {
				["Invalid target for search"] call EDRP_fnc_hint;
			};
			[_target] call EDRP_fnc_searchPlayer;
		},
		[],
		5,
		true,
		true,
		"",
		"EDRP_police_active && cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 5"
	];
	
	// Ticket action
	player addAction [
		"<t color='#00FF00'>Issue Ticket</t>",
		{
			private _target = cursorTarget;
			if (isNull _target || !(_target isKindOf "Man") || _target == player) exitWith {
				["Invalid target for ticket"] call EDRP_fnc_hint;
			};
			[_target] call EDRP_fnc_issueTicket;
		},
		[],
		4,
		true,
		true,
		"",
		"EDRP_police_active && cursorTarget isKindOf 'Man' && cursorTarget != player && cursorTarget distance player < 5"
	];
	
	// Impound vehicle action
	player addAction [
		"<t color='#FF8000'>Impound Vehicle</t>",
		{
			private _vehicle = cursorTarget;
			if (isNull _vehicle || !(_vehicle isKindOf "LandVehicle")) exitWith {
				["Invalid vehicle for impound"] call EDRP_fnc_hint;
			};
			[_vehicle] call EDRP_fnc_impoundVehicle;
		},
		[],
		3,
		true,
		true,
		"",
		"EDRP_police_active && cursorTarget isKindOf 'LandVehicle' && cursorTarget distance player < 10"
	];
};

// Remove police actions
EDRP_fnc_removePoliceActions = {
	// Remove all actions (simplified - in practice you'd track action IDs)
	removeAllActions player;
};

// Get police rank name
EDRP_fnc_getPoliceRankName = {
	params [["_rank", EDRP_police_rank, [0]]];
	
	private _rankData = EDRP_police_ranks select (_rank - 1);
	if (isNil "_rankData") exitWith { "Unknown" };
	
	_rankData select 0
};

// Get police rank salary
EDRP_fnc_getPoliceRankSalary = {
	params [["_rank", EDRP_police_rank, [0]]];
	
	private _rankData = EDRP_police_ranks select (_rank - 1);
	if (isNil "_rankData") exitWith { 0 };
	
	_rankData select 2
};

// Check if player can use lethal force
EDRP_fnc_canUseLethals = {
	// Rank 3+ can use lethals, or rank 2 with authorization
	(EDRP_police_rank >= 3) || (EDRP_police_rank == 2 && EDRP_police_lethals_authorized)
};

// Toggle lethal/non-lethal mode (adapted from Olympus)
EDRP_fnc_toggleLethals = {
	if (!EDRP_police_active) exitWith {
		["You must be on duty to toggle lethals"] call EDRP_fnc_hint;
	};
	
	private _currentWeapon = currentWeapon player;
	if (_currentWeapon == "") exitWith {
		["No weapon equipped"] call EDRP_fnc_hint;
	};
	
	// Check if can use lethals
	if !([] call EDRP_fnc_canUseLethals) exitWith {
		["You are not authorized to use lethal force"] call EDRP_fnc_hint;
	};
	
	// Toggle lethal mode
	private _isNonLethal = player getVariable ["nonLethals", false];
	
	if (_isNonLethal) then {
		// Switch to lethal
		player setVariable ["nonLethals", false, true];
		["Switched to LETHAL mode", "warning"] call EDRP_fnc_hint;
	} else {
		// Switch to non-lethal
		player setVariable ["nonLethals", true, true];
		["Switched to NON-LETHAL mode", "info"] call EDRP_fnc_hint;
	};
};

// Initialize police system on client
if (hasInterface && EDRP_player_faction == "police") then {
	[] call EDRP_fnc_initPoliceSystem;
};
