//  File: fn_receiveMoney.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Receives money
private["_unit","_val","_from"];
_unit = param [0,<PERSON><PERSON><PERSON><PERSON>,[<PERSON><PERSON><PERSON><PERSON>]];
_val = param [1,"",[""]];
_from = param [2,<PERSON><PERSON><PERSON><PERSON>,[<PERSON><PERSON><PERSON><PERSON>]];
if(isNull _unit || isNull _from || _val == "") exitWith {};
if(player != _unit) exitWith {};
if(!([_val] call EDEN_fnc_isNumeric)) exitWith {};
if(_unit == _from) exitWith {}; //Bad boy, trying to exploit his way to riches.

hint format[localize "STR_NOTF_GivenMoney",_from getVariable["realname",name _from],[(parseNumber (_val))] call EDEN_fnc_numberText];
eden_cash = eden_cash + (parseNumber(_val));
eden_cache_cash = eden_cache_cash + (parseNumber(_val));