/*
	EdenRP Altis Life - Database Initialization Function
	Author: EdenRP Development Team
	Description: Initializes database connection and loads server data
	Version: 1.0.0
*/

// Database connection status
EDRP_database_connected = false;
EDRP_database_retries = 0;
EDRP_database_max_retries = 5;

// Database configuration
EDRP_database_name = "edenrp";
EDRP_database_prefix = "edrp_";

diag_log "EDRP Database: Starting initialization...";

// Function to test database connection
EDRP_fnc_testConnection = {
	private _testQuery = "SELECT 1 as test";
	private _result = [0, _testQuery] call EDRP_fnc_asyncCall;
	
	if (count _result > 0 && {(_result select 0) select 0 isEqualTo 1}) then {
		true
	} else {
		false
	};
};

// Function to initialize extDB3
EDRP_fnc_initExtDB = {
	try {
		// Get extDB3 version
		private _version = "extDB3" callExtension "9:VERSION";
		if (_version isEqualTo "") then {
			throw "extDB3 extension not loaded";
		};
		
		diag_log format ["EDRP Database: extDB3 Version: %1", _version];
		
		// Add database connection
		private _result = "extDB3" callExtension format ["9:ADD_DATABASE:%1", EDRP_database_name];
		if !(_result isEqualTo "[1]") then {
			throw format ["Failed to add database connection: %1", _result];
		};
		
		// Add database protocol
		_result = "extDB3" callExtension format ["9:ADD_DATABASE_PROTOCOL:%1:SQL:%2:TEXT2", EDRP_database_name, EDRP_database_name];
		if !(_result isEqualTo "[1]") then {
			throw format ["Failed to add database protocol: %1", _result];
		};
		
		diag_log "EDRP Database: extDB3 initialized successfully";
		true
		
	} catch {
		diag_log format ["EDRP Database Error: %1", _exception];
		false
	};
};

// Function to load server configuration
EDRP_fnc_loadServerConfig = {
	private _query = format ["SELECT * FROM %1server_config WHERE id = 1", EDRP_database_prefix];
	private _result = [0, _query, true] call EDRP_fnc_asyncCall;
	
	if (count _result > 0) then {
		EDRP_server_name = _result select 1;
		EDRP_server_version = _result select 2;
		EDRP_server_max_players = _result select 3;
		
		// Parse JSON settings
		if (!isNil {_result select 4}) then {
			EDRP_economy_settings = parseSimpleArray (_result select 4);
		};
		
		if (!isNil {_result select 5}) then {
			EDRP_faction_settings = parseSimpleArray (_result select 5);
		};
		
		if (!isNil {_result select 6}) then {
			EDRP_admin_list = parseSimpleArray (_result select 6);
		};
		
		diag_log "EDRP Database: Server configuration loaded";
		true
	} else {
		diag_log "EDRP Database Warning: No server configuration found, using defaults";
		false
	};
};

// Function to load market data
EDRP_fnc_loadMarketData = {
	private _query = format ["SELECT item_name, current_price, demand_level, supply_level FROM %1market_data", EDRP_database_prefix];
	private _result = [0, _query] call EDRP_fnc_asyncCall;
	
	EDRP_market_data = createHashMap;
	
	{
		private _itemName = _x select 0;
		private _price = _x select 1;
		private _demand = _x select 2;
		private _supply = _x select 3;
		
		EDRP_market_data set [_itemName, [_price, _demand, _supply]];
	} forEach _result;
	
	publicVariable "EDRP_market_data";
	diag_log format ["EDRP Database: Loaded %1 market items", count _result];
};

// Function to load active gangs
EDRP_fnc_loadGangs = {
	private _query = format ["SELECT id, name, tag, leader_id, bank_balance, level FROM %1gangs WHERE active = 1", EDRP_database_prefix];
	private _result = [0, _query] call EDRP_fnc_asyncCall;
	
	EDRP_server_gangs = [];
	
	{
		private _gang = createHashMap;
		_gang set ["id", _x select 0];
		_gang set ["name", _x select 1];
		_gang set ["tag", _x select 2];
		_gang set ["leader", _x select 3];
		_gang set ["bank", _x select 4];
		_gang set ["level", _x select 5];
		
		EDRP_server_gangs pushBack _gang;
	} forEach _result;
	
	publicVariable "EDRP_server_gangs";
	diag_log format ["EDRP Database: Loaded %1 active gangs", count _result];
};

// Function to load territories
EDRP_fnc_loadTerritories = {
	private _query = format ["SELECT id, territory_name, territory_type, controlling_gang, capture_points FROM %1territories", EDRP_database_prefix];
	private _result = [0, _query] call EDRP_fnc_asyncCall;
	
	EDRP_server_territories = [];
	
	{
		private _territory = createHashMap;
		_territory set ["id", _x select 0];
		_territory set ["name", _x select 1];
		_territory set ["type", _x select 2];
		_territory set ["gang", _x select 3];
		_territory set ["points", _x select 4];
		
		EDRP_server_territories pushBack _territory;
	} forEach _result;
	
	publicVariable "EDRP_server_territories";
	diag_log format ["EDRP Database: Loaded %1 territories", count _result];
};

// Main initialization sequence
[] spawn {
	// Initialize extDB3
	if !(call EDRP_fnc_initExtDB) exitWith {
		diag_log "EDRP Database: Failed to initialize extDB3";
	};
	
	// Wait a moment for connection to establish
	sleep 1;
	
	// Test database connection
	private _connectionAttempts = 0;
	while {_connectionAttempts < EDRP_database_max_retries && !EDRP_database_connected} do {
		_connectionAttempts = _connectionAttempts + 1;
		
		if (call EDRP_fnc_testConnection) then {
			EDRP_database_connected = true;
			diag_log "EDRP Database: Connection established successfully";
		} else {
			diag_log format ["EDRP Database: Connection attempt %1/%2 failed", _connectionAttempts, EDRP_database_max_retries];
			sleep 2;
		};
	};
	
	// Exit if connection failed
	if (!EDRP_database_connected) exitWith {
		diag_log "EDRP Database: Failed to establish connection after maximum retries";
	};
	
	// Load server data
	call EDRP_fnc_loadServerConfig;
	call EDRP_fnc_loadMarketData;
	call EDRP_fnc_loadGangs;
	call EDRP_fnc_loadTerritories;
	
	// Set server as ready
	EDRP_server_isReady = true;
	publicVariable "EDRP_server_isReady";
	
	diag_log "EDRP Database: Initialization complete - Server is ready";
	
	// Start periodic maintenance tasks
	[] spawn {
		while {true} do {
			sleep 300; // 5 minutes
			
			// Update market prices
			call EDRP_fnc_updateMarketPrices;
			
			// Clean up inactive vehicles
			call EDRP_fnc_cleanupVehicles;
		};
	};
};

// Market price update function
EDRP_fnc_updateMarketPrices = {
	private _query = format ["CALL UpdateMarketPrices()", EDRP_database_prefix];
	[0, _query] call EDRP_fnc_asyncCall;
	
	// Reload market data
	call EDRP_fnc_loadMarketData;
};

// Vehicle cleanup function
EDRP_fnc_cleanupVehicles = {
	private _query = format ["CALL ResetVehicleActivity()", EDRP_database_prefix];
	[0, _query] call EDRP_fnc_asyncCall;
};

diag_log "EDRP Database: Initialization script loaded";
