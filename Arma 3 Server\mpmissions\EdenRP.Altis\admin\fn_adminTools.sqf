/*
	EdenRP Altis Life - Advanced Admin Tools (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Advanced administrative tools and utilities
	Version: 1.0.0
*/

// Toggle spectator mode
EDRP_fnc_toggleSpectator = {
	if !([EDRP_admin_level, "spectate"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	EDRP_spectator_mode = !EDRP_spectator_mode;
	
	if (EDRP_spectator_mode) then {
		// Enable spectator mode
		player setVariable ["spectator_mode", true, true];
		
		// Store original position
		EDRP_spectator_original_pos = getPosATL player;
		
		// Make player invisible and invulnerable
		player setCaptive true;
		player allowDamage false;
		player setObjectTexture [0, "#(argb,8,8,3)color(0,0,0,0)"];
		
		// Enable free camera
		[] spawn EDRP_fnc_spectatorCamera;
		
		["Spectator mode enabled - Use WASD to move, Mouse to look"] call EDRP_fnc_hint;
	} else {
		// Disable spectator mode
		player setVariable ["spectator_mode", false, true];
		
		// Restore player
		player setCaptive false;
		player allowDamage true;
		player setObjectTexture [0, ""];
		
		// Return to original position
		if (!isNil "EDRP_spectator_original_pos") then {
			player setPosATL EDRP_spectator_original_pos;
		};
		
		// Disable free camera
		EDRP_spectator_active = false;
		
		["Spectator mode disabled"] call EDRP_fnc_hint;
	};
	
	true
};

// Spectator camera system
EDRP_fnc_spectatorCamera = {
	EDRP_spectator_active = true;
	private _camera = "camera" camCreate (getPosATL player);
	_camera cameraEffect ["internal", "back"];
	
	private _pos = getPosATL player;
	private _dir = getDir player;
	private _pitch = 0;
	
	while { EDRP_spectator_active && EDRP_spectator_mode } do {
		// Get input
		private _forward = if (inputAction "moveForward" > 0) then { 1 } else { 0 };
		private _backward = if (inputAction "moveBack" > 0) then { 1 } else { 0 };
		private _left = if (inputAction "moveLeft" > 0) then { 1 } else { 0 };
		private _right = if (inputAction "moveRight" > 0) then { 1 } else { 0 };
		private _up = if (inputAction "moveUp" > 0) then { 1 } else { 0 };
		private _down = if (inputAction "moveDown" > 0) then { 1 } else { 0 };
		
		// Calculate movement
		private _speed = 10;
		if (inputAction "turboToggle" > 0) then { _speed = 50; };
		
		private _moveVector = [0, 0, 0];
		_moveVector = _moveVector vectorAdd ([sin _dir, cos _dir, 0] vectorMultiply ((_forward - _backward) * _speed));
		_moveVector = _moveVector vectorAdd ([sin(_dir + 90), cos(_dir + 90), 0] vectorMultiply ((_right - _left) * _speed));
		_moveVector = _moveVector vectorAdd ([0, 0, (_up - _down) * _speed]);
		
		_pos = _pos vectorAdd _moveVector;
		
		// Update camera
		_camera setPosATL _pos;
		_camera setDir _dir;
		
		sleep 0.01;
	};
	
	// Cleanup
	_camera cameraEffect ["terminate", "back"];
	camDestroy _camera;
};

// Spawn item for player
EDRP_fnc_spawnItemForPlayer = {
	params [["_targetUID", "", [""]], ["_item", "", [""]], ["_quantity", 1, [0]]];
	
	if (_targetUID == "" || _item == "") exitWith {
		["Invalid parameters"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "spawn_items"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send spawn command to target
	[_item, _quantity] remoteExec ["EDRP_fnc_adminGiveItem", _targetPlayer];
	
	// Log action
	private _logEntry = format ["[%1] %2 spawned %3 x%4 for %5", [daytime] call EDRP_fnc_timeToString, name player, _item, _quantity, name _targetPlayer];
	EDRP_admin_logs pushBack _logEntry;
	
	[format ["Spawned %1 x%2 for %3", _item, _quantity, name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Admin give item (executed on target)
EDRP_fnc_adminGiveItem = {
	params [["_item", "", [""]], ["_quantity", 1, [0]]];
	
	if (_item == "" || _quantity <= 0) exitWith {};
	
	// Add item to player inventory
	if ([_item, _quantity] call EDRP_fnc_addItem) then {
		[format ["Administrator gave you %1 x%2", [_item] call EDRP_fnc_getItemName, _quantity], "success"] call EDRP_fnc_hint;
	} else {
		["Failed to receive item from administrator"] call EDRP_fnc_hint;
	};
};

// Spawn vehicle for player
EDRP_fnc_spawnVehicleForPlayer = {
	params [["_targetUID", "", [""]], ["_vehicleClass", "", [""]], ["_position", [0,0,0], [[]]]];
	
	if (_targetUID == "" || _vehicleClass == "") exitWith {
		["Invalid parameters"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "spawn_vehicles"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Use target player position if no position specified
	if (count _position == 0) then {
		_position = getPosATL _targetPlayer vectorAdd [5, 5, 0];
	};
	
	// Send spawn command to server
	[_vehicleClass, _position, _targetUID] remoteExec ["EDRP_fnc_adminSpawnVehicle", 2];
	
	// Log action
	private _logEntry = format ["[%1] %2 spawned %3 for %4", [daytime] call EDRP_fnc_timeToString, name player, _vehicleClass, name _targetPlayer];
	EDRP_admin_logs pushBack _logEntry;
	
	[format ["Spawned %1 for %2", _vehicleClass, name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Modify player money
EDRP_fnc_modifyPlayerMoney = {
	params [["_targetUID", "", [""]], ["_amount", 0, [0]], ["_type", "bank", [""]]];
	
	if (_targetUID == "" || _amount == 0) exitWith {
		["Invalid parameters"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "modify_money"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send money modification to target
	[_amount, _type] remoteExec ["EDRP_fnc_adminModifyMoney", _targetPlayer];
	
	// Log action
	private _actionType = if (_amount > 0) then { "added" } else { "removed" };
	private _logEntry = format ["[%1] %2 %3 $%4 %5 %6 %7", [daytime] call EDRP_fnc_timeToString, name player, _actionType, abs(_amount), if (_amount > 0) then { "to" } else { "from" }, name _targetPlayer, _type];
	EDRP_admin_logs pushBack _logEntry;
	
	[format ["%1 $%2 %3 %4's %5", if (_amount > 0) then { "Added" } else { "Removed" }, abs(_amount), if (_amount > 0) then { "to" } else { "from" }, name _targetPlayer, _type]] call EDRP_fnc_hint;
	
	true
};

// Admin modify money (executed on target)
EDRP_fnc_adminModifyMoney = {
	params [["_amount", 0, [0]], ["_type", "bank", [""]]];
	
	if (_amount == 0) exitWith {};
	
	switch (_type) do {
		case "bank": {
			EDRP_player_bank = EDRP_player_bank + _amount;
			if (EDRP_player_bank < 0) then { EDRP_player_bank = 0; };
		};
		case "cash": {
			EDRP_player_cash = EDRP_player_cash + _amount;
			if (EDRP_player_cash < 0) then { EDRP_player_cash = 0; };
		};
	};
	
	// Update display
	[] call EDRP_fnc_updatePlayerHUD;
	
	private _actionText = if (_amount > 0) then { "added" } else { "removed" };
	[format ["Administrator %1 $%2 %3 your %4", _actionText, abs(_amount), if (_amount > 0) then { "to" } else { "from" }, _type], "info"] call EDRP_fnc_hint;
};

// View player inventory
EDRP_fnc_viewPlayerInventory = {
	params [["_targetUID", "", [""]]];
	
	if (_targetUID == "") exitWith {
		["No player selected"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "view_player_info"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Request player inventory from target
	[getPlayerUID player] remoteExec ["EDRP_fnc_sendInventoryToAdmin", _targetPlayer];
	
	[format ["Requesting inventory from %1...", name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Send inventory to admin (executed on target)
EDRP_fnc_sendInventoryToAdmin = {
	params [["_adminUID", "", [""]]];
	
	if (_adminUID == "") exitWith {};
	
	private _adminPlayer = [_adminUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _adminPlayer) exitWith {};
	
	// Send inventory data to admin
	[name player, EDRP_player_inventory, EDRP_player_cash, EDRP_player_bank] remoteExec ["EDRP_fnc_displayPlayerInventory", _adminPlayer];
};

// Display player inventory (executed on admin)
EDRP_fnc_displayPlayerInventory = {
	params [["_playerName", "", [""]], ["_inventory", [], [[]]], ["_cash", 0, [0]], ["_bank", 0, [0]]];
	
	if (_playerName == "") exitWith {};
	
	// Create inventory display dialog
	createDialog "EDRP_AdminInventoryDialog";
	
	private _display = findDisplay 51000;
	if (isNull _display) exitWith {};
	
	// Update player info
	private _playerInfoCtrl = _display displayCtrl 51001;
	private _playerInfo = format [
		"Player: %1\nCash: $%2\nBank: $%3\nItems: %4",
		_playerName,
		[_cash] call EDRP_fnc_numberText,
		[_bank] call EDRP_fnc_numberText,
		count _inventory
	];
	_playerInfoCtrl ctrlSetText _playerInfo;
	
	// Update inventory list
	private _inventoryList = _display displayCtrl 51002;
	lbClear _inventoryList;
	
	{
		_x params ["_item", "_quantity"];
		private _itemName = [_item] call EDRP_fnc_getItemName;
		private _entry = format ["%1 x%2", _itemName, _quantity];
		
		_inventoryList lbAdd _entry;
		_inventoryList lbSetData [_forEachIndex, _item];
		_inventoryList lbSetValue [_forEachIndex, _quantity];
	} forEach _inventory;
};

// Heal player
EDRP_fnc_healPlayer = {
	params [["_targetUID", "", [""]]];
	
	if (_targetUID == "") exitWith {
		["No player selected"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "teleport_others"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send heal command to target
	[] remoteExec ["EDRP_fnc_adminHeal", _targetPlayer];
	
	// Log action
	private _logEntry = format ["[%1] %2 healed %3", [daytime] call EDRP_fnc_timeToString, name player, name _targetPlayer];
	EDRP_admin_logs pushBack _logEntry;
	
	[format ["Healed %1", name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Admin heal (executed on target)
EDRP_fnc_adminHeal = {
	player setDamage 0;
	player setVariable ["unconscious", false, true];
	player setVariable ["restrained", false, true];
	
	["You have been healed by an administrator"] call EDRP_fnc_hint;
};

// Revive player
EDRP_fnc_revivePlayer = {
	params [["_targetUID", "", [""]]];
	
	if (_targetUID == "") exitWith {
		["No player selected"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "teleport_others"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send revive command to target
	[] remoteExec ["EDRP_fnc_adminRevive", _targetPlayer];
	
	// Log action
	private _logEntry = format ["[%1] %2 revived %3", [daytime] call EDRP_fnc_timeToString, name player, name _targetPlayer];
	EDRP_admin_logs pushBack _logEntry;
	
	[format ["Revived %1", name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Admin revive (executed on target)
EDRP_fnc_adminRevive = {
	player setDamage 0;
	player setVariable ["unconscious", false, true];
	player setVariable ["restrained", false, true];
	player setVariable ["dead", false, true];
	
	// Close death screen if open
	closeDialog 0;
	
	["You have been revived by an administrator"] call EDRP_fnc_hint;
};

// Freeze player
EDRP_fnc_freezePlayer = {
	params [["_targetUID", "", [""]], ["_freeze", true, [true]]];
	
	if (_targetUID == "") exitWith {
		["No player selected"] call EDRP_fnc_hint;
		false
	};
	
	if !([EDRP_admin_level, "teleport_others"] call EDRP_fnc_hasAdminPermission) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send freeze command to target
	[_freeze] remoteExec ["EDRP_fnc_adminFreeze", _targetPlayer];
	
	// Log action
	private _action = if (_freeze) then { "froze" } else { "unfroze" };
	private _logEntry = format ["[%1] %2 %3 %4", [daytime] call EDRP_fnc_timeToString, name player, _action, name _targetPlayer];
	EDRP_admin_logs pushBack _logEntry;
	
	[format ["%1 %2", if (_freeze) then { "Froze" } else { "Unfroze" }, name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Admin freeze (executed on target)
EDRP_fnc_adminFreeze = {
	params [["_freeze", true, [true]]];
	
	if (_freeze) then {
		player setVariable ["admin_frozen", true, true];
		player enableSimulation false;
		["You have been frozen by an administrator"] call EDRP_fnc_hint;
	} else {
		player setVariable ["admin_frozen", false, true];
		player enableSimulation true;
		["You have been unfrozen by an administrator"] call EDRP_fnc_hint;
	};
};

// Send admin message to player
EDRP_fnc_sendAdminMessage = {
	params [["_targetUID", "", [""]], ["_message", "", [""]]];
	
	if (_targetUID == "" || _message == "") exitWith {
		["Invalid parameters"] call EDRP_fnc_hint;
		false
	};
	
	if (EDRP_admin_level == 0) exitWith {
		["Insufficient permissions"] call EDRP_fnc_hint;
		false
	};
	
	private _targetPlayer = [_targetUID] call EDRP_fnc_getPlayerByUID;
	if (isNull _targetPlayer) exitWith {
		["Player not found"] call EDRP_fnc_hint;
		false
	};
	
	// Send message to target
	[name player, _message] remoteExec ["EDRP_fnc_receiveAdminMessage", _targetPlayer];
	
	// Log action
	private _logEntry = format ["[%1] %2 sent message to %3: %4", [daytime] call EDRP_fnc_timeToString, name player, name _targetPlayer, _message];
	EDRP_admin_logs pushBack _logEntry;
	
	[format ["Message sent to %1", name _targetPlayer]] call EDRP_fnc_hint;
	
	true
};

// Receive admin message (executed on target)
EDRP_fnc_receiveAdminMessage = {
	params [["_adminName", "", [""]], ["_message", "", [""]]];
	
	if (_adminName == "" || _message == "") exitWith {};
	
	// Display admin message
	private _fullMessage = format ["ADMIN MESSAGE from %1:\n\n%2", _adminName, _message];
	[_fullMessage, "Admin Message", false, true] call EDRP_fnc_messageBox;
};

// Get player by UID
EDRP_fnc_getPlayerByUID = {
	params [["_uid", "", [""]]];
	
	if (_uid == "") exitWith { objNull };
	
	{
		if (getPlayerUID _x == _uid) exitWith { _x };
	} forEach allPlayers;
	
	objNull
};
