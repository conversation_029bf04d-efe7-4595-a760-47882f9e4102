//  File: fn_initStats
//	Description: initializes player stats

private["_lastPos","_currentPos","_distanceStep","_startingDeaths","_startingTeleports"];
waitUntil {position player distance getMarkerPos("debug_island_marker") > 600};
[] spawn{
	while{true} do {
		uiSleep 300;
		[9] call EDEN_fnc_ClupdatePartial;
	};
};

waitUntil{!isNil "EDEN_stats_deaths"};
while{true} do {
	_startingDeaths =  EDEN_stats_deaths;
	_startingTeleports = EDEN_stats_teleports;
	_lastPos = [(getPos player select 0),(getPos player select 1)];
	uiSleep 60;
	_currentPos = [(getPos player select 0),(getPos player select 1)];
	_distanceStep = _lastPos distance _currentPos;
	if((_startingDeaths == EDEN_stats_deaths) && (_startingTeleports == EDEN_stats_teleports)) then {
		if(_distanceStep <= 6000) then {
			if(player != vehicle player) then {
				EDEN_stats_distanceVehicle = EDEN_stats_distanceVehicle + round(_distanceStep);
			} else {
				if(_distanceStep <= 500) then {
					EDEN_stats_distanceFoot = EDEN_stats_distanceFoot + round(_distanceStep);
				};
			};
		};
	};
	switch (playerSide) do
	{
		case west: {
			EDEN_stats_playtime_cop = EDEN_stats_playtime_cop + 1;
		};
		case civilian: {
			EDEN_stats_playtime_civ = EDEN_stats_playtime_civ + 1;
		};
		case independent: {
			if !(eden_newsTeam) then {
				EDEN_stats_playtime_med = EDEN_stats_playtime_med + 1;
			} else {
				EDEN_stats_playtime_civ = EDEN_stats_playtime_civ + 1;
			};
		};
	};
};
