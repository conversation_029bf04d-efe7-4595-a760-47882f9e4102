// File: fn_captureBlackMarket.sqf
// Author: <PERSON> "tkc<PERSON><PERSON>" Schultz
private _sign = param [0,obj<PERSON><PERSON>,[obj<PERSON><PERSON>]];
private _market = param [3,"",[""]];

if (isNull _sign || _market isEqualTo "") exitWith {};
if !((typeOf _sign) isEqualTo "Land_SignM_forRent_F") exitWith {};
if ((count eden_gang_data) isEqualTo 0) exitWith {hint "You must be in a gang to capture the Black Market!";};
if ((_sign getVariable ["owners",-1]) isEqualTo (eden_gang_data select 0)) exitWith {hint "Your gang already owns the Black Market!";};
if (eden_action_inUse) exitWith {hint "You are already performing another action!"};

eden_action_inUse = true;

private _name = switch (_market) do {
	case "bmOne": {"Cocaine"};
	case "bmTwo": {"Weed"};
	case "bmThree": {"Heroin"};
	case "bmFour": {"Mushroom"};
};

private _ownerID = _sign getVariable ["owners",-1];
if (!(_ownerID isEqualTo -1) && ((_sign getVariable ["notify",0]) <= serverTime)) then {
	private _onlineMembers = ([_ownerID] call EDEN_fnc_getOnlineMembers);
	[[3,format["<t color='#ffa700'><t size='1.8'><t align='center'>Gang Notification<br/><t color='#ffffff'><t align='center'><t size='1.1'>Another gang is capturing your Black Market used for %1 processing!",_name]],"EDEN_fnc_broadcast",_onlineMembers,false] spawn EDEN_fnc_MP;
	_sign setVariable ["notify",(serverTime + 300),true];
};

_market setMarkerType "mil_warning";
private _playerGangName = eden_gang_data select 1;

disableSerialization;
private _title = "Capturing Black Market";
5 cutRsc ["life_progress","PLAIN DOWN"];
private _ui = uiNamespace getVariable "life_progress";
private _progressBar = _ui displayCtrl 38201;
private _titleText = _ui displayCtrl 38202;
_titleText ctrlSetText format["%2 (1%1)...","%",_title];
_progressBar progressSetPosition 0.01;
private _cP = 0.01;
private _cpRate = 0.01;

hint "You need to remain within 12m of the sign to continue capturing.";

private _exit = false;
while {true} do {
	uiSleep 0.26;
	if(isNull _ui) then {
		5 cutRsc ["life_progress","PLAIN DOWN"];
		_ui = uiNamespace getVariable "life_progress";
		_progressBar = _ui displayCtrl 38201;
		_titleText = _ui displayCtrl 38202;
	};
	_cP = _cP + _cpRate;
	_progressBar progressSetPosition _cP;
	_titleText ctrlSetText format["%3 (%1%2)...",round(_cP * 100),"%",_title];
	if (_cP >= 1) exitWith {_market setMarkerType "mil_marker";};
	if !(alive player) exitWith {_exit = true; _market setMarkerType "mil_marker";};
	if !(isNull objectParent player) exitWith {_exit = true; _market setMarkerType "mil_marker";};
	if (player distance _sign > 12) exitWith {_exit = true; hint "You must stay near the sign to capture!"; _market setMarkerType "mil_marker";};
};

//Kill the UI display and check for various states
5 cutText ["","PLAIN DOWN"];
player playMoveNow "stop";
if (_exit) exitWith {eden_action_inUse = false;};

uiSleep round(random(5));
uiSleep round(random(5));

if !(_ownerID isEqualTo -1) then {
	private _onlineMembers = ([_ownerID] call EDEN_fnc_getOnlineMembers);
	[[3,format["<t color='#ffa700'><t size='1.8'><t align='center'>Gang Notification<br/><t color='#ffffff'><t align='center'><t size='1.1'>Another gang has captured your Black Market used for %1 processing!",_name]],"EDEN_fnc_broadcast",_onlineMembers,false] spawn EDEN_fnc_MP;
};

_sign setVariable ["owners",(eden_gang_data select 0),true];
_sign setVariable ["notify",0,true];
eden_action_inUse = false;
titleText [format["Your gang now owns this Black Market and can use it for processing %1.",_name],"PLAIN DOWN"];
_market setMarkerType "mil_marker";
_market setMarkerText format ["Black Market - %1 (%2)", _name, _playerGangName];
[[3,format["The %1 Black Market has been captured by the %2!",_name,_playerGangName],false,[]],"EDEN_fnc_broadcast",-2,false] spawn EDEN_fnc_MP;
