/*
	EdenRP Altis Life - Housing System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Core housing and property management system
	Version: 1.0.0
*/

// Initialize housing system
EDRP_fnc_initHousingSystem = {
	// Housing state variables
	EDRP_owned_houses = [];
	EDRP_house_keys = [];
	EDRP_house_transaction = false;
	EDRP_house_markers = [];
	
	// Housing statistics
	EDRP_housing_stats = createHashMapFromArray [
		["houses_owned", 0],
		["total_spent", 0],
		["storage_upgrades", 0],
		["security_upgrades", 0],
		["garage_upgrades", 0]
	];
	
	// Load housing configuration
	[] call EDRP_fnc_loadHousingConfig;
	
	["Housing system initialized"] call EDRP_fnc_logInfo;
};

// Load housing configuration
EDRP_fnc_loadHousingConfig = {
	// House types and pricing (adapted from Olympus)
	EDRP_house_types = [
		["Land_i_House_Small_01_V1_F", "Small House", 75000, 2, 100, 100, 0],
		["Land_i_House_Small_01_V2_F", "Small House (Variant)", 80000, 2, 100, 100, 0],
		["Land_i_House_Small_02_V1_F", "Medium House", 125000, 3, 200, 150, 1],
		["Land_i_House_Small_02_V2_F", "Medium House (Variant)", 130000, 3, 200, 150, 1],
		["Land_i_House_Big_01_V1_F", "Large House", 200000, 4, 350, 250, 2],
		["Land_i_House_Big_01_V2_F", "Large House (Variant)", 210000, 4, 350, 250, 2],
		["Land_i_House_Big_02_V1_F", "Mansion", 350000, 6, 500, 400, 3],
		["Land_i_House_Big_02_V2_F", "Mansion (Variant)", 375000, 6, 500, 400, 3],
		["Land_i_Shop_01_V1_F", "Commercial Property", 500000, 4, 700, 300, 2],
		["Land_i_Shop_02_V1_F", "Large Commercial", 750000, 6, 1000, 500, 4]
	];
	
	// Upgrade costs and benefits
	EDRP_house_upgrades = createHashMapFromArray [
		["storage", [0.15, 700, "Increases virtual storage capacity by 700 units"]],
		["physical_storage", [200000, 200, "Increases physical storage capacity by 200 units"]],
		["security", [100000, 1, "Adds advanced security system with alarms"]],
		["garage", [150000, 1, "Adds one garage space for vehicle storage"]],
		["lighting", [25000, 1, "Adds interior lighting control system"]],
		["furniture", [50000, 1, "Adds furniture and decoration options"]]
	];
	
	// Property tax rates
	EDRP_property_tax_rate = 0.02; // 2% of house value per month
	
	// House expiration period
	EDRP_house_expiration_days = 45; // Houses expire after 45 days of inactivity
	
	// Maximum houses per player
	EDRP_max_houses_per_player = 3;
};

// Get house configuration
EDRP_fnc_getHouseConfig = {
	params [["_houseType", "", [""]]];
	
	{
		if ((_x select 0) == _houseType) exitWith {
			_x
		};
	} forEach EDRP_house_types;
	
	[]
};

// Check if house is available for purchase
EDRP_fnc_isHouseAvailable = {
	params [["_house", objNull, [objNull]]];
	
	if (isNull _house || !(_house isKindOf "House_F")) exitWith { false };
	
	// Check if house is already owned
	private _owner = _house getVariable ["house_owner", []];
	if (count _owner > 0) exitWith { false };
	
	// Check if house is for sale
	private _forSale = _house getVariable ["for_sale", ""];
	if (_forSale != "") exitWith { false };
	
	// Check if house is restricted
	if (_house getVariable ["restricted_house", false]) exitWith { false };
	
	true
};

// Buy house
EDRP_fnc_buyHouse = {
	params [["_house", objNull, [objNull]]];
	
	if (isNull _house || !(_house isKindOf "House_F")) exitWith {
		["Invalid house selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check if transaction is in progress
	if (EDRP_house_transaction) exitWith {
		["You currently have an active transaction, please wait"] call EDRP_fnc_hint;
		false
	};
	
	// Check if house is available
	if !([_house] call EDRP_fnc_isHouseAvailable) exitWith {
		["This house is not available for purchase"] call EDRP_fnc_hint;
		false
	};
	
	// Check house limit
	if (count EDRP_owned_houses >= EDRP_max_houses_per_player) exitWith {
		[format ["You can only own %1 houses", EDRP_max_houses_per_player]] call EDRP_fnc_hint;
		false
	};
	
	// Get house configuration
	private _houseConfig = [typeOf _house] call EDRP_fnc_getHouseConfig;
	if (count _houseConfig == 0) exitWith {
		["House type not supported"] call EDRP_fnc_hint;
		false
	};
	
	_houseConfig params ["_type", "_name", "_price", "_doors", "_virtualStorage", "_physicalStorage", "_garageSpaces"];
	
	// Check if player has enough money
	private _totalCost = _price;
	if (EDRP_player_bank < _totalCost) exitWith {
		[format ["You need $%1 in your bank to purchase this house", [_totalCost] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
		false
	};
	
	// Confirm purchase
	private _message = format [
		"Purchase %1 for $%2?\n\nFeatures:\n- %3 virtual storage\n- %4 physical storage\n- %5 garage spaces",
		_name,
		[_price] call EDRP_fnc_numberText,
		_virtualStorage,
		_physicalStorage,
		_garageSpaces
	];
	
	if ([_message, "Purchase House", true, true] call EDRP_fnc_messageBox) then {
		// Start transaction
		EDRP_house_transaction = true;
		
		// Deduct money
		EDRP_player_bank = EDRP_player_bank - _totalCost;
		
		// Send purchase request to server
		[player, getPlayerUID player, _house, _price] remoteExec ["EDRP_fnc_addHouse", 2];
		
		["Processing house purchase..."] call EDRP_fnc_hint;
		
		true
	} else {
		false
	};
};

// House ownership response (from server)
EDRP_fnc_houseOwnership = {
	params [
		["_house", objNull, [objNull]],
		["_mode", 1, [0]],
		["_uid", "", [""]],
		["_price", 0, [0]]
	];
	
	if (isNull _house) exitWith {};
	
	switch (_mode) do {
		case 1: { // House purchased successfully
			// Set house variables
			_house setVariable ["house_owner", [getPlayerUID player, name player], true];
			_house setVariable ["locked", true, true];
			_house setVariable ["for_sale", "", true];
			_house setVariable ["virtual_inventory", [[], 0], true];
			_house setVariable ["physical_inventory", [[], 0], true];
			_house setVariable ["uid", round(random 99999), true];
			
			// Get house config
			private _houseConfig = [typeOf _house] call EDRP_fnc_getHouseConfig;
			_houseConfig params ["_type", "_name", "_basePrice", "_doors", "_virtualStorage", "_physicalStorage", "_garageSpaces"];
			
			// Set storage capacities
			_house setVariable ["virtual_storage_capacity", _virtualStorage, true];
			_house setVariable ["physical_storage_capacity", _physicalStorage, true];
			_house setVariable ["garage_spaces", _garageSpaces, true];
			_house setVariable ["upgrade_level", 1, true];
			
			// Add to owned houses
			EDRP_owned_houses pushBack [str(getPosATL _house), []];
			
			// Create house marker
			private _marker = createMarkerLocal [format["house_%1", _house getVariable "uid"], getPosATL _house];
			_marker setMarkerText _name;
			_marker setMarkerColor "ColorBlue";
			_marker setMarkerType "loc_Lighthouse";
			EDRP_house_markers pushBack _marker;
			
			// Lock all doors
			private _numOfDoors = getNumber(configFile >> "CfgVehicles" >> (typeOf _house) >> "numberOfDoors");
			for "_i" from 1 to _numOfDoors do {
				_house setVariable [format["bis_disabled_Door_%1", _i], 1, true];
			};
			
			// Update statistics
			EDRP_housing_stats set ["houses_owned", (EDRP_housing_stats get "houses_owned") + 1];
			EDRP_housing_stats set ["total_spent", (EDRP_housing_stats get "total_spent") + _price];
			
			// Show success message
			["House purchased successfully! You now have the keys", "success"] call EDRP_fnc_hint;
			
			// Clear transaction flag
			EDRP_house_transaction = false;
		};
		
		case 2: { // House sold successfully
			// Remove house variables
			_house setVariable ["house_owner", nil, true];
			_house setVariable ["locked", false, true];
			_house setVariable ["virtual_inventory", nil, true];
			_house setVariable ["physical_inventory", nil, true];
			_house setVariable ["virtual_storage_capacity", 0, true];
			_house setVariable ["physical_storage_capacity", 0, true];
			_house setVariable ["garage_spaces", 0, true];
			
			// Remove marker
			deleteMarkerLocal format["house_%1", _house getVariable "uid"];
			_house setVariable ["uid", nil, true];
			
			// Remove from owned houses
			private _housePos = str(getPosATL _house);
			{
				if ((_x select 0) == _housePos) exitWith {
					EDRP_owned_houses deleteAt _forEachIndex;
				};
			} forEach EDRP_owned_houses;
			
			// Calculate sale price (50% of original + upgrades)
			private _houseConfig = [typeOf _house] call EDRP_fnc_getHouseConfig;
			private _basePrice = _houseConfig select 2;
			private _salePrice = round(_basePrice / 2);
			
			// Add upgrade value
			private _upgradeValue = [_house] call EDRP_fnc_calculateUpgradeValue;
			_salePrice = _salePrice + round(_upgradeValue * 0.25); // 25% of upgrade value
			
			// Add money to player
			EDRP_player_bank = EDRP_player_bank + _salePrice;
			
			// Update statistics
			EDRP_housing_stats set ["houses_owned", (EDRP_housing_stats get "houses_owned") - 1];
			
			// Show success message
			[format ["House sold for $%1", [_salePrice] call EDRP_fnc_numberText], "success"] call EDRP_fnc_hint;
		};
		
		case 3: { // Purchase failed
			["House purchase failed - please try again"] call EDRP_fnc_hint;
			EDRP_house_transaction = false;
		};
		
		case 4: { // Bought from another player
			// Similar to case 1 but with different pricing
			[_house, 1, _uid, _price] call EDRP_fnc_houseOwnership;
		};
		
		case 5: { // Price mismatch
			["Purchase denied - price has changed. Please try again"] call EDRP_fnc_hint;
			EDRP_house_transaction = false;
		};
	};
};

// Sell house
EDRP_fnc_sellHouse = {
	params [["_house", objNull, [objNull]]];
	
	if (isNull _house || !(_house isKindOf "House_F")) exitWith {
		["Invalid house selected"] call EDRP_fnc_hint;
		false
	};
	
	// Check ownership
	private _owner = _house getVariable ["house_owner", []];
	if (count _owner == 0 || (_owner select 0) != getPlayerUID player) exitWith {
		["You don't own this house"] call EDRP_fnc_hint;
		false
	};
	
	// Calculate sale price
	private _houseConfig = [typeOf _house] call EDRP_fnc_getHouseConfig;
	private _basePrice = _houseConfig select 2;
	private _salePrice = round(_basePrice / 2);
	
	// Add upgrade value
	private _upgradeValue = [_house] call EDRP_fnc_calculateUpgradeValue;
	_salePrice = _salePrice + round(_upgradeValue * 0.25);
	
	// Confirm sale
	private _message = format [
		"Sell this house for $%1?\n\nThis includes 50%% of the original price plus 25%% of upgrade value.\n\nAll items in storage will be lost!",
		[_salePrice] call EDRP_fnc_numberText
	];
	
	if ([_message, "Sell House", true, true] call EDRP_fnc_messageBox) then {
		// Send sell request to server
		[_house, getPlayerUID player] remoteExec ["EDRP_fnc_sellHouse", 2];
		
		["Processing house sale..."] call EDRP_fnc_hint;
		
		true
	} else {
		false
	};
};

// Calculate upgrade value
EDRP_fnc_calculateUpgradeValue = {
	params [["_house", objNull, [objNull]]];
	
	if (isNull _house) exitWith { 0 };
	
	private _totalValue = 0;
	
	// Storage upgrades
	private _houseConfig = [typeOf _house] call EDRP_fnc_getHouseConfig;
	private _baseStorage = _houseConfig select 4;
	private _currentStorage = _house getVariable ["virtual_storage_capacity", _baseStorage];
	private _storageUpgrades = (_currentStorage - _baseStorage) / 700;
	_totalValue = _totalValue + (_storageUpgrades * (_houseConfig select 2) * 0.15);
	
	// Physical storage upgrades
	private _basePhysical = _houseConfig select 5;
	private _currentPhysical = _house getVariable ["physical_storage_capacity", _basePhysical];
	private _physicalUpgrades = (_currentPhysical - _basePhysical) / 200;
	_totalValue = _totalValue + (_physicalUpgrades * 200000);
	
	// Other upgrades
	if (_house getVariable ["security_system", false]) then {
		_totalValue = _totalValue + 100000;
	};
	
	if (_house getVariable ["lighting_system", false]) then {
		_totalValue = _totalValue + 25000;
	};
	
	if (_house getVariable ["furniture_system", false]) then {
		_totalValue = _totalValue + 50000;
	};
	
	// Garage upgrades
	private _baseGarage = _houseConfig select 6;
	private _currentGarage = _house getVariable ["garage_spaces", _baseGarage];
	private _garageUpgrades = _currentGarage - _baseGarage;
	_totalValue = _totalValue + (_garageUpgrades * 150000);
	
	_totalValue
};

// Lock/unlock house
EDRP_fnc_toggleHouseLock = {
	params [["_house", objNull, [objNull]]];
	
	if (isNull _house || !(_house isKindOf "House_F")) exitWith {
		["Invalid house"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player has access
	if !([_house] call EDRP_fnc_hasHouseAccess) exitWith {
		["You don't have access to this house"] call EDRP_fnc_hint;
		false
	};
	
	private _locked = _house getVariable ["locked", true];
	
	if (_locked) then {
		_house setVariable ["locked", false, true];
		["House storage unlocked"] call EDRP_fnc_hint;
	} else {
		_house setVariable ["locked", true, true];
		["House storage locked"] call EDRP_fnc_hint;
	};
	
	true
};

// Check house access
EDRP_fnc_hasHouseAccess = {
	params [["_house", objNull, [objNull]]];
	
	if (isNull _house) exitWith { false };
	
	// Check ownership
	private _owner = _house getVariable ["house_owner", []];
	if (count _owner > 0 && (_owner select 0) == getPlayerUID player) exitWith { true };
	
	// Check keys
	private _keyPlayers = _house getVariable ["keyPlayers", []];
	if (getPlayerUID player in _keyPlayers) exitWith { true };
	
	false
};

// Open house menu
EDRP_fnc_openHouseMenu = {
	params [["_house", objNull, [objNull]]];
	
	if (isNull _house || !(_house isKindOf "House_F")) exitWith {
		["Invalid house"] call EDRP_fnc_hint;
		false
	};
	
	// Check access
	if !([_house] call EDRP_fnc_hasHouseAccess) exitWith {
		["You don't have access to this house"] call EDRP_fnc_hint;
		false
	};
	
	// Store current house
	EDRP_current_house = _house;
	
	// Create house dialog
	createDialog "EDRP_HouseDialog";
	
	// Update house menu
	[] call EDRP_fnc_updateHouseMenu;
	
	true
};

// Update house menu
EDRP_fnc_updateHouseMenu = {
	private _display = findDisplay 38000;
	if (isNull _display || isNil "EDRP_current_house") exitWith {};
	
	private _house = EDRP_current_house;
	private _houseConfig = [typeOf _house] call EDRP_fnc_getHouseConfig;
	
	if (count _houseConfig == 0) exitWith {};
	
	_houseConfig params ["_type", "_name", "_price", "_doors", "_virtualStorage", "_physicalStorage", "_garageSpaces"];
	
	// Update house info
	private _houseInfoCtrl = _display displayCtrl 38001;
	private _currentVirtual = _house getVariable ["virtual_storage_capacity", _virtualStorage];
	private _currentPhysical = _house getVariable ["physical_storage_capacity", _physicalStorage];
	private _currentGarage = _house getVariable ["garage_spaces", _garageSpaces];
	
	private _houseInfo = format [
		"House: %1\nValue: $%2\nVirtual Storage: %3\nPhysical Storage: %4\nGarage Spaces: %5\nUpgrade Level: %6",
		_name,
		[_price] call EDRP_fnc_numberText,
		_currentVirtual,
		_currentPhysical,
		_currentGarage,
		_house getVariable ["upgrade_level", 1]
	];
	_houseInfoCtrl ctrlSetText _houseInfo;
	
	// Update button states based on ownership
	private _owner = _house getVariable ["house_owner", []];
	private _isOwner = (count _owner > 0 && (_owner select 0) == getPlayerUID player);
	
	(_display displayCtrl 38002) ctrlEnable _isOwner; // Upgrade button
	(_display displayCtrl 38003) ctrlEnable _isOwner; // Manage keys button
	(_display displayCtrl 38004) ctrlEnable _isOwner; // Sell button
};

// Add house actions
EDRP_fnc_addHouseActions = {
	// Buy house action
	player addAction [
		"<t color='#00FF00'>Buy House</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_buyHouse;
		},
		[],
		6,
		true,
		true,
		"",
		"cursorTarget isKindOf 'House_F' && cursorTarget distance player < 10 && [cursorTarget] call EDRP_fnc_isHouseAvailable"
	];
	
	// House menu action
	player addAction [
		"<t color='#0080FF'>House Menu</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_openHouseMenu;
		},
		[],
		5,
		true,
		true,
		"",
		"cursorTarget isKindOf 'House_F' && cursorTarget distance player < 10 && [cursorTarget] call EDRP_fnc_hasHouseAccess"
	];
	
	// Lock/unlock action
	player addAction [
		"<t color='#FFFF00'>Toggle Lock</t>",
		{
			params ["_target", "_caller", "_actionId", "_arguments"];
			[_target] call EDRP_fnc_toggleHouseLock;
		},
		[],
		4,
		true,
		true,
		"",
		"cursorTarget isKindOf 'House_F' && cursorTarget distance player < 10 && [cursorTarget] call EDRP_fnc_hasHouseAccess"
	];
};

// Initialize housing system on client
if (hasInterface) then {
	[] call EDRP_fnc_initHousingSystem;
	
	// Add house actions
	[] call EDRP_fnc_addHouseActions;
};
