// File: fn_checkMap.sqf
// Author: <PERSON> "tkc<PERSON><PERSON>" Schultz
// https://community.bistudio.com/wiki/Arma_3:_Event_Handlers/addMissionEventHandler#Map
params [
	["_mapOpen",false,[false]],
	["_mapForced",false,[false]]
];

if (player getVariable ["restrained",false]) exitWith {openMap false;};
if (player getVariable ["blindfolded",false]) exitWith {openMap false;};
if (eden_isDowned) exitWith {openMap false;};

if (_mapOpen) then {
	switch (playerSide) do {
		case west: {[] spawn EDEN_fnc_copMarkers;};
		case independent: {
			if !(eden_newsTeam) then {
				[] spawn EDEN_fnc_medicMarkers;
			} else {
				[] spawn EDEN_fnc_newsMarkers;
			};
		};
		case civilian: {[] spawn EDEN_fnc_civMarkers;};
		default {};
	};
};