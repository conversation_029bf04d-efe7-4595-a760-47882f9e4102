/*
	EdenRP Altis Life - Faction Setup
	Author: EdenRP Development Team
	Description: Handles faction-specific initialization and configuration
	Version: 1.0.0
*/

// Faction initialization function
EDRP_fnc_factionSetup = {
	diag_log "EdenRP: Starting faction setup...";
	
	// Initialize faction-specific systems based on player side
	switch (playerSide) do {
		case west: {
			call EDRP_fnc_initializePolice;
		};
		case independent: {
			call EDRP_fnc_initializeMedical;
		};
		case civilian: {
			call EDRP_fnc_initializeCivilian;
		};
	};
	
	diag_log "EdenRP: Faction setup completed";
};

// Police initialization
EDRP_fnc_initializePolice = {
	EDRP_loading_status = "<t color='#0080FF'>Initializing Police Systems...</t>";
	
	// Police rank structure
	EDRP_police_ranks = [
		["Cadet", 1, 25000],
		["Officer", 2, 30000],
		["Senior Officer", 3, 35000],
		["Corporal", 4, 40000],
		["Sergeant", 5, 45000],
		["Staff Sergeant", 6, 50000],
		["Lieutenant", 7, 55000],
		["Captain", 8, 60000],
		["Major", 9, 65000],
		["Chief", 10, 70000]
	];
	
	// Police departments
	EDRP_police_departments = [
		["Patrol", "patrol", "General patrol duties"],
		["Traffic", "traffic", "Traffic enforcement"],
		["Detective", "detective", "Criminal investigations"],
		["SWAT", "swat", "Special operations"],
		["Air Unit", "air", "Aerial support"],
		["K9 Unit", "k9", "Canine operations"]
	];
	
	// Police equipment by rank
	EDRP_police_equipment = createHashMap;
	EDRP_police_equipment set [1, ["hgun_P07_F", "16Rnd_9x21_Mag"]];
	EDRP_police_equipment set [2, ["hgun_P07_F", "SMG_02_F", "16Rnd_9x21_Mag", "30Rnd_9x21_Mag_SMG_02"]];
	EDRP_police_equipment set [3, ["hgun_P07_F", "SMG_02_F", "16Rnd_9x21_Mag", "30Rnd_9x21_Mag_SMG_02"]];
	EDRP_police_equipment set [4, ["hgun_P07_F", "arifle_MX_F", "16Rnd_9x21_Mag", "30Rnd_65x39_caseless_mag"]];
	EDRP_police_equipment set [5, ["hgun_P07_F", "arifle_MX_F", "16Rnd_9x21_Mag", "30Rnd_65x39_caseless_mag"]];
	
	// Police vehicles by rank
	EDRP_police_vehicles = createHashMap;
	EDRP_police_vehicles set [1, ["C_Offroad_01_F"]];
	EDRP_police_vehicles set [2, ["C_Offroad_01_F", "C_SUV_01_F"]];
	EDRP_police_vehicles set [3, ["C_Offroad_01_F", "C_SUV_01_F", "B_MRAP_01_F"]];
	EDRP_police_vehicles set [4, ["C_Offroad_01_F", "C_SUV_01_F", "B_MRAP_01_F", "I_MRAP_03_F"]];
	EDRP_police_vehicles set [5, ["C_Offroad_01_F", "C_SUV_01_F", "B_MRAP_01_F", "I_MRAP_03_F", "B_Heli_Light_01_F"]];
	
	// Police spawn locations
	EDRP_police_spawns = [
		["Kavala Police Station", [3560.26, 13724.2, 0.00143909], "kavala"],
		["Pyrgos Police Station", [14677.9, 16678.5, 0.00143909], "pyrgos"],
		["Athira Police Station", [14251.8, 18803.5, 0.00143909], "athira"],
		["Sofia Police Station", [25327.3, 21373.8, 0.00143909], "sofia"],
		["Air HQ", [16049.5, 16952.9, 0.00143909], "air_hq"]
	];
	
	// Initialize police-specific functions
	call EDRP_fnc_initPoliceActions;
	call EDRP_fnc_initPoliceVehicles;
	call EDRP_fnc_initPoliceEquipment;
	
	// Setup police radio channels
	call EDRP_fnc_setupPoliceRadio;
	
	// Load police configuration
	call EDRP_fnc_loadPoliceConfig;
	
	diag_log "EdenRP: Police faction initialized";
};

// Medical initialization
EDRP_fnc_initializeMedical = {
	EDRP_loading_status = "<t color='#FF8000'>Initializing Medical Systems...</t>";
	
	// Medical rank structure
	EDRP_medical_ranks = [
		["EMT", 1, 20000],
		["Paramedic", 2, 25000],
		["Advanced Paramedic", 3, 30000],
		["Field Supervisor", 4, 35000],
		["Shift Supervisor", 5, 40000],
		["Training Officer", 6, 45000],
		["Lieutenant", 7, 50000],
		["Captain", 8, 55000],
		["Deputy Chief", 9, 60000],
		["Chief of Medicine", 10, 65000]
	];
	
	// Medical certifications
	EDRP_medical_certifications = [
		["EMT", "emt", "Basic emergency medical technician"],
		["Paramedic", "paramedic", "Advanced life support"],
		["Flight Medic", "flight", "Helicopter emergency medical services"],
		["Trauma Specialist", "trauma", "Advanced trauma life support"],
		["Instructor", "instructor", "Medical training and education"]
	];
	
	// Medical equipment by rank
	EDRP_medical_equipment = createHashMap;
	EDRP_medical_equipment set [1, ["Medikit", "FirstAidKit"]];
	EDRP_medical_equipment set [2, ["Medikit", "FirstAidKit", "ToolKit"]];
	EDRP_medical_equipment set [3, ["Medikit", "FirstAidKit", "ToolKit", "hgun_P07_F"]];
	
	// Medical vehicles by rank
	EDRP_medical_vehicles = createHashMap;
	EDRP_medical_vehicles set [1, ["C_Van_01_box_F"]];
	EDRP_medical_vehicles set [2, ["C_Van_01_box_F", "C_SUV_01_F"]];
	EDRP_medical_vehicles set [3, ["C_Van_01_box_F", "C_SUV_01_F", "B_Heli_Light_01_F"]];
	
	// Medical spawn locations
	EDRP_medical_spawns = [
		["Kavala Hospital", [3529.98, 13650.7, 0.00143909], "kavala"],
		["Pyrgos Hospital", [14677.2, 16759.8, 0.00143909], "pyrgos"],
		["Athira Hospital", [14251.1, 18803.9, 0.00143909], "athira"],
		["Sofia Hospital", [25327.8, 21373.2, 0.00143909], "sofia"],
		["Air Rescue", [16049.1, 16952.3, 0.00143909], "air_rescue"]
	];
	
	// Initialize medical-specific functions
	call EDRP_fnc_initMedicalActions;
	call EDRP_fnc_initMedicalVehicles;
	call EDRP_fnc_initMedicalEquipment;
	
	// Setup medical radio channels
	call EDRP_fnc_setupMedicalRadio;
	
	// Load medical configuration
	call EDRP_fnc_loadMedicalConfig;
	
	diag_log "EdenRP: Medical faction initialized";
};

// Civilian initialization
EDRP_fnc_initializeCivilian = {
	EDRP_loading_status = "<t color='#00FF00'>Initializing Civilian Systems...</t>";
	
	// Civilian occupations
	EDRP_civilian_occupations = [
		["Unemployed", "unemployed", "No current occupation"],
		["Farmer", "farmer", "Agricultural work"],
		["Miner", "miner", "Resource extraction"],
		["Fisherman", "fisherman", "Commercial fishing"],
		["Trucker", "trucker", "Cargo transportation"],
		["Mechanic", "mechanic", "Vehicle repair services"],
		["Taxi Driver", "taxi", "Passenger transportation"],
		["Real Estate", "realtor", "Property sales and management"],
		["Business Owner", "business", "Commercial enterprise"],
		["Freelancer", "freelance", "Independent contractor"]
	];
	
	// Civilian skill categories
	EDRP_civilian_skills = [
		["Farming", "farming", "Agricultural expertise"],
		["Mining", "mining", "Resource extraction skills"],
		["Fishing", "fishing", "Commercial fishing abilities"],
		["Trucking", "trucking", "Transportation expertise"],
		["Mechanics", "mechanics", "Vehicle repair skills"],
		["Business", "business", "Commercial acumen"],
		["Crafting", "crafting", "Item creation abilities"],
		["Survival", "survival", "Wilderness survival skills"]
	];
	
	// Civilian spawn locations
	EDRP_civilian_spawns = [
		["Kavala", [3560.26, 13724.2, 0.00143909], "kavala"],
		["Pyrgos", [14677.9, 16678.5, 0.00143909], "pyrgos"],
		["Athira", [14251.8, 18803.5, 0.00143909], "athira"],
		["Sofia", [25327.3, 21373.8, 0.00143909], "sofia"],
		["Zaros", [8866.91, 18244.1, 0.00143909], "zaros"],
		["Paros", [23398.4, 19870.3, 0.00143909], "paros"],
		["Neochori", [13414.4, 14638.5, 0.00143909], "neochori"]
	];
	
	// Initialize civilian-specific functions
	call EDRP_fnc_initCivilianJobs;
	call EDRP_fnc_initCivilianVehicles;
	call EDRP_fnc_initCivilianShops;
	call EDRP_fnc_initCivilianHousing;
	
	// Initialize skill system
	call EDRP_fnc_initSkillSystem;
	
	// Load civilian configuration
	call EDRP_fnc_loadCivilianConfig;
	
	diag_log "EdenRP: Civilian faction initialized";
};

// Initialize survival system
EDRP_fnc_initializeSurvival = {
	// Survival settings
	EDRP_survival_enabled = true;
	EDRP_survival_hunger_rate = 0.1; // Per minute
	EDRP_survival_thirst_rate = 0.15; // Per minute
	EDRP_survival_fatigue_rate = 0.05; // Per minute
	
	// Survival thresholds
	EDRP_survival_hunger_warning = 25;
	EDRP_survival_thirst_warning = 25;
	EDRP_survival_fatigue_warning = 75;
	
	// Survival effects
	EDRP_survival_hunger_effects = true;
	EDRP_survival_thirst_effects = true;
	EDRP_survival_fatigue_effects = true;
	
	// Start survival monitoring
	[] spawn EDRP_fnc_survivalLoop;
	
	diag_log "EdenRP: Survival system initialized";
};

// Get faction rank name
EDRP_fnc_getFactionRankName = {
	params ["_faction", "_rank"];
	
	private _rankName = "Unknown";
	
	switch (_faction) do {
		case "police": {
			if (_rank > 0 && _rank <= count EDRP_police_ranks) then {
				_rankName = (EDRP_police_ranks select (_rank - 1)) select 0;
			};
		};
		case "medical": {
			if (_rank > 0 && _rank <= count EDRP_medical_ranks) then {
				_rankName = (EDRP_medical_ranks select (_rank - 1)) select 0;
			};
		};
	};
	
	_rankName
};

// Get faction salary
EDRP_fnc_getFactionSalary = {
	params ["_faction", "_rank"];
	
	private _salary = 0;
	
	switch (_faction) do {
		case "police": {
			if (_rank > 0 && _rank <= count EDRP_police_ranks) then {
				_salary = (EDRP_police_ranks select (_rank - 1)) select 2;
			};
		};
		case "medical": {
			if (_rank > 0 && _rank <= count EDRP_medical_ranks) then {
				_salary = (EDRP_medical_ranks select (_rank - 1)) select 2;
			};
		};
	};
	
	_salary
};

// Check faction access
EDRP_fnc_checkFactionAccess = {
	params ["_faction", "_rank", "_item"];
	
	private _hasAccess = false;
	
	switch (_faction) do {
		case "police": {
			private _equipment = EDRP_police_equipment getOrDefault [_rank, []];
			_hasAccess = _item in _equipment;
		};
		case "medical": {
			private _equipment = EDRP_medical_equipment getOrDefault [_rank, []];
			_hasAccess = _item in _equipment;
		};
	};
	
	_hasAccess
};

diag_log "EdenRP: Faction setup functions loaded";
