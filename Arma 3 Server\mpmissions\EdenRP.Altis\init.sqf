/*
	EdenRP Altis Life - Main Initialization Script
	Author: EdenRP Development Team
	Description: Main initialization script for EdenRP Altis Life server
	Version: 1.0.0
*/

// Disable saving and auto-saving
enableSaving [false, false];

// Initialize global variables
EDRP_server_isReady = false;
EDRP_client_isReady = false;
EDRP_mission_started = false;

// Server-side initialization
if (isServer) then {
	// Initialize server variables
	EDRP_server_uptime = time;
	EDRP_server_restart_time = 4 * 60 * 60; // 4 hours in seconds
	EDRP_server_population = 0;
	EDRP_server_economy_multiplier = 1.0;
	
	// Initialize market system
	EDRP_market_prices = [];
	EDRP_market_last_update = time;
	EDRP_market_update_interval = 300; // 5 minutes
	
	// Initialize gang system
	EDRP_gangs_active = [];
	EDRP_gangs_territories = [];
	EDRP_gangs_wars = [];
	
	// Initialize housing system
	EDRP_houses_owned = [];
	EDRP_houses_for_sale = [];
	EDRP_houses_rented = [];
	
	// Initialize vehicle system
	EDRP_vehicles_spawned = [];
	EDRP_vehicles_impounded = [];
	EDRP_vehicles_stolen = [];
	
	// Initialize event system
	EDRP_events_active = [];
	EDRP_events_scheduled = [];
	EDRP_events_completed = [];
	
	// Initialize wanted system
	EDRP_wanted_list = [];
	EDRP_bounty_list = [];
	
	// Initialize admin system
	EDRP_admin_list = [];
	EDRP_moderator_list = [];
	EDRP_support_list = [];
	
	// Initialize logging system
	EDRP_log_actions = true;
	EDRP_log_chat = true;
	EDRP_log_economy = true;
	EDRP_log_admin = true;
	
	// Start server monitoring
	[] spawn {
		while {true} do {
			sleep 60; // Check every minute
			EDRP_server_population = count allPlayers;
			
			// Update market prices every 5 minutes
			if (time - EDRP_market_last_update > EDRP_market_update_interval) then {
				[] call EDRP_fnc_updateMarketPrices;
				EDRP_market_last_update = time;
			};
			
			// Check for server restart
			if (time > EDRP_server_restart_time) then {
				["Server restart in 10 minutes!"] remoteExec ["EDRP_fnc_serverMessage", -2];
				sleep 600; // 10 minutes
				["Server restarting now!"] remoteExec ["EDRP_fnc_serverMessage", -2];
				sleep 5;
				#shutdown;
			};
		};
	};
	
	// Initialize database connection
	[] spawn {
		waitUntil {!isNil "EDRP_fnc_asyncCall"};
		
		// Test database connection
		_result = [0, "SELECT 1"] call EDRP_fnc_asyncCall;
		if (_result isEqualTo []) then {
			diag_log "EdenRP: Database connection failed!";
		} else {
			diag_log "EdenRP: Database connection successful!";
			EDRP_server_isReady = true;
			publicVariable "EDRP_server_isReady";
		};
	};
	
	diag_log "EdenRP: Server initialization complete";
};

// Client-side initialization
if (hasInterface) then {
	// Wait for server to be ready
	waitUntil {!isNil "EDRP_server_isReady" && EDRP_server_isReady};
	
	// Initialize client variables
	EDRP_player_cash = 0;
	EDRP_player_bank = 5000; // Starting bank money
	EDRP_player_licenses = [];
	EDRP_player_inventory = [];
	EDRP_player_gear = [];
	EDRP_player_vehicles = [];
	EDRP_player_houses = [];
	EDRP_player_gang = "";
	EDRP_player_gang_rank = 0;
	EDRP_player_job = "civilian";
	EDRP_player_level = 1;
	EDRP_player_xp = 0;
	EDRP_player_playtime = 0;
	EDRP_player_wanted = false;
	EDRP_player_bounty = 0;
	EDRP_player_arrested = false;
	EDRP_player_restrained = false;
	EDRP_player_escorted = false;
	EDRP_player_dead = false;
	EDRP_player_unconscious = false;
	EDRP_player_bleeding = false;
	EDRP_player_health = 100;
	EDRP_player_thirst = 100;
	EDRP_player_hunger = 100;
	EDRP_player_fatigue = 0;
	
	// Initialize UI variables
	EDRP_ui_hud_visible = true;
	EDRP_ui_inventory_open = false;
	EDRP_ui_phone_open = false;
	EDRP_ui_map_open = false;
	EDRP_ui_admin_open = false;
	
	// Initialize interaction variables
	EDRP_interaction_target = objNull;
	EDRP_interaction_distance = 5;
	EDRP_interaction_actions = [];
	
	// Initialize communication variables
	EDRP_radio_frequency = 0;
	EDRP_radio_volume = 50;
	EDRP_phone_number = "";
	EDRP_phone_contacts = [];
	EDRP_phone_messages = [];
	EDRP_phone_calls = [];
	
	// Initialize faction-specific variables
	switch (playerSide) do {
		case west: { // Police
			EDRP_player_job = "police";
			EDRP_player_rank = 1;
			EDRP_player_badge_number = 0;
			EDRP_player_department = "patrol";
		};
		case independent: { // Medical
			EDRP_player_job = "medical";
			EDRP_player_rank = 1;
			EDRP_player_certification = "emt";
			EDRP_player_hospital = "kavala";
		};
		case civilian: { // Civilian
			EDRP_player_job = "civilian";
			EDRP_player_occupation = "unemployed";
			EDRP_player_skills = [];
		};
	};
	
	// Start client initialization
	[] spawn {
		// Wait for system initialization
		waitUntil {!isNil "EDRP_fnc_systemInit"};

		// Initialize core systems
		call EDRP_fnc_systemInit;

		// Wait for server to be ready
		waitUntil {!isNil "EDRP_server_isReady" && EDRP_server_isReady};

		// Initialize session handler
		call EDRP_fnc_sessionHandler;

		// Wait for player data to load
		waitUntil {EDRP_session_completed};

		// Setup player
		call EDRP_fnc_playerSetup;

		// Initialize faction systems
		call EDRP_fnc_factionSetup;

		// Setup player actions and event handlers
		[] call EDRP_fnc_setupPlayerActions;
		[] call EDRP_fnc_setupEventHandlers;

		// Initialize UI systems
		[] call EDRP_fnc_initializeHUD;
		[] call EDRP_fnc_initializeInventory;
		[] call EDRP_fnc_initializePhone;
		[] call EDRP_fnc_initializeMap;

		// Start client monitoring loops
		[] call EDRP_fnc_startClientMonitoring;

		// Show intro sequence
		[] call EDRP_fnc_introSequence;

		// Show briefing
		[] call EDRP_fnc_briefingSystem;

		EDRP_client_isReady = true;

		// Notify server that client is ready
		[getPlayerUID player, "client_ready"] remoteExec ["EDRP_fnc_clientEvent", 2];

		diag_log "EdenRP: Client initialization complete";
	};
};

// Headless client initialization
if (!hasInterface && !isServer) then {
	// Headless client specific initialization
	diag_log "EdenRP: Headless client initialization complete";
};

// Global initialization complete
EDRP_mission_started = true;
publicVariable "EDRP_mission_started";

diag_log "EdenRP: Mission initialization complete";
