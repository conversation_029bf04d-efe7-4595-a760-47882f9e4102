/*
	EdenRP Altis Life - Player Setup
	Author: EdenRP Development Team
	Description: Handles player initialization and setup after data loading
	Version: 1.0.0
*/

// Player setup function
EDRP_fnc_playerSetup = {
	if (!EDRP_session_completed) exitWith {};
	
	diag_log "EdenRP: Starting player setup...";
	
	EDRP_loading_status = "<t color='#00FF00'>Setting up player...</t>";
	EDRP_loading_progress = 35;
	
	// Disable simulation during setup
	player enableSimulation false;
	player allowDamage false;
	
	// Clear default gear
	call EDRP_fnc_clearPlayerGear;
	
	// Setup faction-specific initialization
	switch (playerSide) do {
		case west: {
			call EDRP_fnc_setupPolicePlayer;
		};
		case independent: {
			call EDRP_fnc_setupMedicalPlayer;
		};
		case civilian: {
			call EDRP_fnc_setupCivilianPlayer;
		};
	};
	
	// Setup player actions
	call EDRP_fnc_setupPlayerActions;
	
	// Setup event handlers
	call EDRP_fnc_setupEventHandlers;
	
	// Setup key bindings
	call EDRP_fnc_setupKeyBindings;
	
	// Initialize player needs
	call EDRP_fnc_initPlayerNeeds;
	
	// Initialize player inventory
	call EDRP_fnc_initPlayerInventory;
	
	// Setup HUD and UI
	call EDRP_fnc_setupHUD;
	
	// Enable simulation and damage
	player enableSimulation true;
	player allowDamage true;
	
	EDRP_loading_progress = 45;
	EDRP_loading_status = "<t color='#00FF00'>Player setup complete</t>";
	
	diag_log "EdenRP: Player setup completed";
};

// Clear player gear function
EDRP_fnc_clearPlayerGear = {
	removeAllWeapons player;
	removeAllItems player;
	removeAllAssignedItems player;
	removeUniform player;
	removeVest player;
	removeBackpack player;
	removeHeadgear player;
	removeGoggles player;
};

// Setup police player
EDRP_fnc_setupPolicePlayer = {
	EDRP_loading_status = "<t color='#0080FF'>Initializing Police Officer...</t>";
	
	// Set police-specific variables
	player setVariable ["EDRP_police_on_duty", false, true];
	player setVariable ["EDRP_police_callsign", "", true];
	player setVariable ["EDRP_police_department", "patrol", true];
	
	// Load police gear based on rank
	call EDRP_fnc_loadPoliceGear;
	
	// Setup police actions
	call EDRP_fnc_setupPoliceActions;
	
	// Join police radio channel
	[player, "police"] call EDRP_fnc_joinRadioChannel;
	
	// Setup police spawn locations
	call EDRP_fnc_setupPoliceSpawns;
	
	diag_log "EdenRP: Police player setup completed";
};

// Setup medical player
EDRP_fnc_setupMedicalPlayer = {
	EDRP_loading_status = "<t color='#FF8000'>Initializing Medical Personnel...</t>";
	
	// Set medical-specific variables
	player setVariable ["EDRP_medical_on_duty", false, true];
	player setVariable ["EDRP_medical_certification", "emt", true];
	player setVariable ["EDRP_medical_hospital", "kavala", true];
	
	// Load medical gear based on rank
	call EDRP_fnc_loadMedicalGear;
	
	// Setup medical actions
	call EDRP_fnc_setupMedicalActions;
	
	// Join medical radio channel
	[player, "medical"] call EDRP_fnc_joinRadioChannel;
	
	// Setup medical spawn locations
	call EDRP_fnc_setupMedicalSpawns;
	
	diag_log "EdenRP: Medical player setup completed";
};

// Setup civilian player
EDRP_fnc_setupCivilianPlayer = {
	EDRP_loading_status = "<t color='#00FF00'>Initializing Civilian...</t>";
	
	// Set civilian-specific variables
	player setVariable ["EDRP_civilian_occupation", "unemployed", true];
	player setVariable ["EDRP_civilian_job_active", false, true];
	
	// Load civilian gear
	call EDRP_fnc_loadCivilianGear;
	
	// Setup civilian actions
	call EDRP_fnc_setupCivilianActions;
	
	// Setup civilian spawn locations
	call EDRP_fnc_setupCivilianSpawns;
	
	// Check for gang membership
	if (EDRP_player_gang != "") then {
		call EDRP_fnc_setupGangMember;
	};
	
	diag_log "EdenRP: Civilian player setup completed";
};

// Load police gear function
EDRP_fnc_loadPoliceGear = {
	private _rank = EDRP_police_rank;
	private _gear = [];
	
	// Basic police uniform
	player forceAddUniform "U_Rangemaster_F";
	player addVest "V_TacVest_blk_POLICE";
	player addHeadgear "H_Cap_police";
	
	// Add basic items
	player addItem "ItemMap";
	player addItem "ItemCompass";
	player addItem "ItemWatch";
	player addItem "ItemRadio";
	player addItem "ItemGPS";
	
	// Rank-based equipment
	switch (_rank) do {
		case 1: { // Cadet
			player addWeapon "hgun_P07_F";
			player addMagazine "16Rnd_9x21_Mag";
			player addMagazine "16Rnd_9x21_Mag";
		};
		case 2; case 3: { // Officer/Senior Officer
			player addWeapon "hgun_P07_F";
			player addWeapon "SMG_02_F";
			player addMagazine "16Rnd_9x21_Mag";
			player addMagazine "16Rnd_9x21_Mag";
			player addMagazine "30Rnd_9x21_Mag_SMG_02";
			player addMagazine "30Rnd_9x21_Mag_SMG_02";
		};
		default { // Sergeant and above
			player addWeapon "hgun_P07_F";
			player addWeapon "arifle_MX_F";
			player addMagazine "16Rnd_9x21_Mag";
			player addMagazine "16Rnd_9x21_Mag";
			player addMagazine "30Rnd_65x39_caseless_mag";
			player addMagazine "30Rnd_65x39_caseless_mag";
		};
	};
	
	// Police tools
	player addItem "ToolKit";
	player addItem "FirstAidKit";
	
	EDRP_police_gear_loaded = true;
};

// Load medical gear function
EDRP_fnc_loadMedicalGear = {
	private _rank = EDRP_medical_rank;
	
	// Basic medical uniform
	player forceAddUniform "U_Rangemaster_F";
	player addVest "V_TacVest_blk";
	player addHeadgear "H_Cap_blk";
	
	// Add basic items
	player addItem "ItemMap";
	player addItem "ItemCompass";
	player addItem "ItemWatch";
	player addItem "ItemRadio";
	player addItem "ItemGPS";
	
	// Medical equipment
	player addItem "Medikit";
	player addItem "FirstAidKit";
	player addItem "FirstAidKit";
	player addItem "FirstAidKit";
	
	// Rank-based equipment
	if (_rank >= 3) then {
		player addWeapon "hgun_P07_F";
		player addMagazine "16Rnd_9x21_Mag";
		player addMagazine "16Rnd_9x21_Mag";
	};
	
	EDRP_medical_gear_loaded = true;
};

// Load civilian gear function
EDRP_fnc_loadCivilianGear = {
	// Basic civilian clothing
	player forceAddUniform "U_C_Poloshirt_blue";
	
	// Add basic items
	player addItem "ItemMap";
	player addItem "ItemCompass";
	player addItem "ItemWatch";
	player addItem "ItemRadio";
	
	// Basic tools
	player addItem "ToolKit";
	player addItem "FirstAidKit";
};

// Initialize player needs
EDRP_fnc_initPlayerNeeds = {
	// Set initial needs values
	EDRP_player_hunger = 100;
	EDRP_player_thirst = 100;
	EDRP_player_fatigue = 0;
	EDRP_player_health = 100;
	
	// Apply to player
	player setVariable ["EDRP_player_hunger", EDRP_player_hunger, true];
	player setVariable ["EDRP_player_thirst", EDRP_player_thirst, true];
	player setVariable ["EDRP_player_fatigue", EDRP_player_fatigue, true];
	player setVariable ["EDRP_player_health", EDRP_player_health, true];
	
	// Start needs monitoring
	[] spawn EDRP_fnc_playerNeedsLoop;
};

// Initialize player inventory
EDRP_fnc_initPlayerInventory = {
	// Set inventory capacity based on faction
	private _capacity = switch (playerSide) do {
		case west: {50}; // Police
		case independent: {40}; // Medical
		case civilian: {30}; // Civilian
	};
	
	player setVariable ["EDRP_inventory_capacity", _capacity, true];
	player setVariable ["EDRP_inventory_weight", 0, true];
	
	// Initialize virtual inventory
	if (count EDRP_player_inventory == 0) then {
		EDRP_player_inventory = [];
	};
	
	player setVariable ["EDRP_player_inventory", EDRP_player_inventory, true];
};

// Setup gang member
EDRP_fnc_setupGangMember = {
	// Load gang data
	private _gangData = [EDRP_player_gang] call EDRP_fnc_getGangData;
	
	if (count _gangData > 0) then {
		player setVariable ["EDRP_gang_name", _gangData select 0, true];
		player setVariable ["EDRP_gang_tag", _gangData select 1, true];
		player setVariable ["EDRP_gang_rank", EDRP_player_gang_rank, true];
		
		// Join gang radio channel
		[player, format ["gang_%1", EDRP_player_gang]] call EDRP_fnc_joinRadioChannel;
		
		diag_log format ["EdenRP: Player joined gang: %1", _gangData select 0];
	};
};

diag_log "EdenRP: Player setup functions loaded";
