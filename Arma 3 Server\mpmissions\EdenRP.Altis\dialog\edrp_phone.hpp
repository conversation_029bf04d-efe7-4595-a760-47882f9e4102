/*
	EdenRP Altis Life - Phone System
	Author: EdenRP Development Team
	Description: Phone and communication interface
	Version: 1.0.0
*/

class EDRP_PhoneMenu: EDRP_MenuBase {
	idd = 5000;
	onLoad = "[] spawn EDRP_fnc_updatePhone;";
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {
			// Phone-like appearance
			x = 0.35;
			y = 0.15;
			w = 0.3;
			h = 0.7;
			colorBackground[] = {0.1, 0.1, 0.1, 0.95};
		};
		
		class Title: BaseTitle {
			text = "EdenRP Phone";
			x = 0.35;
			y = 0.15;
			w = 0.3;
			h = 0.05;
			colorBackground[] = {0.2, 0.2, 0.2, 1};
		};
		
		// Phone screen
		class ScreenBackground: EDRP_RscText {
			idc = -1;
			x = 0.36;
			y = 0.21;
			w = 0.28;
			h = 0.58;
			colorBackground[] = {0.05, 0.05, 0.05, 1};
		};
		
		// Tab backgrounds
		class Tab1Background: EDRP_RscText {
			idc = -1;
			x = 0.36;
			y = 0.8;
			w = 0.056;
			h = 0.04;
			colorBackground[] = {0.15, 0.15, 0.15, 0.8};
		};
		
		class Tab2Background: Tab1Background {
			x = 0.416;
		};
		
		class Tab3Background: Tab1Background {
			x = 0.472;
		};
		
		class Tab4Background: Tab1Background {
			x = 0.528;
		};
		
		class Tab5Background: Tab1Background {
			x = 0.584;
		};
	};
	
	class controls: controlsBase {
		// Phone tabs
		class ContactsTab: EDRP_RscButton {
			idc = 5001;
			text = "📞";
			tooltip = "Contacts";
			x = 0.36;
			y = 0.8;
			w = 0.056;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[5000, 'contacts'] call EDRP_fnc_switchPhoneTab;";
		};
		
		class MessagesTab: EDRP_RscButton {
			idc = 5002;
			text = "💬";
			tooltip = "Messages";
			x = 0.416;
			y = 0.8;
			w = 0.056;
			h = 0.04;
			colorBackground[] = {0.3, 0.3, 0.3, 0.8};
			onButtonClick = "[5000, 'messages'] call EDRP_fnc_switchPhoneTab;";
		};
		
		class DialerTab: EDRP_RscButton {
			idc = 5003;
			text = "🔢";
			tooltip = "Dialer";
			x = 0.472;
			y = 0.8;
			w = 0.056;
			h = 0.04;
			colorBackground[] = {0.3, 0.3, 0.3, 0.8};
			onButtonClick = "[5000, 'dialer'] call EDRP_fnc_switchPhoneTab;";
		};
		
		class AppsTab: EDRP_RscButton {
			idc = 5004;
			text = "📱";
			tooltip = "Apps";
			x = 0.528;
			y = 0.8;
			w = 0.056;
			h = 0.04;
			colorBackground[] = {0.3, 0.3, 0.3, 0.8};
			onButtonClick = "[5000, 'apps'] call EDRP_fnc_switchPhoneTab;";
		};
		
		class SettingsTab: EDRP_RscButton {
			idc = 5005;
			text = "⚙️";
			tooltip = "Settings";
			x = 0.584;
			y = 0.8;
			w = 0.056;
			h = 0.04;
			colorBackground[] = {0.3, 0.3, 0.3, 0.8};
			onButtonClick = "[5000, 'settings'] call EDRP_fnc_switchPhoneTab;";
		};
		
		// Main content area
		class ContentList: EDRP_RscListBox {
			idc = 5010;
			x = 0.37;
			y = 0.22;
			w = 0.26;
			h = 0.45;
			sizeEx = 0.025;
			onLBSelChanged = "[] call EDRP_fnc_phoneSelectionChanged;";
			onLBDblClick = "[] call EDRP_fnc_phoneDoubleClick;";
		};
		
		// Action buttons
		class ActionButton1: EDRP_RscButtonMenu {
			idc = 5011;
			text = "Call";
			x = 0.37;
			y = 0.68;
			w = 0.08;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_phoneAction1;";
		};
		
		class ActionButton2: EDRP_RscButtonMenu {
			idc = 5012;
			text = "Message";
			x = 0.46;
			y = 0.68;
			w = 0.08;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_phoneAction2;";
		};
		
		class ActionButton3: EDRP_RscButtonMenu {
			idc = 5013;
			text = "Add";
			x = 0.55;
			y = 0.68;
			w = 0.08;
			h = 0.03;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_phoneAction3;";
		};
		
		// Input field
		class InputEdit: EDRP_RscEdit {
			idc = 5020;
			text = "";
			x = 0.37;
			y = 0.72;
			w = 0.26;
			h = 0.03;
		};
		
		// Status display
		class StatusText: EDRP_RscText {
			idc = 5030;
			text = "Ready";
			x = 0.37;
			y = 0.76;
			w = 0.26;
			h = 0.025;
			style = 2;
			sizeEx = 0.025;
		};
		
		// Close button
		class ButtonClose: EDRP_RscButtonMenu {
			idc = 5099;
			text = "❌";
			x = 0.62;
			y = 0.16;
			w = 0.025;
			h = 0.025;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "closeDialog 5000; [] call EDRP_fnc_closePhone;";
		};
	};
};

// Message composition dialog
class EDRP_MessageDialog: EDRP_MenuBaseCompact {
	idd = 5100;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Send Message";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class RecipientCombo: EDRP_RscCombo {
			idc = 5101;
			x = 0.32;
			y = 0.28;
			w = 0.36;
			h = 0.04;
		};
		
		class MessageEdit: EDRP_RscEdit {
			idc = 5102;
			text = "";
			x = 0.32;
			y = 0.34;
			w = 0.36;
			h = 0.25;
			style = 16; // Multi-line
		};
		
		class RecipientLabel: EDRP_RscText {
			idc = -1;
			text = "To:";
			x = 0.32;
			y = 0.26;
			w = 0.36;
			h = 0.02;
		};
		
		class MessageLabel: EDRP_RscText {
			idc = -1;
			text = "Message:";
			x = 0.32;
			y = 0.32;
			w = 0.36;
			h = 0.02;
		};
		
		class CharCountText: EDRP_RscText {
			idc = 5103;
			text = "0/160";
			x = 0.32;
			y = 0.60;
			w = 0.36;
			h = 0.02;
			style = 1; // Right aligned
		};
		
		class SendButton: BaseButtonOK {
			text = "Send";
			onButtonClick = "[] call EDRP_fnc_sendMessage; closeDialog 5100;";
		};
		
		class ButtonClose: BaseButtonClose {};
	};
};

// Contact management dialog
class EDRP_ContactDialog: EDRP_MenuBaseCompact {
	idd = 5200;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {};
		class Title: BaseTitle {
			text = "Add Contact";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class NameEdit: EDRP_RscEdit {
			idc = 5201;
			text = "";
			x = 0.32;
			y = 0.3;
			w = 0.36;
			h = 0.04;
		};
		
		class NumberEdit: EDRP_RscEdit {
			idc = 5202;
			text = "";
			x = 0.32;
			y = 0.36;
			w = 0.36;
			h = 0.04;
		};
		
		class NameLabel: EDRP_RscText {
			idc = -1;
			text = "Name:";
			x = 0.32;
			y = 0.28;
			w = 0.36;
			h = 0.02;
		};
		
		class NumberLabel: EDRP_RscText {
			idc = -1;
			text = "Number:";
			x = 0.32;
			y = 0.34;
			w = 0.36;
			h = 0.02;
		};
		
		class AddButton: BaseButtonOK {
			text = "Add Contact";
			onButtonClick = "[] call EDRP_fnc_addContact; closeDialog 5200;";
		};
		
		class ButtonClose: BaseButtonClose {};
	};
};

// Call interface dialog
class EDRP_CallDialog: EDRP_MenuBaseCompact {
	idd = 5300;
	
	class controlsBackground: controlsBackgroundBase {
		class Background: BaseBackground {
			colorBackground[] = {0.05, 0.05, 0.05, 0.95};
		};
		class Title: BaseTitle {
			text = "Calling...";
		};
		class ContentBackground: BaseContentBackground {};
	};
	
	class controls: controlsBase {
		class CallerNameText: EDRP_RscText {
			idc = 5301;
			text = "";
			x = 0.32;
			y = 0.3;
			w = 0.36;
			h = 0.04;
			style = 2;
			sizeEx = 0.04;
			font = "RobotoCondensedBold";
		};
		
		class CallerNumberText: EDRP_RscText {
			idc = 5302;
			text = "";
			x = 0.32;
			y = 0.35;
			w = 0.36;
			h = 0.03;
			style = 2;
			sizeEx = 0.03;
		};
		
		class CallStatusText: EDRP_RscText {
			idc = 5303;
			text = "Calling...";
			x = 0.32;
			y = 0.4;
			w = 0.36;
			h = 0.03;
			style = 2;
			sizeEx = 0.025;
		};
		
		class CallDurationText: EDRP_RscText {
			idc = 5304;
			text = "00:00";
			x = 0.32;
			y = 0.45;
			w = 0.36;
			h = 0.03;
			style = 2;
			sizeEx = 0.03;
		};
		
		class AnswerButton: EDRP_RscButtonMenu {
			idc = 5305;
			text = "Answer";
			x = 0.32;
			y = 0.55;
			w = 0.15;
			h = 0.05;
			colorBackground[] = EDRP_COLOR_SUCCESS;
			onButtonClick = "[] call EDRP_fnc_answerCall;";
		};
		
		class HangupButton: EDRP_RscButtonMenu {
			idc = 5306;
			text = "Hang Up";
			x = 0.53;
			y = 0.55;
			w = 0.15;
			h = 0.05;
			colorBackground[] = EDRP_COLOR_ERROR;
			onButtonClick = "[] call EDRP_fnc_hangupCall; closeDialog 5300;";
		};
		
		class MuteButton: EDRP_RscButtonMenu {
			idc = 5307;
			text = "Mute";
			x = 0.32;
			y = 0.62;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_WARNING;
			onButtonClick = "[] call EDRP_fnc_muteCall;";
		};
		
		class SpeakerButton: EDRP_RscButtonMenu {
			idc = 5308;
			text = "Speaker";
			x = 0.44;
			y = 0.62;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_PRIMARY;
			onButtonClick = "[] call EDRP_fnc_toggleSpeaker;";
		};
		
		class HoldButton: EDRP_RscButtonMenu {
			idc = 5309;
			text = "Hold";
			x = 0.56;
			y = 0.62;
			w = 0.1;
			h = 0.04;
			colorBackground[] = EDRP_COLOR_SECONDARY;
			onButtonClick = "[] call EDRP_fnc_holdCall;";
		};
	};
};
