-- --------------------------------------------------------
-- EdenRP Altis Life Database Schema
-- Author: EdenRP Development Team
-- Description: Complete database schema for EdenRP server
-- Version: 1.0.0
-- Based on: Olympus Altis Life (Modified for EdenRP)
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;

-- Create EdenRP database
CREATE DATABASE IF NOT EXISTS `edenrp` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */;
USE `edenrp`;

-- --------------------------------------------------------
-- Table structure for table `edrp_players`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_players` (
  `uid` int(12) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `playerid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `cash` bigint(20) NOT NULL DEFAULT 0,
  `bank_account` bigint(20) NOT NULL DEFAULT 5000,
  `deposit_box` bigint(20) unsigned NOT NULL DEFAULT 0,
  
  -- Faction Levels
  `police_rank` enum('0','1','2','3','4','5','6','7','8','9','10') NOT NULL DEFAULT '0',
  `medical_rank` enum('0','1','2','3','4','5','6','7','8','9','10') NOT NULL DEFAULT '0',
  `admin_level` enum('0','1','2','3','4','5') NOT NULL DEFAULT '0',
  `donator_level` int(3) NOT NULL DEFAULT 0,
  
  -- Licenses (JSON format for better structure)
  `police_licenses` json DEFAULT NULL,
  `civilian_licenses` json DEFAULT NULL,
  `medical_licenses` json DEFAULT NULL,
  
  -- Gear Storage (JSON format)
  `police_gear` json NOT NULL DEFAULT '{}',
  `medical_gear` json NOT NULL DEFAULT '{}',
  `civilian_gear` json NOT NULL DEFAULT '{}',
  
  -- Player Status
  `arrested` json NOT NULL DEFAULT '{}',
  `wanted_status` json DEFAULT NULL,
  `blacklisted` tinyint(1) NOT NULL DEFAULT 0,
  `restrictions` json DEFAULT NULL,
  
  -- Statistics and Progression
  `player_stats` json DEFAULT NULL,
  `skill_levels` json DEFAULT NULL,
  `achievements` json DEFAULT NULL,
  `playtime_total` int(11) NOT NULL DEFAULT 0,
  `playtime_police` int(11) NOT NULL DEFAULT 0,
  `playtime_medical` int(11) NOT NULL DEFAULT 0,
  `playtime_civilian` int(11) NOT NULL DEFAULT 0,
  
  -- Position and Session Data
  `last_position` json DEFAULT NULL,
  `last_side` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'civilian',
  `last_server` int(2) NOT NULL DEFAULT 1,
  
  -- Timestamps
  `last_active` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `first_joined` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_login` timestamp NULL DEFAULT NULL,
  
  -- Additional Features
  `profile_picture` varchar(255) DEFAULT NULL,
  `biography` text DEFAULT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `email_address` varchar(100) DEFAULT NULL,
  `preferred_language` varchar(5) DEFAULT 'en',
  
  PRIMARY KEY (`uid`),
  UNIQUE KEY `playerid` (`playerid`),
  KEY `name` (`name`),
  KEY `blacklisted` (`blacklisted`),
  KEY `last_active` (`last_active`),
  KEY `police_rank` (`police_rank`),
  KEY `medical_rank` (`medical_rank`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_vehicles`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_vehicles` (
  `id` int(12) NOT NULL AUTO_INCREMENT,
  `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner_type` enum('player','gang','faction') NOT NULL DEFAULT 'player',
  `faction` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `classname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `vehicle_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `custom_name` varchar(50) DEFAULT NULL,
  
  -- Vehicle Status
  `alive` tinyint(1) NOT NULL DEFAULT 1,
  `active` tinyint(1) NOT NULL DEFAULT 0,
  `impounded` tinyint(1) NOT NULL DEFAULT 0,
  `stolen` tinyint(1) NOT NULL DEFAULT 0,
  
  -- Vehicle Properties
  `license_plate` varchar(20) NOT NULL,
  `color_scheme` json NOT NULL DEFAULT '[]',
  `inventory` json NOT NULL DEFAULT '{}',
  `fuel_level` float NOT NULL DEFAULT 1.0,
  `damage_level` float NOT NULL DEFAULT 0.0,
  
  -- Insurance and Modifications
  `insured` tinyint(1) NOT NULL DEFAULT 0,
  `insurance_expires` timestamp NULL DEFAULT NULL,
  `modifications` json NOT NULL DEFAULT '{}',
  `tuning_data` json DEFAULT NULL,
  
  -- Position Data
  `garage_location` varchar(50) DEFAULT NULL,
  `last_position` json DEFAULT NULL,
  `last_direction` float DEFAULT 0,
  
  -- Timestamps
  `purchased_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_used` timestamp NULL DEFAULT NULL,
  
  PRIMARY KEY (`id`),
  KEY `owner_id` (`owner_id`),
  KEY `faction` (`faction`),
  KEY `vehicle_type` (`vehicle_type`),
  KEY `alive` (`alive`),
  KEY `active` (`active`),
  KEY `license_plate` (`license_plate`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_gangs`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_gangs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tag` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `leader_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `bank_balance` bigint(20) DEFAULT 0,
  `max_members` int(3) NOT NULL DEFAULT 8,
  
  -- Gang Status
  `active` tinyint(1) DEFAULT 1,
  `verified` tinyint(1) DEFAULT 0,
  `level` int(3) NOT NULL DEFAULT 1,
  
  -- Gang Statistics
  `total_kills` int(11) NOT NULL DEFAULT 0,
  `total_deaths` int(11) NOT NULL DEFAULT 0,
  `territory_controlled` int(3) NOT NULL DEFAULT 0,
  `reputation_points` int(11) NOT NULL DEFAULT 0,
  
  -- Gang Properties
  `gang_color` varchar(7) DEFAULT '#FFFFFF',
  `gang_logo` varchar(255) DEFAULT NULL,
  `gang_description` text DEFAULT NULL,
  `gang_rules` text DEFAULT NULL,
  
  -- Territory and Assets
  `owned_territories` json DEFAULT NULL,
  `gang_vehicles` json DEFAULT NULL,
  `gang_properties` json DEFAULT NULL,
  
  -- Timestamps
  `created_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_active` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`),
  UNIQUE KEY `tag_UNIQUE` (`tag`),
  KEY `leader_id` (`leader_id`),
  KEY `active` (`active`),
  KEY `level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_gang_members`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_gang_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `gang_id` int(11) NOT NULL,
  `player_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rank` enum('member','lieutenant','captain','leader') NOT NULL DEFAULT 'member',
  `permissions` json DEFAULT NULL,
  `contribution_points` int(11) NOT NULL DEFAULT 0,
  `joined_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_active` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `gang_player_unique` (`gang_id`,`player_id`),
  KEY `player_id` (`player_id`),
  CONSTRAINT `fk_gang_members_gang` FOREIGN KEY (`gang_id`) REFERENCES `edrp_gangs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_houses`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_houses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `house_position` json NOT NULL,
  `house_type` varchar(30) NOT NULL DEFAULT 'small',
  `custom_name` varchar(50) DEFAULT NULL,
  
  -- House Status
  `owned` tinyint(1) DEFAULT 0,
  `locked` tinyint(1) DEFAULT 1,
  `for_sale` tinyint(1) DEFAULT 0,
  `sale_price` int(11) DEFAULT 0,
  
  -- Storage and Inventory
  `virtual_inventory` json DEFAULT NULL,
  `physical_inventory` json DEFAULT NULL,
  `virtual_storage_capacity` int(11) NOT NULL DEFAULT 100,
  `physical_storage_capacity` int(11) NOT NULL DEFAULT 100,
  
  -- Access Control
  `authorized_players` json DEFAULT NULL,
  `security_level` int(2) NOT NULL DEFAULT 1,
  `alarm_system` tinyint(1) DEFAULT 0,
  
  -- House Features
  `garage_spaces` int(2) NOT NULL DEFAULT 0,
  `upgrade_level` int(2) NOT NULL DEFAULT 1,
  `decorations` json DEFAULT NULL,
  
  -- Timestamps
  `purchased_date` timestamp NULL DEFAULT NULL,
  `last_accessed` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `rent_due_date` timestamp NULL DEFAULT NULL,
  
  PRIMARY KEY (`id`),
  KEY `owner_id` (`owner_id`),
  KEY `owned` (`owned`),
  KEY `for_sale` (`for_sale`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_server_config`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_server_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_name` varchar(100) NOT NULL DEFAULT 'EdenRP Altis Life',
  `server_version` varchar(20) NOT NULL DEFAULT '1.0.0',
  `max_players` int(3) NOT NULL DEFAULT 120,
  `restart_warnings` json DEFAULT NULL,
  `economy_settings` json DEFAULT NULL,
  `faction_settings` json DEFAULT NULL,
  `admin_list` json DEFAULT NULL,
  `banned_players` json DEFAULT NULL,
  `server_rules` text DEFAULT NULL,
  `maintenance_mode` tinyint(1) DEFAULT 0,
  `last_restart` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),

  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_wanted_list`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_wanted_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `wanted_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `crime_type` varchar(50) NOT NULL,
  `crime_description` text NOT NULL,
  `bounty_amount` int(11) NOT NULL DEFAULT 0,
  `danger_level` enum('low','medium','high','extreme') NOT NULL DEFAULT 'low',
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `arrest_attempts` int(3) NOT NULL DEFAULT 0,
  `issued_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_seen` timestamp NULL DEFAULT NULL,
  `expires_date` timestamp NULL DEFAULT NULL,

  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `active` (`active`),
  KEY `danger_level` (`danger_level`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_market_data`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_market_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_name` varchar(50) NOT NULL,
  `base_price` int(11) NOT NULL,
  `current_price` int(11) NOT NULL,
  `demand_level` float NOT NULL DEFAULT 1.0,
  `supply_level` float NOT NULL DEFAULT 1.0,
  `price_history` json DEFAULT NULL,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),

  PRIMARY KEY (`id`),
  UNIQUE KEY `item_name` (`item_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_transactions`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_transactions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `transaction_type` enum('purchase','sale','transfer','fine','salary','bonus','tax') NOT NULL,
  `amount` bigint(20) NOT NULL,
  `description` varchar(255) NOT NULL,
  `related_player` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),

  PRIMARY KEY (`id`),
  KEY `player_id` (`player_id`),
  KEY `transaction_type` (`transaction_type`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_phone_messages`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_phone_messages` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sender_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `recipient_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message_type` enum('text','call','emergency') NOT NULL DEFAULT 'text',
  `message_content` text NOT NULL,
  `read_status` tinyint(1) NOT NULL DEFAULT 0,
  `sent_timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_timestamp` timestamp NULL DEFAULT NULL,

  PRIMARY KEY (`id`),
  KEY `sender_id` (`sender_id`),
  KEY `recipient_id` (`recipient_id`),
  KEY `sent_timestamp` (`sent_timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_police_reports`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_police_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `officer_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `suspect_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `report_type` enum('arrest','citation','incident','investigation') NOT NULL,
  `report_title` varchar(100) NOT NULL,
  `report_content` text NOT NULL,
  `evidence_data` json DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `status` enum('open','closed','under_review') NOT NULL DEFAULT 'open',
  `created_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_modified` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),

  PRIMARY KEY (`id`),
  KEY `officer_id` (`officer_id`),
  KEY `suspect_id` (`suspect_id`),
  KEY `report_type` (`report_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_medical_records`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_medical_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `patient_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `medic_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `treatment_type` enum('revive','heal','surgery','checkup') NOT NULL,
  `injury_description` text DEFAULT NULL,
  `treatment_notes` text DEFAULT NULL,
  `medication_given` json DEFAULT NULL,
  `location` varchar(100) DEFAULT NULL,
  `cost` int(11) NOT NULL DEFAULT 0,
  `treatment_date` timestamp NOT NULL DEFAULT current_timestamp(),

  PRIMARY KEY (`id`),
  KEY `patient_id` (`patient_id`),
  KEY `medic_id` (`medic_id`),
  KEY `treatment_type` (`treatment_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_territories`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_territories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `territory_name` varchar(50) NOT NULL,
  `territory_type` enum('drug','capture','resource','gang_base') NOT NULL,
  `position_data` json NOT NULL,
  `controlling_gang` int(11) DEFAULT NULL,
  `capture_points` int(11) NOT NULL DEFAULT 0,
  `max_capture_points` int(11) NOT NULL DEFAULT 100,
  `income_rate` int(11) NOT NULL DEFAULT 0,
  `last_contested` timestamp NULL DEFAULT NULL,
  `created_date` timestamp NOT NULL DEFAULT current_timestamp(),

  PRIMARY KEY (`id`),
  UNIQUE KEY `territory_name` (`territory_name`),
  KEY `controlling_gang` (`controlling_gang`),
  KEY `territory_type` (`territory_type`),
  CONSTRAINT `fk_territory_gang` FOREIGN KEY (`controlling_gang`) REFERENCES `edrp_gangs` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_server_logs`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_server_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `log_type` enum('admin','player','system','economy','security') NOT NULL,
  `player_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `admin_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `server_id` int(2) NOT NULL DEFAULT 1,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),

  PRIMARY KEY (`id`),
  KEY `log_type` (`log_type`),
  KEY `player_id` (`player_id`),
  KEY `admin_id` (`admin_id`),
  KEY `timestamp` (`timestamp`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Table structure for table `edrp_skill_progression`
-- --------------------------------------------------------
CREATE TABLE IF NOT EXISTS `edrp_skill_progression` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `player_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `skill_name` varchar(30) NOT NULL,
  `skill_level` int(3) NOT NULL DEFAULT 1,
  `experience_points` int(11) NOT NULL DEFAULT 0,
  `total_actions` int(11) NOT NULL DEFAULT 0,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),

  PRIMARY KEY (`id`),
  UNIQUE KEY `player_skill_unique` (`player_id`,`skill_name`),
  KEY `skill_name` (`skill_name`),
  KEY `skill_level` (`skill_level`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------
-- Insert default configuration data
-- --------------------------------------------------------
INSERT INTO `edrp_server_config` (`id`, `server_name`, `server_version`, `max_players`, `economy_settings`, `faction_settings`) VALUES
(1, 'EdenRP Altis Life', '1.0.0', 120,
'{"starting_cash": 0, "starting_bank": 5000, "tax_rate": 0.05, "inflation_rate": 0.02}',
'{"police_slots": 20, "medical_slots": 15, "civilian_slots": 85}');

-- --------------------------------------------------------
-- Insert default market data
-- --------------------------------------------------------
INSERT INTO `edrp_market_data` (`item_name`, `base_price`, `current_price`) VALUES
('apple', 25, 25),
('peach', 30, 30),
('water', 15, 15),
('bread', 45, 45),
('copperingot', 1450, 1450),
('ironingot', 2850, 2850),
('saltrefined', 1250, 1250),
('diamondcut', 3500, 3500),
('goldbar', 2750, 2750),
('oilprocessed', 3250, 3250),
('heroinprocessed', 4500, 4500),
('cocaineprocessed', 3850, 3850),
('marijuanaprocessed', 2250, 2250);

-- --------------------------------------------------------
-- Create stored procedures for common operations
-- --------------------------------------------------------
DELIMITER //

CREATE PROCEDURE `ResetVehicleActivity`()
BEGIN
    UPDATE `edrp_vehicles` SET `active` = 0 WHERE `active` = 1 AND `alive` = 1;
END//

CREATE PROCEDURE `CleanupOldLogs`(IN days_old INT)
BEGIN
    DELETE FROM `edrp_server_logs` WHERE `timestamp` < DATE_SUB(NOW(), INTERVAL days_old DAY);
END//

CREATE PROCEDURE `UpdateMarketPrices`()
BEGIN
    UPDATE `edrp_market_data`
    SET `current_price` = GREATEST(1, `base_price` * (0.8 + (RAND() * 0.4)))
    WHERE `last_updated` < DATE_SUB(NOW(), INTERVAL 1 HOUR);
END//

DELIMITER ;

-- --------------------------------------------------------
-- Create indexes for performance optimization
-- --------------------------------------------------------
CREATE INDEX `idx_players_last_active` ON `edrp_players` (`last_active`);
CREATE INDEX `idx_vehicles_owner_faction` ON `edrp_vehicles` (`owner_id`, `faction`);
CREATE INDEX `idx_transactions_player_date` ON `edrp_transactions` (`player_id`, `timestamp`);
CREATE INDEX `idx_logs_type_timestamp` ON `edrp_server_logs` (`log_type`, `timestamp`);

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
