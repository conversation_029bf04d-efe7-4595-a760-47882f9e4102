/*
	EdenRP Altis Life - Resource Gathering Configuration
	Author: EdenRP Development Team
	Description: Configuration for all resource gathering activities
	Version: 1.0.0
*/

class CfgGather {
	// Apple Gathering
	class apple {
		name = "STR_EDRP_ItemApple";
		item = "apple";
		zones[] = {
			"apple_1",
			"apple_2",
			"apple_3"
		};
		skill = "";
		skillReq = 0;
		time = 3;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,3};
		amountSkilled[] = {2,5};
		xp = 5;
		levelReq = 0;
		tools[] = {};
		toolsReq = 0;
	};

	// Peach Gathering
	class peach {
		name = "STR_EDRP_ItemPeach";
		item = "peach";
		zones[] = {
			"peach_1",
			"peach_2"
		};
		skill = "";
		skillReq = 0;
		time = 3;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,3};
		amountSkilled[] = {2,5};
		xp = 5;
		levelReq = 0;
		tools[] = {};
		toolsReq = 0;
	};

	// Copper Mining
	class copper {
		name = "STR_EDRP_ItemCopper";
		item = "copperore";
		zones[] = {
			"copper_1",
			"copper_2",
			"copper_3"
		};
		skill = "mining";
		skillReq = 0;
		time = 8;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,2};
		amountSkilled[] = {2,4};
		xp = 10;
		levelReq = 0;
		tools[] = {"pickaxe"};
		toolsReq = 1;
	};

	// Iron Mining
	class iron {
		name = "STR_EDRP_ItemIron";
		item = "ironore";
		zones[] = {
			"iron_1",
			"iron_2"
		};
		skill = "mining";
		skillReq = 1;
		time = 10;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,2};
		amountSkilled[] = {2,3};
		xp = 15;
		levelReq = 5;
		tools[] = {"pickaxe"};
		toolsReq = 1;
	};

	// Salt Mining
	class salt {
		name = "STR_EDRP_ItemSalt";
		item = "salt";
		zones[] = {
			"salt_1"
		};
		skill = "mining";
		skillReq = 0;
		time = 6;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,3};
		amountSkilled[] = {2,5};
		xp = 8;
		levelReq = 0;
		tools[] = {"pickaxe"};
		toolsReq = 1;
	};

	// Diamond Mining
	class diamond {
		name = "STR_EDRP_ItemDiamond";
		item = "diamond";
		zones[] = {
			"diamond_1"
		};
		skill = "mining";
		skillReq = 3;
		time = 15;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,1};
		amountSkilled[] = {1,2};
		xp = 50;
		levelReq = 15;
		tools[] = {"pickaxe"};
		toolsReq = 1;
	};

	// Gold Mining
	class gold {
		name = "STR_EDRP_ItemGold";
		item = "goldore";
		zones[] = {
			"gold_1"
		};
		skill = "mining";
		skillReq = 2;
		time = 12;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,2};
		amountSkilled[] = {1,3};
		xp = 25;
		levelReq = 10;
		tools[] = {"pickaxe"};
		toolsReq = 1;
	};

	// Oil Extraction
	class oil {
		name = "STR_EDRP_ItemOil";
		item = "oilunprocessed";
		zones[] = {
			"oil_1",
			"oil_2"
		};
		skill = "extraction";
		skillReq = 1;
		time = 10;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,2};
		amountSkilled[] = {2,4};
		xp = 20;
		levelReq = 5;
		tools[] = {"oilpump"};
		toolsReq = 1;
	};

	// Heroin Processing (Illegal)
	class heroin {
		name = "STR_EDRP_ItemHeroin";
		item = "heroinunprocessed";
		zones[] = {
			"heroin_1"
		};
		skill = "chemistry";
		skillReq = 2;
		time = 20;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,2};
		amountSkilled[] = {2,3};
		xp = 30;
		levelReq = 10;
		tools[] = {"chemkit"};
		toolsReq = 1;
		illegal = 1;
	};

	// Cocaine Processing (Illegal)
	class cocaine {
		name = "STR_EDRP_ItemCocaine";
		item = "cocaineunprocessed";
		zones[] = {
			"cocaine_1"
		};
		skill = "chemistry";
		skillReq = 1;
		time = 15;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,2};
		amountSkilled[] = {2,4};
		xp = 25;
		levelReq = 8;
		tools[] = {"chemkit"};
		toolsReq = 1;
		illegal = 1;
	};

	// Marijuana Growing (Illegal)
	class marijuana {
		name = "STR_EDRP_ItemMarijuana";
		item = "marijuanaunprocessed";
		zones[] = {
			"marijuana_1",
			"marijuana_2"
		};
		skill = "farming";
		skillReq = 1;
		time = 12;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,3};
		amountSkilled[] = {2,5};
		xp = 15;
		levelReq = 5;
		tools[] = {};
		toolsReq = 0;
		illegal = 1;
	};

	// Fishing
	class fish {
		name = "Fish";
		item = "fishraw";
		zones[] = {
			"fishing_1",
			"fishing_2",
			"fishing_3"
		};
		skill = "fishing";
		skillReq = 0;
		time = 8;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,2};
		amountSkilled[] = {2,4};
		xp = 12;
		levelReq = 0;
		tools[] = {"fishingrod"};
		toolsReq = 1;
	};

	// Turtle Hunting (Illegal)
	class turtle {
		name = "Turtle Meat";
		item = "turtleraw";
		zones[] = {
			"turtle_1"
		};
		skill = "hunting";
		skillReq = 1;
		time = 15;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,1};
		amountSkilled[] = {1,2};
		xp = 35;
		levelReq = 10;
		tools[] = {"huntingknife"};
		toolsReq = 1;
		illegal = 1;
	};

	// Sand Collection
	class sand {
		name = "Sand";
		item = "sand";
		zones[] = {
			"sand_1",
			"sand_2"
		};
		skill = "";
		skillReq = 0;
		time = 5;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {2,4};
		amountSkilled[] = {3,6};
		xp = 3;
		levelReq = 0;
		tools[] = {};
		toolsReq = 0;
	};

	// Rock Collection
	class rock {
		name = "Rock";
		item = "rock";
		zones[] = {
			"rock_1",
			"rock_2",
			"rock_3"
		};
		skill = "";
		skillReq = 0;
		time = 4;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		animationCaller = "AinvPknlMstpSnonWnonDnon_medic_1";
		amount[] = {1,3};
		amountSkilled[] = {2,5};
		xp = 2;
		levelReq = 0;
		tools[] = {};
		toolsReq = 0;
	};
};
