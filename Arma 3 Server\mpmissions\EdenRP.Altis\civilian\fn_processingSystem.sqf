/*
	EdenRP Altis Life - Resource Processing System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Handles all resource processing and manufacturing
	Version: 1.0.0
*/

// Main processing function (adapted from Olympus fn_processAction)
EDRP_fnc_processAction = {
	params [
		["_vendor", obj<PERSON><PERSON>, [obj<PERSON><PERSON>]],
		["_player", player, [obj<PERSON><PERSON>]],
		["_id", -1, [0]],
		["_type", "", [""]]
	];

	if (EDRP_is_processing) exitWith {["You are already processing something"] call EDRP_fnc_hint;};
	if (EDRP_action_inUse) exitWith {["You are currently doing something else"] call EDRP_fnc_hint;};

	// Processing configurations (adapted from Olympus)
	private _itemInfo = switch (_type) do {
		// Legal processing
		case "salt": {[["salt"], "saltrefined", 350, "Processing Salt", 3]};
		case "iron": {[["ironore"], "ironingot", 750, "Processing Iron", 8]};
		case "copper": {[["copperore"], "copperingot", 875, "Processing Copper", 5]};
		case "oil": {[["oilunprocessed"], "oilprocessed", 1130, "Processing Oil", 8]};
		case "diamond": {[["diamond"], "diamondcut", 2500, "Cutting Diamond", 12]};
		case "gold": {[["goldore"], "goldbar", 1500, "Smelting Gold", 10]};
		case "fish": {[["fishraw"], "fishcooked", 0, "Cooking Fish", 3]};

		// Illegal processing
		case "marijuana": {[["marijuana"], "marijuanaprocessed", 1500, "Processing Marijuana", 6]};
		case "cocaine": {[["cocaine"], "cocaineprocessed", 2000, "Processing Cocaine", 8]};
		case "heroin": {[["heroin"], "heroinprocessed", 2500, "Processing Heroin", 10]};

		default {[]};
	};

	if (_itemInfo isEqualTo []) exitWith {["Invalid processing type"] call EDRP_fnc_hint;};

	_itemInfo params ["_oldItems", "_newItem", "_cost", "_processText", "_delay"];

	// Check if player has required items
	private _hasItems = true;
	{
		if !([_x] call EDRP_fnc_hasItem) exitWith {
			[format ["You need %1 to process", [_x] call EDRP_fnc_getItemName]] call EDRP_fnc_hint;
			_hasItems = false;
		};
	} forEach _oldItems;

	if (!_hasItems) exitWith {};

	// Check processing cost
	if (_cost > 0 && EDRP_player_cash < _cost) exitWith {
		[format ["You need $%1 to process this", [_cost] call EDRP_fnc_numberText]] call EDRP_fnc_hint;
	};

	// Start processing
	EDRP_is_processing = true;
	EDRP_action_inUse = true;

	[_oldItems, _newItem, _cost, _processText, _delay, _type] spawn EDRP_fnc_processLoop;
};

// Processing loop
EDRP_fnc_processLoop = {
	params ["_oldItems", "_newItem", "_cost", "_processText", "_delay", "_type"];

	// Progress bar
	[
		_delay,
		_processText,
		{
			params ["_oldItems", "_newItem", "_cost", "_type"];

			// Remove old items
			{
				if !([_x, 1] call EDRP_fnc_removeItem) exitWith {
					["Missing required materials"] call EDRP_fnc_hint;
					false
				};
			} forEach _oldItems;

			// Pay processing cost
			if (_cost > 0) then {
				EDRP_player_cash = EDRP_player_cash - _cost;
			};

			// Add new item
			if ([_newItem, 1] call EDRP_fnc_addItem) then {
				private _itemName = [_newItem] call EDRP_fnc_getItemName;
				[format ["Processed into %1", _itemName], "success"] call EDRP_fnc_hint;

				// Add XP based on processing type
				private _xp = switch (_type) do {
					case "salt": { 5 };
					case "iron": { 12 };
					case "copper": { 8 };
					case "oil": { 15 };
					case "diamond": { 40 };
					case "gold": { 20 };
					case "fish": { 5 };
					case "marijuana": { 25 };
					case "cocaine": { 35 };
					case "heroin": { 45 };
					default { 10 };
				};

				// Determine skill type
				private _skill = switch (_type) do {
					case "iron"; case "copper"; case "gold": { "smelting" };
					case "salt"; case "oil"; case "marijuana"; case "cocaine"; case "heroin": { "refining" };
					case "diamond": { "gemcutting" };
					case "fish": { "cooking" };
					default { "" };
				};

				if (_skill != "") then {
					[_skill, _xp] call EDRP_fnc_addJobXP;
				};

				playSound "processing_machine";
				true
			} else {
				["Inventory full - processing failed"] call EDRP_fnc_hint;
				// Return items
				{
					[_x, 1] call EDRP_fnc_addItem;
				} forEach _oldItems;
				false
			};
		},
		{
			["Processing cancelled"] call EDRP_fnc_hint;
			false
		},
		[_oldItems, _newItem, _cost, _type]
	] call EDRP_fnc_progressBar;

	EDRP_is_processing = false;
	EDRP_action_inUse = false;
};

// Start processing resources (legacy compatibility)
EDRP_fnc_startProcessing = {
	params [
		["_processType", "", [""]],
		["_processLocation", "", [""]],
		["_quantity", 1, [0]]
	];
	
	if (_processType == "" || _processLocation == "") exitWith {
		["Invalid processing parameters"] call EDRP_fnc_hint;
		false
	};
	
	// Check if already processing
	if (EDRP_processing_active) exitWith {
		["You are already processing resources"] call EDRP_fnc_hint;
		false
	};
	
	// Get process configuration
	private _config = [_processType] call EDRP_fnc_getProcessConfig;
	if (_config isEqualTo []) exitWith {
		["Unknown process type"] call EDRP_fnc_hint;
		false
	};
	
	_config params ["_name", "_outputItem", "_inputItem", "_inputAmount", "_outputAmount", "_time", "_skill", "_skillReq", "_xp", "_levelReq", "_tools", "_cost"];
	
	// Check requirements
	if !([_processType, _config, _quantity] call EDRP_fnc_checkProcessRequirements) exitWith { false };
	
	// Check if in valid processing zone
	if !([_processLocation] call EDRP_fnc_isInProcessZone) exitWith {
		["You must be at a valid processing facility"] call EDRP_fnc_hint;
		false
	};
	
	// Start processing
	EDRP_processing_active = true;
	EDRP_processing_type = _processType;
	EDRP_processing_progress = 0;
	EDRP_processing_location = _processLocation;
	
	// Show processing started message
	[format ["Started processing %1x %2...", _quantity, _name], "info"] call EDRP_fnc_hint;
	
	// Start processing animation and progress
	[_processType, _config, _quantity] spawn EDRP_fnc_processingLoop;
	
	true
};

// Processing loop with progress bar
EDRP_fnc_processingLoop = {
	params ["_processType", "_config", "_quantity"];
	
	_config params ["_name", "_outputItem", "_inputItem", "_inputAmount", "_outputAmount", "_time", "_skill", "_skillReq", "_xp", "_levelReq", "_tools", "_cost"];
	
	private _processedCount = 0;
	
	while {_processedCount < _quantity && EDRP_processing_active} do {
		// Calculate actual processing time based on skill
		private _skillLevel = if (_skill != "") then { EDRP_job_skills get _skill } else { 0 };
		private _timeReduction = _skillLevel * 0.08; // 8% reduction per skill level
		private _actualTime = _time * (1 - _timeReduction);
		if (_actualTime < (_time * 0.4)) then { _actualTime = _time * 0.4; }; // Minimum 40% of base time
		
		// Start progress bar for single item
		private _success = [
			_actualTime,
			format ["Processing %1 (%2/%3)...", _name, _processedCount + 1, _quantity],
			{
				// On success - process single item
				params ["_processType", "_config"];
				_config params ["_name", "_outputItem", "_inputItem", "_inputAmount", "_outputAmount", "_time", "_skill", "_skillReq", "_xp", "_levelReq", "_tools", "_cost"];
				
				// Remove input items
				private _removed = [_inputItem, _inputAmount] call EDRP_fnc_removeItem;
				if (!_removed) exitWith {
					["Missing required materials"] call EDRP_fnc_hint;
					false
				};
				
				// Pay processing cost
				if (_cost > 0) then {
					if (EDRP_player_cash >= _cost) then {
						EDRP_player_cash = EDRP_player_cash - _cost;
					} else {
						["Not enough money for processing cost"] call EDRP_fnc_hint;
						// Return input items
						[_inputItem, _inputAmount] call EDRP_fnc_addItem;
						false
					};
				};
				
				// Calculate output amount based on skill
				private _skillLevel = if (_skill != "") then { EDRP_job_skills get _skill } else { 0 };
				private _actualOutput = _outputAmount;
				
				// Skill bonus (up to 25% more output)
				if (random 100 < (_skillLevel * 2)) then {
					_actualOutput = _actualOutput + 1;
				};
				
				// Add output items to inventory
				private _added = [_outputItem, _actualOutput] call EDRP_fnc_addItem;
				if (_added) then {
					[format ["Processed %1x %2", _actualOutput, [_outputItem] call EDRP_fnc_getItemName], "success"] call EDRP_fnc_hint;
					
					// Add XP
					if (_skill != "") then {
						[_skill, _xp] call EDRP_fnc_addJobXP;
					};
					
					// Update statistics
					private _statKey = format ["%1_processed", _processType];
					if (_statKey in EDRP_processing_stats) then {
						EDRP_processing_stats set [_statKey, (EDRP_processing_stats get _statKey) + _actualOutput];
					};
					
					// Play processing sound
					playSound "processing_machine";
					
					// Update job progress if active
					if (EDRP_job_active && EDRP_current_job == _skill) then {
						EDRP_job_progress = EDRP_job_progress + (_actualOutput * 3);
					};
					
					true
				} else {
					["Inventory full - processing failed"] call EDRP_fnc_hint;
					// Return input items
					[_inputItem, _inputAmount] call EDRP_fnc_addItem;
					false
				};
			},
			{
				// On failure/cancel
				["Processing cancelled"] call EDRP_fnc_hint;
				false
			},
			[_processType, _config]
		] call EDRP_fnc_progressBar;
		
		if (_success) then {
			_processedCount = _processedCount + 1;
		} else {
			// Stop processing on failure
			EDRP_processing_active = false;
		};
		
		// Play processing animation
		private _animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		switch (_processType) do {
			case "copper";
			case "iron";
			case "gold": { _animation = "Acts_carFixingWheel"; };
			case "salt";
			case "oil": { _animation = "Acts_Briefing_SB"; };
			case "diamond": { _animation = "AinvPknlMstpSnonWnonDnon_medic_1"; };
			case "fish": { _animation = "AinvPknlMstpSnonWnonDnon_medic_1"; };
		};
		
		player playAction _animation;
		
		// Small delay between items
		sleep 0.5;
	};
	
	// Processing complete
	EDRP_processing_active = false;
	
	if (_processedCount > 0) then {
		[format ["Processing complete! Processed %1/%2 items", _processedCount, _quantity], "success"] call EDRP_fnc_hint;
	};
};

// Check processing requirements
EDRP_fnc_checkProcessRequirements = {
	params ["_processType", "_config", "_quantity"];
	
	_config params ["_name", "_outputItem", "_inputItem", "_inputAmount", "_outputAmount", "_time", "_skill", "_skillReq", "_xp", "_levelReq", "_tools", "_cost"];
	
	// Check skill level requirement
	if (_skill != "" && _skillReq > 0) then {
		private _currentLevel = EDRP_job_skills get _skill;
		if (_currentLevel < _skillReq) exitWith {
			[format ["Requires %1 level %2 (current: %3)", _skill, _skillReq, _currentLevel], "error"] call EDRP_fnc_hint;
			false
		};
	};
	
	// Check player level requirement
	if (_levelReq > 0 && EDRP_player_level < _levelReq) exitWith {
		[format ["Requires player level %1 (current: %2)", _levelReq, EDRP_player_level], "error"] call EDRP_fnc_hint;
		false
	};
	
	// Check tool requirements
	{
		if !([_x] call EDRP_fnc_hasItem) exitWith {
			[format ["Requires %1", [_x] call EDRP_fnc_getItemName], "error"] call EDRP_fnc_hint;
			false
		};
	} forEach _tools;
	
	// Check input materials
	private _totalInputNeeded = _inputAmount * _quantity;
	if !([_inputItem, _totalInputNeeded] call EDRP_fnc_hasItem) exitWith {
		[format ["Need %1x %2", _totalInputNeeded, [_inputItem] call EDRP_fnc_getItemName], "error"] call EDRP_fnc_hint;
		false
	};
	
	// Check processing cost
	private _totalCost = _cost * _quantity;
	if (_totalCost > 0 && EDRP_player_cash < _totalCost) exitWith {
		[format ["Need $%1 for processing cost", [_totalCost] call EDRP_fnc_numberText], "error"] call EDRP_fnc_hint;
		false
	};
	
	// Check inventory space for output
	private _totalOutputSpace = _outputAmount * _quantity;
	if !([_outputItem, _totalOutputSpace] call EDRP_fnc_hasInventorySpace) exitWith {
		["Not enough inventory space for processed items"] call EDRP_fnc_hint;
		false
	};
	
	true
};

// Get processing configuration
EDRP_fnc_getProcessConfig = {
	params [["_processType", "", [""]]];
	
	private _configs = createHashMapFromArray [
		// Format: [name, outputItem, inputItem, inputAmount, outputAmount, time, skill, skillReq, xp, levelReq, [tools], cost]
		["copper", ["Copper Ingot", "copperingot", "copperore", 1, 1, 5, "smelting", 0, 8, 0, [], 0]],
		["iron", ["Iron Ingot", "ironingot", "ironore", 1, 1, 8, "smelting", 1, 12, 3, [], 500]],
		["salt", ["Refined Salt", "saltrefined", "salt", 2, 1, 4, "refining", 0, 5, 0, [], 0]],
		["diamond", ["Cut Diamond", "diamondcut", "diamond", 1, 1, 12, "gemcutting", 2, 40, 10, ["gemcutter"], 2000]],
		["gold", ["Gold Bar", "goldbar", "goldore", 2, 1, 10, "smelting", 2, 20, 8, [], 1000]],
		["oil", ["Processed Oil", "oilprocessed", "oilunprocessed", 1, 1, 8, "refining", 1, 15, 5, [], 800]],
		["fish", ["Cooked Fish", "fishcooked", "fishraw", 1, 1, 3, "cooking", 0, 5, 0, [], 0]],
		["marijuana", ["Processed Marijuana", "marijuanaprocessed", "marijuana", 1, 1, 6, "refining", 1, 25, 5, ["chemkit"], 1500]],
		["cocaine", ["Processed Cocaine", "cocaineprocessed", "cocaine", 1, 1, 8, "refining", 2, 35, 8, ["chemkit"], 2000]],
		["heroin", ["Processed Heroin", "heroinprocessed", "heroin", 1, 1, 10, "refining", 3, 45, 12, ["chemkit"], 2500]]
	];
	
	_configs getOrDefault [_processType, []]
};

// Check if player is in processing zone
EDRP_fnc_isInProcessZone = {
	params [["_zoneName", "", [""]]];
	
	private _pos = getMarkerPos _zoneName;
	private _size = getMarkerSize _zoneName;
	private _distance = player distance2D _pos;
	
	_distance <= (_size select 0)
};

// Get nearby processing zones
EDRP_fnc_getNearbyProcessZones = {
	params [["_range", 50, [0]]];
	
	private _nearbyZones = [];
	private _playerPos = getPos player;
	
	// Check all processing markers
	private _processMarkers = [
		"copper_processor_1", "copper_processor_2",
		"iron_processor_1",
		"salt_processor_1",
		"diamond_processor_1",
		"gold_processor_1",
		"oil_processor_1", "oil_processor_2",
		"fish_processor_1", "fish_processor_2", "fish_processor_3",
		"drug_processor_1", "drug_processor_2"
	];
	
	{
		private _markerPos = getMarkerPos _x;
		if (_playerPos distance2D _markerPos <= _range) then {
			private _processType = [_x] call EDRP_fnc_getZoneProcessType;
			_nearbyZones pushBack [_x, _processType, _playerPos distance2D _markerPos];
		};
	} forEach _processMarkers;
	
	// Sort by distance
	_nearbyZones sort true;
	
	_nearbyZones
};

// Get process type from zone name
EDRP_fnc_getZoneProcessType = {
	params [["_zoneName", "", [""]]];
	
	private _processType = "";
	
	switch (true) do {
		case (_zoneName find "copper" >= 0): { _processType = "copper"; };
		case (_zoneName find "iron" >= 0): { _processType = "iron"; };
		case (_zoneName find "salt" >= 0): { _processType = "salt"; };
		case (_zoneName find "diamond" >= 0): { _processType = "diamond"; };
		case (_zoneName find "gold" >= 0): { _processType = "gold"; };
		case (_zoneName find "oil" >= 0): { _processType = "oil"; };
		case (_zoneName find "fish" >= 0): { _processType = "fish"; };
		case (_zoneName find "drug" >= 0): { _processType = "drugs"; };
	};
	
	_processType
};

// Stop processing
EDRP_fnc_stopProcessing = {
	if (!EDRP_processing_active) exitWith {
		["No active processing to stop"] call EDRP_fnc_hint;
		false
	};
	
	EDRP_processing_active = false;
	EDRP_processing_type = "";
	EDRP_processing_progress = 0;
	EDRP_processing_location = "";
	
	// Stop progress bar
	[] call EDRP_fnc_stopProgressBar;
	
	["Processing stopped"] call EDRP_fnc_hint;
	
	true
};

// Initialize processing system on client
if (hasInterface) then {
	[] call EDRP_fnc_initProcessingSystem;
};
