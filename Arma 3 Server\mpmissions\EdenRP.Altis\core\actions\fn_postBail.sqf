//  File: fn_postBail.sqf
//	Author: <PERSON> "<PERSON>" Boardwine
//	Description: Called when the player attempts to post bail.
//	Needs to be revised.
private["_unit"];
_unit = _this select 1;

if(isNil "eden_cash") then {eden_cash = 0; eden_cache_cash = eden_random_cash_val;};
if(isNil "eden_atmcash") then {eden_atmcash = 0; eden_cache_atmcash = eden_random_cash_val;};

if((eden_cash + (eden_random_cash_val - 5000)) > eden_cache_cash || (eden_atmcash + (eden_random_cash_val - 5000)) > eden_cache_atmcash) exitWith {
	[["event","Hacked Cash"],["player",name player],["player_id",getPlayerUID player],["hackedcash",eden_cash - (eden_cache_cash - eden_random_cash_val)],["hackedbank",eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)],["location",getPos player]] call EDEN_fnc_logIt;
	[[profileName,format["Hacked Cash Detected! (Cash Hacked In = %1) (Bank Hacked In = %2)",eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDEN_fnc_notifyAdmins",-2,false] spawn EDEN_fnc_MP;
	[[1,player,[eden_cash - (eden_cache_cash - eden_random_cash_val),eden_atmcash - (eden_cache_atmcash - eden_random_cash_val)]],"EDENS_fnc_handleDisc",false,false] spawn EDEN_fnc_MP;
	["HackedMoney",false,false] call compile PreProcessFileLineNumbers "\a3\functions_f\Misc\fn_endMission.sqf";
};

if(eden_bail_paid) exitWith {};
if(isNil {life_bail_amount}) then {life_bail_amount = 3500;};
if(!isNil "eden_canpay_bail") exitWith {hint localize "STR_NOTF_Bail_Post"};
if(eden_atmcash < life_bail_amount && eden_cash < life_bail_amount) exitWith {hint format[localize "STR_NOTF_Bail_NotEnough",life_bail_amount];};

if(eden_cash >= life_bail_amount) then {
	eden_cash = eden_cash - life_bail_amount;
	eden_cache_cash = eden_cache_cash - life_bail_amount;
}else{
	eden_atmcash = eden_atmcash - life_bail_amount;
	eden_cache_atmcash = eden_cache_atmcash - life_bail_amount;
};

eden_bail_paid = true;
[1] call EDEN_fnc_ClupdatePartial;
[[0,"STR_NOTF_Bail_Bailed",true,[profileName]],"EDEN_fnc_broadcast",-2,false] spawn EDEN_fnc_MP;
