/*
	EdenRP Altis Life - Processing Configuration
	Author: EdenRP Development Team
	Description: Configuration for all item processing activities
	Version: 1.0.0
*/

class CfgProcess {
	// Copper Processing
	class copper {
		name = "Copper Ingot";
		item = "copperingot";
		requiredItem = "copperore";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 5;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "smelting";
		skillReq = 0;
		xp = 8;
		levelReq = 0;
		zones[] = {
			"copper_processor_1",
			"copper_processor_2"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 0;
		illegal = 0;
	};

	// Iron Processing
	class iron {
		name = "Iron Ingot";
		item = "ironingot";
		requiredItem = "ironore";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 8;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "smelting";
		skillReq = 1;
		xp = 12;
		levelReq = 3;
		zones[] = {
			"iron_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 500;
		illegal = 0;
	};

	// Salt Processing
	class salt {
		name = "Refined Salt";
		item = "saltrefined";
		requiredItem = "salt";
		requiredAmount = 2;
		returnedAmount = 1;
		processTime = 4;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "refining";
		skillReq = 0;
		xp = 5;
		levelReq = 0;
		zones[] = {
			"salt_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 0;
		illegal = 0;
	};

	// Diamond Processing
	class diamond {
		name = "Cut Diamond";
		item = "diamondcut";
		requiredItem = "diamond";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 12;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "gemcutting";
		skillReq = 2;
		xp = 40;
		levelReq = 10;
		zones[] = {
			"diamond_processor_1"
		};
		tools[] = {"gemcutter"};
		toolsReq = 1;
		cost = 2000;
		illegal = 0;
	};

	// Gold Processing
	class gold {
		name = "Gold Bar";
		item = "goldbar";
		requiredItem = "goldore";
		requiredAmount = 2;
		returnedAmount = 1;
		processTime = 10;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "smelting";
		skillReq = 2;
		xp = 20;
		levelReq = 8;
		zones[] = {
			"gold_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 1000;
		illegal = 0;
	};

	// Oil Processing
	class oil {
		name = "Processed Oil";
		item = "oilprocessed";
		requiredItem = "oilunprocessed";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 8;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "refining";
		skillReq = 1;
		xp = 15;
		levelReq = 5;
		zones[] = {
			"oil_processor_1",
			"oil_processor_2"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 800;
		illegal = 0;
	};

	// Heroin Processing (Illegal)
	class heroin {
		name = "Processed Heroin";
		item = "heroinprocessed";
		requiredItem = "heroinunprocessed";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 15;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "chemistry";
		skillReq = 2;
		xp = 25;
		levelReq = 8;
		zones[] = {
			"heroin_processor_1"
		};
		tools[] = {"chemkit"};
		toolsReq = 1;
		cost = 1500;
		illegal = 1;
	};

	// Cocaine Processing (Illegal)
	class cocaine {
		name = "Processed Cocaine";
		item = "cocaineprocessed";
		requiredItem = "cocaineunprocessed";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 12;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "chemistry";
		skillReq = 1;
		xp = 20;
		levelReq = 5;
		zones[] = {
			"cocaine_processor_1"
		};
		tools[] = {"chemkit"};
		toolsReq = 1;
		cost = 1200;
		illegal = 1;
	};

	// Marijuana Processing (Illegal)
	class marijuana {
		name = "Processed Marijuana";
		item = "marijuanaprocessed";
		requiredItem = "marijuanaunprocessed";
		requiredAmount = 2;
		returnedAmount = 1;
		processTime = 8;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "chemistry";
		skillReq = 0;
		xp = 10;
		levelReq = 3;
		zones[] = {
			"marijuana_processor_1",
			"marijuana_processor_2"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 500;
		illegal = 1;
	};

	// Fish Processing
	class fish {
		name = "Cooked Fish";
		item = "fishcooked";
		requiredItem = "fishraw";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 3;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "cooking";
		skillReq = 0;
		xp = 5;
		levelReq = 0;
		zones[] = {
			"fish_processor_1",
			"fish_processor_2",
			"fish_processor_3"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 0;
		illegal = 0;
	};

	// Turtle Processing (Illegal)
	class turtle {
		name = "Cooked Turtle Meat";
		item = "turtlecooked";
		requiredItem = "turtleraw";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 8;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "cooking";
		skillReq = 1;
		xp = 25;
		levelReq = 8;
		zones[] = {
			"turtle_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 200;
		illegal = 1;
	};

	// Glass Processing
	class glass {
		name = "Glass";
		item = "glass";
		requiredItem = "sand";
		requiredAmount = 3;
		returnedAmount = 1;
		processTime = 6;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "glassmaking";
		skillReq = 0;
		xp = 8;
		levelReq = 0;
		zones[] = {
			"glass_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 300;
		illegal = 0;
	};

	// Cement Processing
	class cement {
		name = "Cement";
		item = "cement";
		requiredItem = "rock";
		requiredAmount = 4;
		returnedAmount = 1;
		processTime = 8;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "construction";
		skillReq = 0;
		xp = 10;
		levelReq = 0;
		zones[] = {
			"cement_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 400;
		illegal = 0;
	};

	// Plastic Processing
	class plastic {
		name = "Plastic";
		item = "plastic";
		requiredItem = "oilprocessed";
		requiredAmount = 2;
		returnedAmount = 1;
		processTime = 10;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "manufacturing";
		skillReq = 1;
		xp = 15;
		levelReq = 5;
		zones[] = {
			"plastic_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 600;
		illegal = 0;
	};

	// Rubber Processing
	class rubber {
		name = "Rubber";
		item = "rubber";
		requiredItem = "oilprocessed";
		requiredAmount = 1;
		returnedAmount = 1;
		processTime = 6;
		animation = "AinvPknlMstpSnonWnonDnon_medic_1";
		skill = "manufacturing";
		skillReq = 0;
		xp = 8;
		levelReq = 0;
		zones[] = {
			"rubber_processor_1"
		};
		tools[] = {};
		toolsReq = 0;
		cost = 400;
		illegal = 0;
	};
};
