#include "..\..\macro.h"
//  File: fn_buyDopamineCrate.sqf
//	Author: Ozadu
//	Description: Takes money from the player and sends a request for a crate to the server.

params[
	["_target",obj<PERSON>ull,[obj<PERSON><PERSON>]],
	["_caller",obj<PERSON><PERSON>,[obj<PERSON><PERSON>]],
	["_id",-1,[0]],
	["_args",[],[[]]]
];

private _isNeo = false;
private _exit = false;
private _targetPos = [0,0,0];

if(playerSide != independent) exitWith {};
if(__GETC__(life_medicLevel) < 4) exitWith {hint "This feature is currently restricted to Search & Rescue+"};
_price = 50000;
_confirm = [format["Do you wish to purchase a dopamine crate for $%1?",_price],"Purchase Dopamine Crate","Buy",true] call BIS_fnc_guiMessage;
if(!_confirm) exitWith {};

if ((player distance2D [11104.9,13092.6,0.7526]) < 100) then {_isNeo = true};

/*Check if anything is in the way of spawn*/
if !(_isNeo) then {
	_targetPos = getPosATL _target;
	_hs = nearestObjects[_targetPos,["Land_Hospital_side2_F"],175];
	if(count _hs == 0) exitWith {};
	_hs = _hs select 0;
	_crateType = "Land_Cargo10_yellow_F";
	_crate = _crateType createVehicleLocal [0,0,0];
	_crate setPosATL (_hs modelToWorld [12.6616,-3.1123,10.6315]);
	_collides = [_crate] call EDEN_fnc_objectCollides;
	deleteVehicle _crate;
	if(_collides) exitWith {_exit = true};
} else {
	_targetPos = [12104.4,14068.8,-0.00708389];
	_crateType = "Land_Cargo10_yellow_F";
	_crate = _crateType createVehicleLocal [0,0,0];
	_crate setPos [12104.4,14068.8,-0.00708389];
	_collides = [_crate] call EDEN_fnc_objectCollides;
	deleteVehicle _crate;
	if(_collides) exitWith {_exit = true};
};

if(_exit) exitWith {hint "Something is blocking the spawn"};

//take some money
if(_price > eden_atmcash) exitWith {hint localize "STR_NOTF_NotEnoughMoney"};
eden_atmcash = eden_atmcash - _price;
eden_cache_atmcash = eden_cache_atmcash - _price;
[1] call EDEN_fnc_ClupdatePartial;
hint format["Dopamine crate has been purchased for a price of $%1. It will spawn on top of the hospital.",_price];

[[player,_targetPos,_isNeo],"EDENS_fnc_spawnDopamineCrate",false,false] call EDEN_fnc_mp;
