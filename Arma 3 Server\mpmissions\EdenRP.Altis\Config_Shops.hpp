/*
	EdenRP Altis Life - Shop Configuration
	Author: EdenRP Development Team
	Description: Configuration for all shops and markets
	Version: 1.0.0
*/

class CfgShops {
	// General Store
	class general {
		name = "General Store";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"water", "Water Bottle", 15, -1, ""},
			{"apple", "Apple", 25, -1, ""},
			{"peach", "Peach", 30, -1, ""},
			{"bread", "Bread", 45, -1, ""},
			{"pickaxe", "Pickaxe", 750, -1, ""},
			{"fishingrod", "Fishing Rod", 850, -1, ""},
			{"huntingknife", "Hunting Knife", 1250, -1, ""},
			{"chemkit", "Chemistry Kit", 2500, -1, ""},
			{"oilpump", "Oil Pump", 1850, -1, ""},
			{"gemcutter", "Gem Cutter", 3500, -1, ""}
		};
	};

	// Market - Copper
	class market_copper {
		name = "Copper Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"copperingot", "Copper Ingot", 1450, 1, ""}
		};
	};

	// Market - Iron
	class market_iron {
		name = "Iron Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"ironingot", "Iron Ingot", 2850, 1, ""}
		};
	};

	// Market - Salt
	class market_salt {
		name = "Salt Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"saltrefined", "Refined Salt", 1250, 1, ""}
		};
	};

	// Market - Diamond
	class market_diamond {
		name = "Diamond Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"diamondcut", "Cut Diamond", 3500, 1, ""}
		};
	};

	// Market - Gold
	class market_gold {
		name = "Gold Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"goldbar", "Gold Bar", 2750, 1, ""}
		};
	};

	// Market - Oil
	class market_oil {
		name = "Oil Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"oilprocessed", "Processed Oil", 3250, 1, ""}
		};
	};

	// Market - Fish
	class market_fish {
		name = "Fish Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"fishcooked", "Cooked Fish", 850, 1, ""}
		};
	};

	// Black Market - Drugs
	class blackmarket_drugs {
		name = "Black Market";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"heroinprocessed", "Processed Heroin", 4500, 1, ""},
			{"cocaineprocessed", "Processed Cocaine", 3850, 1, ""},
			{"marijuanaprocessed", "Processed Marijuana", 2250, 1, ""},
			{"turtlecooked", "Cooked Turtle Meat", 6500, 1, ""}
		};
	};

	// Clothing Store
	class clothing {
		name = "Clothing Store";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"U_C_Poloshirt_blue", "Blue Polo Shirt", 250, -1, ""},
			{"U_C_Poloshirt_burgundy", "Burgundy Polo Shirt", 275, -1, ""},
			{"U_C_Poloshirt_stripped", "Stripped Polo Shirt", 225, -1, ""},
			{"U_C_Poloshirt_tricolour", "Tricolour Polo Shirt", 350, -1, ""},
			{"U_C_Poloshirt_salmon", "Salmon Polo Shirt", 175, -1, ""},
			{"U_C_Poloshirt_redwhite", "Red/White Polo Shirt", 300, -1, ""},
			{"U_C_Poor_1", "Rag tagged clothes", 250, -1, ""},
			{"U_IG_Guerilla2_1", "Green stripped shirt and Pants", 650, -1, ""},
			{"U_IG_Guerilla2_3", "Brown stripped shirt and Pants", 655, -1, ""},
			{"U_OG_Guerilla2_1", "Sasquatch clothes", 1200, -1, ""},
			{"U_C_HunterBody_grn", "Hunting clothes", 1500, -1, ""},
			{"U_C_WorkerCoveralls", "Mechanic Coveralls", 2500, -1, ""}
		};
	};

	// Gun Store
	class gun_store {
		name = "Gun Store";
		side = "civ";
		license = "gun";
		level = 0;
		items[] = {
			{"hgun_Rook40_F", "Rook-40 9mm", 6500, -1, ""},
			{"hgun_Pistol_heavy_02_F", "Zubr .45 ACP", 9850, -1, ""},
			{"16Rnd_9x21_Mag", "9mm 16Rnd Mag", 25, -1, ""},
			{"6Rnd_45ACP_Cylinder", ".45 ACP 6Rnd Cylinder", 50, -1, ""},
			{"arifle_sdar_F", "SDAR 5.56mm", 20000, -1, "boat"},
			{"20Rnd_556x45_UW_mag", "5.56mm 20Rnd Underwater Mag", 125, -1, ""}
		};
	};

	// Rebel Outpost
	class rebel {
		name = "Rebel Outpost";
		side = "civ";
		license = "rebel";
		level = 0;
		items[] = {
			{"arifle_TRG21_F", "TRG-21 5.56mm", 25000, -1, ""},
			{"arifle_Mk20_F", "Mk20 5.56mm", 30000, -1, ""},
			{"arifle_Katiba_F", "Katiba 6.5mm", 35000, -1, ""},
			{"srifle_DMR_01_F", "Rahim 7.62mm", 50000, -1, ""},
			{"arifle_SDAR_F", "SDAR 5.56mm", 20000, -1, ""},
			{"30Rnd_556x45_Stanag", "5.56mm 30Rnd STANAG", 300, -1, ""},
			{"30Rnd_65x39_caseless_green", "6.5mm 30Rnd Caseless", 275, -1, ""},
			{"10Rnd_762x54_Mag", "7.62mm 10Rnd Mag", 500, -1, ""},
			{"20Rnd_556x45_UW_mag", "5.56mm 20Rnd Underwater Mag", 125, -1, ""}
		};
	};

	// Police Armory
	class cop_basic {
		name = "Police Armory";
		side = "cop";
		license = "";
		level = 0;
		items[] = {
			{"hgun_P07_snds_F", "P07 9mm (Silenced)", 0, -1, ""},
			{"hgun_Pistol_heavy_01_snds_F", "4-five .45 ACP (Silenced)", 0, -1, ""},
			{"SMG_02_ACO_F", "Sting 9mm", 0, -1, ""},
			{"arifle_sdar_F", "SDAR 5.56mm (Taser)", 0, -1, ""},
			{"16Rnd_9x21_Mag", "9mm 16Rnd Mag", 0, -1, ""},
			{"11Rnd_45ACP_Mag", ".45 ACP 11Rnd Mag", 0, -1, ""},
			{"30Rnd_9x21_Mag", "9mm 30Rnd Mag", 0, -1, ""},
			{"20Rnd_556x45_UW_mag", "5.56mm 20Rnd Taser Mag", 0, -1, ""}
		};
	};

	// Police Advanced Armory
	class cop_advanced {
		name = "Police Advanced Armory";
		side = "cop";
		license = "";
		level = 3;
		items[] = {
			{"arifle_MX_F", "MX 6.5mm", 0, -1, ""},
			{"arifle_MXC_F", "MXC 6.5mm", 0, -1, ""},
			{"arifle_MXM_F", "MXM 6.5mm", 0, -1, ""},
			{"srifle_EBR_F", "Mk18 ABR 7.62mm", 0, -1, ""},
			{"30Rnd_65x39_caseless_mag", "6.5mm 30Rnd STANAG", 0, -1, ""},
			{"20Rnd_762x51_Mag", "7.62mm 20Rnd Mag", 0, -1, ""}
		};
	};

	// Medical Supplies
	class medical {
		name = "Medical Supplies";
		side = "med";
		license = "";
		level = 0;
		items[] = {
			{"ToolKit", "Medical Kit", 0, -1, ""},
			{"Medikit", "First Aid Kit", 0, -1, ""},
			{"ItemGPS", "GPS", 0, -1, ""},
			{"Binocular", "Binoculars", 0, -1, ""},
			{"NVGoggles_OPFOR", "Night Vision Goggles", 0, -1, ""}
		};
	};

	// Vehicle Rental
	class vehicle_rental {
		name = "Vehicle Rental";
		side = "civ";
		license = "driver";
		level = 0;
		items[] = {
			{"C_Hatchback_01_F", "Hatchback (Rental)", 150, -1, ""},
			{"C_Offroad_01_F", "Offroad (Rental)", 200, -1, ""},
			{"C_SUV_01_F", "SUV (Rental)", 300, -1, ""},
			{"C_Quadbike_01_F", "Quad Bike (Rental)", 100, -1, ""}
		};
	};

	// Boat Rental
	class boat_rental {
		name = "Boat Rental";
		side = "civ";
		license = "boat";
		level = 0;
		items[] = {
			{"C_Rubberboat", "Rescue Boat (Rental)", 300, -1, ""},
			{"C_Boat_Civil_01_F", "Motor Boat (Rental)", 850, -1, ""}
		};
	};

	// Helicopter Rental
	class heli_rental {
		name = "Helicopter Rental";
		side = "civ";
		license = "pilot";
		level = 0;
		items[] = {
			{"B_Heli_Light_01_F", "Hummingbird (Rental)", 2500, -1, ""}
		};
	};

	// Donator Shop
	class donator {
		name = "Donator Shop";
		side = "civ";
		license = "";
		level = 0;
		items[] = {
			{"U_I_CombatUniform", "Combat Fatigues (AAF)", 5000, -1, "donator"},
			{"U_B_CombatUniform_mcam", "Combat Fatigues (MTP)", 5000, -1, "donator"},
			{"U_O_CombatUniform_ocamo", "Combat Fatigues (Hex)", 5000, -1, "donator"},
			{"U_I_pilotCoveralls", "Pilot Coveralls", 7500, -1, "donator"},
			{"U_I_HeliPilotCoveralls", "Heli Pilot Coveralls", 10000, -1, "donator"}
		};
	};
};
