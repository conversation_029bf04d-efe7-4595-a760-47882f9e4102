/*
	EdenRP Altis Life - Territory System (Adapted from Olympus)
	Author: EdenRP Development Team
	Description: Gang territory capture and control system
	Version: 1.0.0
*/

// Initialize territory system
EDRP_fnc_initTerritorySystem = {
	// Territory state variables
	EDRP_capturing_territory = false;
	EDRP_territory_data = createHashMap;
	EDRP_territory_markers = [];
	
	// Load territory configuration
	[] call EDRP_fnc_loadTerritoryConfig;
	
	// Initialize territory markers
	[] call EDRP_fnc_initTerritoryMarkers;
	
	["Territory system initialized"] call EDRP_fnc_logInfo;
};

// Load territory configuration
EDRP_fnc_loadTerritoryConfig = {
	// Territory definitions (adapted from Olympus)
	EDRP_territories = [
		["Weed Processing", "weed_processing", [4500, 4500, 0], "drug", 100, 0.5],
		["Cocaine Processing", "cocaine_processing", [5500, 5500, 0], "drug", 100, 0.5],
		["Heroin Processing", "heroin_processing", [6500, 6500, 0], "drug", 100, 0.5],
		["Meth Lab", "meth_lab", [7500, 7500, 0], "drug", 100, 0.5],
		["Arms Depot", "arms_depot", [8500, 8500, 0], "arms", 100, 0.5],
		["Money Laundry", "money_laundry", [9500, 9500, 0], "money", 100, 0.5],
		["Smuggling Port", "smuggling_port", [10500, 10500, 0], "smuggle", 100, 0.5],
		["Black Market", "black_market", [11500, 11500, 0], "market", 100, 0.5]
	];
	
	// Territory benefits
	EDRP_territory_benefits = createHashMapFromArray [
		["drug", ["25% faster drug processing", "15% higher drug prices", "Access to advanced recipes"]],
		["arms", ["Access to advanced weapons", "20% weapon discount", "Ammunition manufacturing"]],
		["money", ["Money laundering services", "10% bank interest bonus", "Offshore accounts"]],
		["smuggle", ["Import/export operations", "Contraband trading", "Vehicle modifications"]],
		["market", ["Black market access", "Rare item trading", "Information brokering"]]
	];
	
	// Capture requirements
	EDRP_capture_requirements = createHashMapFromArray [
		["min_members", 2], // Minimum gang members in area
		["capture_time", 300], // 5 minutes to capture
		["contest_time", 60], // 1 minute contest period
		["capture_radius", 100] // Capture area radius
	];
};

// Initialize territory markers
EDRP_fnc_initTerritoryMarkers = {
	{
		_x params ["_name", "_id", "_position", "_type", "_radius", "_progress"];
		
		// Create territory marker
		private _marker = createMarker [format ["%1_territory", _id], _position];
		_marker setMarkerType "loc_Bunker";
		_marker setMarkerSize [2, 2];
		_marker setMarkerColor "ColorRed";
		_marker setMarkerText format [" %1 (Neutral)", _name];
		
		// Store territory data
		EDRP_territory_data set [_id, [0, "Neutral", _progress, _position, _radius]];
		EDRP_territory_markers pushBack _marker;
		
	} forEach EDRP_territories;
};

// Start territory capture
EDRP_fnc_captureTerritory = {
	params [["_territoryID", "", [""]]];
	
	if (_territoryID == "") exitWith {
		["Invalid territory ID"] call EDRP_fnc_hint;
		false
	};
	
	// Check if player is in a gang
	if (count EDRP_gang_data == 0) exitWith {
		["You must be in a gang to capture territory"] call EDRP_fnc_hint;
		false
	};
	
	// Check if already capturing
	if (EDRP_capturing_territory) exitWith {
		["You are already capturing territory"] call EDRP_fnc_hint;
		false
	};
	
	// Get territory data
	private _territoryData = EDRP_territory_data get _territoryID;
	if (isNil "_territoryData") exitWith {
		["Territory not found"] call EDRP_fnc_hint;
		false
	};
	
	_territoryData params ["_ownerGangID", "_ownerGangName", "_captureProgress", "_position", "_radius"];
	
	// Check if player is in capture area
	if (player distance _position > _radius) exitWith {
		["You must be within the territory to capture it"] call EDRP_fnc_hint;
		false
	};
	
	// Check for required weapons (adapted from Olympus)
	private _hasValidWeapon = false;
	private _validWeapons = ["arifle_Katiba_F", "arifle_MX_F", "arifle_TRG21_F", "arifle_Mk20_F"];
	
	{
		if (currentWeapon player == _x) exitWith {
			_hasValidWeapon = true;
		};
	} forEach _validWeapons;
	
	if (!_hasValidWeapon) exitWith {
		["You need a rifle to capture territory"] call EDRP_fnc_hint;
		false
	};
	
	// Start capture process
	EDRP_capturing_territory = true;
	player setVariable ["cappingCart", true, true];
	
	// Show capture UI
	[] call EDRP_fnc_showCaptureUI;
	
	// Start capture loop
	[_territoryID] spawn EDRP_fnc_captureLoop;
	
	true
};

// Territory capture loop
EDRP_fnc_captureLoop = {
	params [["_territoryID", "", [""]]];
	
	private _territoryData = EDRP_territory_data get _territoryID;
	_territoryData params ["_ownerGangID", "_ownerGangName", "_captureProgress", "_position", "_radius"];
	
	private _gangID = EDRP_gang_data select 0;
	private _gangName = EDRP_gang_data select 1;
	
	private _startTime = time;
	private _lastUpdate = 0;
	
	while {EDRP_capturing_territory && player distance _position <= _radius && alive player} do {
		// Count nearby gang members
		private _nearbyMembers = 0;
		private _nearbyGangs = [];
		
		{
			if (_x distance _position <= _radius && alive _x) then {
				private _playerGangData = _x getVariable ["gang_data", []];
				if (count _playerGangData > 0) then {
					private _playerGangID = _playerGangData select 0;
					if (_playerGangID == _gangID) then {
						_nearbyMembers = _nearbyMembers + 1;
					} else {
						if !(_playerGangID in _nearbyGangs) then {
							_nearbyGangs pushBack _playerGangID;
						};
					};
				};
			};
		} forEach allPlayers;
		
		// Check minimum members requirement
		if (_nearbyMembers < (EDRP_capture_requirements get "min_members")) exitWith {
			["Not enough gang members in the area"] call EDRP_fnc_hint;
			EDRP_capturing_territory = false;
		};
		
		// Check for contested territory
		private _contested = count _nearbyGangs > 0;
		
		// Calculate capture rate
		private _captureRate = 0.001; // Base rate
		if (_nearbyMembers > 2) then { _nearbyMembers = 2; }; // Cap bonus
		_captureRate = _captureRate + (_nearbyMembers * 0.000375);
		
		// Determine capture direction
		private _title = "";
		if (_ownerGangID != _gangID && _captureProgress > 0) then {
			// Uncapping enemy territory
			_captureRate = _captureRate * -1;
			_title = "Uncapping...";
		} else {
			// Capturing neutral or own territory
			if (_ownerGangID != _gangID) then {
				// Reset ownership for neutral capture
				_ownerGangID = _gangID;
				_ownerGangName = _gangName;
				_captureProgress = 0;
			};
			_title = "Capturing...";
		};
		
		// Apply contested penalty
		if (_contested) then {
			_captureRate = _captureRate * 0.5;
			_title = "Contested...";
		};
		
		// Update capture progress
		_captureProgress = _captureProgress + _captureRate;
		_captureProgress = _captureProgress max 0 min 1;
		
		// Update territory data
		EDRP_territory_data set [_territoryID, [_ownerGangID, _ownerGangName, _captureProgress, _position, _radius]];
		
		// Update UI every 5 seconds
		if (time > _lastUpdate + 5) then {
			[] call EDRP_fnc_updateCaptureUI;
			_lastUpdate = time;
		};
		
		// Check if capture is complete
		if (_captureProgress >= 1) exitWith {
			// Territory captured
			[_territoryID, _gangID, _gangName] call EDRP_fnc_territoryCapture;
			EDRP_capturing_territory = false;
		};
		
		sleep 0.1;
	};
	
	// Clean up
	player setVariable ["cappingCart", false, true];
	EDRP_capturing_territory = false;
	[] call EDRP_fnc_hideCaptureUI;
};

// Territory captured
EDRP_fnc_territoryCapture = {
	params [
		["_territoryID", "", [""]],
		["_gangID", 0, [0]],
		["_gangName", "", [""]]
	];
	
	// Update territory ownership
	private _territoryData = EDRP_territory_data get _territoryID;
	_territoryData set [0, _gangID];
	_territoryData set [1, _gangName];
	_territoryData set [2, 1.0];
	EDRP_territory_data set [_territoryID, _territoryData];
	
	// Update marker
	private _marker = format ["%1_territory", _territoryID];
	_marker setMarkerColor "ColorGreen";
	_marker setMarkerText format [" %1 (%2)", [_territoryID] call EDRP_fnc_getTerritoryName, _gangName];
	
	// Notify gang members
	private _territoryName = [_territoryID] call EDRP_fnc_getTerritoryName;
	private _message = format ["%1 has captured %2!", _gangName, _territoryName];
	[_message, _gangID] remoteExec ["EDRP_fnc_broadcastGangMessage", 2];
	
	// Update statistics
	EDRP_gang_stats set ["territory_captured", (EDRP_gang_stats get "territory_captured") + 1];
	
	// Show capture success
	[format ["Territory captured: %1", _territoryName], "success"] call EDRP_fnc_hint;
	
	// Log capture
	[format ["TERRITORY: %1 captured %2", _gangName, _territoryName]] call EDRP_fnc_logInfo;
};

// Show capture UI
EDRP_fnc_showCaptureUI = {
	// Create capture progress display
	5 cutRsc ["EDRP_CaptureProgress", "PLAIN"];
};

// Update capture UI
EDRP_fnc_updateCaptureUI = {
	private _display = uiNamespace getVariable ["EDRP_CaptureProgress", displayNull];
	if (isNull _display) exitWith {};
	
	// Update progress bar and text
	private _progressCtrl = _display displayCtrl 1000;
	private _textCtrl = _display displayCtrl 1001;
	
	// Get current territory being captured
	private _currentTerritory = "";
	{
		private _territoryData = EDRP_territory_data get _x;
		if ((_territoryData select 0) == (EDRP_gang_data select 0) && (_territoryData select 2) < 1) exitWith {
			_currentTerritory = _x;
		};
	} forEach (keys EDRP_territory_data);
	
	if (_currentTerritory != "") then {
		private _territoryData = EDRP_territory_data get _currentTerritory;
		private _progress = _territoryData select 2;
		private _territoryName = [_currentTerritory] call EDRP_fnc_getTerritoryName;
		
		_progressCtrl progressSetPosition _progress;
		_textCtrl ctrlSetText format ["Capturing %1: %2%", _territoryName, round(_progress * 100)];
	};
};

// Hide capture UI
EDRP_fnc_hideCaptureUI = {
	5 cutText ["", "PLAIN DOWN"];
};

// Get territory name
EDRP_fnc_getTerritoryName = {
	params [["_territoryID", "", [""]]];
	
	{
		if ((_x select 1) == _territoryID) exitWith {
			_x select 0
		};
	} forEach EDRP_territories;
	
	"Unknown Territory"
};

// Get territory benefits
EDRP_fnc_getTerritoryBenefits = {
	params [["_territoryType", "", [""]]];
	
	EDRP_territory_benefits getOrDefault [_territoryType, []]
};

// Check if gang controls territory
EDRP_fnc_gangControlsTerritory = {
	params [
		["_gangID", 0, [0]],
		["_territoryID", "", [""]]
	];
	
	private _territoryData = EDRP_territory_data get _territoryID;
	if (isNil "_territoryData") exitWith { false };
	
	(_territoryData select 0) == _gangID && (_territoryData select 2) >= 1
};

// Get gang territories
EDRP_fnc_getGangTerritories = {
	params [["_gangID", 0, [0]]];
	
	private _territories = [];
	
	{
		private _territoryData = EDRP_territory_data get _x;
		if ((_territoryData select 0) == _gangID && (_territoryData select 2) >= 1) then {
			_territories pushBack _x;
		};
	} forEach (keys EDRP_territory_data);
	
	_territories
};

// Territory income system
EDRP_fnc_territoryIncome = {
	if (count EDRP_gang_data == 0) exitWith {};
	
	private _gangID = EDRP_gang_data select 0;
	private _territories = [_gangID] call EDRP_fnc_getGangTerritories;
	
	if (count _territories == 0) exitWith {};
	
	private _totalIncome = 0;
	
	{
		// Base income per territory
		private _income = 5000; // $5,000 per territory per hour
		
		// Territory type bonuses
		private _territoryType = "";
		{
			if ((_x select 1) == _territoryID) exitWith {
				_territoryType = _x select 3;
			};
		} forEach EDRP_territories;
		
		switch (_territoryType) do {
			case "drug": { _income = _income * 1.5; };
			case "arms": { _income = _income * 1.3; };
			case "money": { _income = _income * 2.0; };
			case "smuggle": { _income = _income * 1.2; };
			case "market": { _income = _income * 1.8; };
		};
		
		_totalIncome = _totalIncome + _income;
		
	} forEach _territories;
	
	// Add to gang bank
	EDRP_gang_bank = EDRP_gang_bank + _totalIncome;
	
	// Notify gang members
	if (_totalIncome > 0) then {
		private _message = format ["Territory income: $%1 added to gang bank", [_totalIncome] call EDRP_fnc_numberText];
		[_message, _gangID] remoteExec ["EDRP_fnc_broadcastGangMessage", 2];
	};
};

// Add territory actions
EDRP_fnc_addTerritoryActions = {
	{
		_x params ["_name", "_id", "_position", "_type", "_radius", "_progress"];
		
		// Add capture action
		player addAction [
			format ["<t color='#FF0000'>Capture %1</t>", _name],
			{
				params ["_target", "_caller", "_actionId", "_arguments"];
				_arguments params ["_territoryID"];
				[_territoryID] call EDRP_fnc_captureTerritory;
			},
			[_id],
			6,
			true,
			true,
			"",
			format ["player distance %1 < %2 && count EDRP_gang_data > 0 && !EDRP_capturing_territory", _position, _radius]
		];
		
	} forEach EDRP_territories;
};

// Initialize territory system on client
if (hasInterface) then {
	[] call EDRP_fnc_initTerritorySystem;
	
	// Add territory actions
	[] call EDRP_fnc_addTerritoryActions;
	
	// Start territory income timer (every hour)
	[] spawn {
		while {true} do {
			sleep 3600; // 1 hour
			[] call EDRP_fnc_territoryIncome;
		};
	};
};
