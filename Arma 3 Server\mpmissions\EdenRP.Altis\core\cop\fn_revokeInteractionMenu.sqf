//  File: fn_revokeInteractionMenu.sqf
//	Author: <PERSON> "<PERSON>" Boardwine

//	Description: Replaces the mass for various cop actions towards another player.
#include <interaction.h>
private["_display","_Btn1","_Btn2","_Btn3","_Btn4","_Btn5","_Btn6","_Btn7","_Btn8"];
if(!dialog) then {
	["pInteraction_Menu"] call EDEN_fnc_createDialog;
};
disableSerialization;
params [
	["_curTarget",objNull,[objNull]]
];
if(isNull _curTarget) exitWith {closeDialog 0;}; //Bad target

if(!isPlayer _curTarget && side _curTarget isEqualTo civilian) exitWith {closeDialog 0;}; //Bad side check?
_display = findDisplay 37400;
_Btn1 = _display displayCtrl Btn1;
_Btn2 = _display displayCtrl Btn2;
_Btn3 = _display displayCtrl Btn3;
_Btn4 = _display displayCtrl Btn4;
_Btn5 = _display displayCtrl Btn5;
_Btn6 = _display displayCtrl Btn6;
_Btn7 = _display displayCtrl Btn7;
_Btn8 = _display displayCtrl Btn8;
life_pInact_curTarget = _curTarget;

{_x ctrlShow true;} foreach [_Btn3,_Btn4,_Btn5];
{_x ctrlShow false;} foreach [_Btn6,_Btn7,_Btn8];

//Set Seize drivers license
_Btn1 ctrlSetText "Revoke Drivers License";
_Btn1 buttonSetAction "[[1],""EDEN_fnc_revokePlayerLicense"",life_pInact_curTarget,FALSE] spawn EDEN_fnc_MP; hint ""Drivers license revoked"";";

//Set Seize firearms license
_Btn2 ctrlSetText "Revoke Firearms License";
_Btn2 buttonSetAction "[[2],""EDEN_fnc_revokePlayerLicense"",life_pInact_curTarget,FALSE] spawn EDEN_fnc_MP; hint ""Firearms license revoked"";";

//Set Seize wpl license
_Btn3 ctrlSetText "Revoke WP License";
_Btn3 buttonSetAction "[[3],""EDEN_fnc_revokePlayerLicense"",life_pInact_curTarget,FALSE] spawn EDEN_fnc_MP; hint ""Workers Protection license revoked"";";

//Set Seize vigilante license
_Btn4 ctrlSetText "Revoke Vigilante License";
_Btn4 buttonSetAction "[[4,player],""EDEN_fnc_revokePlayerLicense"",life_pInact_curTarget,FALSE] spawn EDEN_fnc_MP; hint ""Vigilante license revoked"";";

//Set Downgrade Vigilante License
_Btn5 ctrlSetText "Downgrade Vigilante Tier";
_Btn5 buttonSetAction "[[5,player],""EDEN_fnc_revokePlayerLicense"",life_pInact_curTarget,FALSE] spawn EDEN_fnc_MP; hint""Vigilante tier downgraded"";";