/*
	EdenRP Altis Life - Update Main Menu Function
	Author: EdenRP Development Team
	Description: Updates the main menu with current player information
	Version: 1.0.0
*/

disableSerialization;

private _dialog = findDisplay 3000;
if (isNull _dialog) exitWith {};

// Get controls
private _playerNameText = _dialog displayCtrl 3002;
private _playerLevelText = _dialog displayCtrl 3003;
private _playerRankText = _dialog displayCtrl 3004;
private _playerPlaytimeText = _dialog displayCtrl 3005;
private _playerLocationText = _dialog displayCtrl 3006;
private _cashValue = _dialog displayCtrl 3007;
private _bankValue = _dialog displayCtrl 3008;
private _quickActionsList = _dialog displayCtrl 3001;
private _mainContentList = _dialog displayCtrl 3010;
private _nearbyPlayersCombo = _dialog displayCtrl 3021;
private _statusText = _dialog displayCtrl 3030;
private _infoText = _dialog displayCtrl 3031;

// Update player information
_playerNameText ctrlSetText format ["%1", name player];
_playerLevelText ctrlSetText format ["Level: %1", EDRP_player_level];
_playerRankText ctrlSetText format ["Rank: %1", EDRP_player_rank];

// Calculate and display playtime
private _playtimeMinutes = EDRP_player_playtime / 60;
private _hours = floor (_playtimeMinutes / 60);
private _minutes = floor (_playtimeMinutes % 60);
_playerPlaytimeText ctrlSetText format ["Playtime: %1h %2m", _hours, _minutes];

// Get current location
private _location = [getPos player] call EDRP_fnc_getLocationName;
_playerLocationText ctrlSetText format ["Location: %1", _location];

// Update money displays
private _cashText = [EDRP_player_cash] call EDRP_fnc_numberText;
private _bankText = [EDRP_player_bank] call EDRP_fnc_numberText;

_cashValue ctrlSetStructuredText parseText format [
	"<t align='center' size='1.2' color='#00FF00'>$%1</t>", 
	_cashText
];

_bankValue ctrlSetStructuredText parseText format [
	"<t align='center' size='1.2' color='#0080FF'>$%1</t>", 
	_bankText
];

// Update quick actions list
lbClear _quickActionsList;

private _quickActions = [
	["Give Money", "money", "Give cash to nearby players"],
	["Check Time", "time", "View current server time"],
	["Check Stats", "stats", "View your statistics"],
	["Toggle HUD", "hud", "Show/hide HUD elements"],
	["Surrender", "surrender", "Surrender to police"],
	["Flip Vehicle", "flip", "Flip nearby vehicle"],
	["Repair Vehicle", "repair", "Repair nearby vehicle"],
	["Refuel Vehicle", "refuel", "Refuel nearby vehicle"]
];

// Add faction-specific quick actions
switch (EDRP_player_faction) do {
	case "police": {
		_quickActions pushBack ["Radar Gun", "radar", "Check vehicle speeds"];
		_quickActions pushBack ["Breathalyzer", "breathalyzer", "Test for alcohol"];
		_quickActions pushBack ["Search Player", "search", "Search nearby player"];
		_quickActions pushBack ["Restrain Player", "restrain", "Restrain nearby player"];
	};
	
	case "medical": {
		_quickActions pushBack ["Revive Player", "revive", "Revive nearby player"];
		_quickActions pushBack ["Heal Player", "heal", "Heal nearby player"];
		_quickActions pushBack ["Medical Bag", "medbag", "Deploy medical equipment"];
	};
	
	case "civilian": {
		_quickActions pushBack ["Rob Player", "rob", "Rob nearby player"];
		_quickActions pushBack ["Lockpick Vehicle", "lockpick", "Attempt to lockpick vehicle"];
	};
};

{
	_x params ["_name", "_action", "_description"];
	private _index = _quickActionsList lbAdd _name;
	_quickActionsList lbSetData [_index, _action];
	_quickActionsList lbSetTooltip [_index, _description];
	_quickActionsList lbSetPicture [_index, format ["images\icons\%1.paa", _action]];
} forEach _quickActions;

// Update main content based on current tab
private _currentTab = EDRP_mainmenu_current_tab;
if (isNil "_currentTab") then { _currentTab = "main"; };

lbClear _mainContentList;

switch (_currentTab) do {
	case "main": {
		// Show licenses and achievements
		{
			private _licenseName = [_x] call EDRP_fnc_getLicenseName;
			private _index = _mainContentList lbAdd _licenseName;
			_mainContentList lbSetData [_index, _x];
			_mainContentList lbSetPicture [_index, format ["images\licenses\%1.paa", _x]];
		} forEach EDRP_player_licenses;
		
		_infoText ctrlSetStructuredText parseText "Your licenses and achievements are displayed above.";
	};
	
	case "inventory": {
		// Show inventory summary
		{
			private _itemName = [_x] call EDRP_fnc_getItemName;
			private _quantity = missionNamespace getVariable [_x, 0];
			if (_quantity > 0) then {
				private _index = _mainContentList lbAdd format ["%1x %2", _quantity, _itemName];
				_mainContentList lbSetData [_index, _x];
				_mainContentList lbSetPicture [_index, [_x] call EDRP_fnc_getItemIcon];
			};
		} forEach EDRP_virtual_items;
		
		_infoText ctrlSetStructuredText parseText "Your virtual inventory items. Double-click to use.";
	};
	
	case "vehicle": {
		// Show owned vehicles
		{
			_x params ["_classname", "_plate", "_location", "_damage"];
			private _vehicleName = [_classname] call EDRP_fnc_getVehicleName;
			private _index = _mainContentList lbAdd format ["%1 (%2)", _vehicleName, _plate];
			_mainContentList lbSetData [_index, str _forEachIndex];
			_mainContentList lbSetPicture [_index, [_classname] call EDRP_fnc_getVehicleIcon];
		} forEach EDRP_player_vehicles;
		
		_infoText ctrlSetStructuredText parseText "Your owned vehicles. Double-click for options.";
	};
	
	case "phone": {
		// Show recent contacts/messages
		{
			_x params ["_name", "_number", "_lastContact"];
			private _index = _mainContentList lbAdd format ["%1 (%2)", _name, _number];
			_mainContentList lbSetData [_index, _number];
			_mainContentList lbSetPicture [_index, "images\icons\contact.paa"];
		} forEach EDRP_phone_contacts;
		
		_infoText ctrlSetStructuredText parseText "Your phone contacts. Double-click to call or message.";
	};
	
	case "stats": {
		// Show player statistics
		private _stats = [
			["XP Earned", EDRP_player_xp],
			["Money Earned", EDRP_player_total_earned],
			["Distance Traveled", EDRP_player_distance_traveled],
			["Time Played", EDRP_player_playtime],
			["Arrests Made", EDRP_player_arrests],
			["Lives Saved", EDRP_player_lives_saved]
		];
		
		{
			_x params ["_statName", "_statValue"];
			private _displayValue = if (_statName == "Time Played") then {
				private _hours = floor (_statValue / 3600);
				private _minutes = floor ((_statValue % 3600) / 60);
				format ["%1h %2m", _hours, _minutes];
			} else {
				[_statValue] call EDRP_fnc_numberText;
			};
			
			private _index = _mainContentList lbAdd format ["%1: %2", _statName, _displayValue];
			_mainContentList lbSetData [_index, _statName];
		} forEach _stats;
		
		_infoText ctrlSetStructuredText parseText "Your gameplay statistics and achievements.";
	};
	
	case "settings": {
		// Show settings options
		private _settings = [
			["HUD Visibility", "Toggle HUD elements"],
			["Sound Settings", "Adjust game sounds"],
			["Key Bindings", "Customize controls"],
			["Graphics Settings", "Adjust visual quality"],
			["Notification Settings", "Configure alerts"]
		];
		
		{
			_x params ["_settingName", "_description"];
			private _index = _mainContentList lbAdd _settingName;
			_mainContentList lbSetData [_index, _settingName];
			_mainContentList lbSetTooltip [_index, _description];
		} forEach _settings;
		
		_infoText ctrlSetStructuredText parseText "Game settings and preferences. Double-click to modify.";
	};
};

// Update nearby players combo
lbClear _nearbyPlayersCombo;
_nearbyPlayersCombo lbAdd "Select Player...";
_nearbyPlayersCombo lbSetData [0, ""];

private _nearbyPlayers = [player, 10] call EDRP_fnc_getNearbyPlayers;
{
	private _index = _nearbyPlayersCombo lbAdd (name _x);
	_nearbyPlayersCombo lbSetData [_index, getPlayerUID _x];
} forEach _nearbyPlayers;

_nearbyPlayersCombo lbSetCurSel 0;

// Update status
_statusText ctrlSetText format ["Status: %1 | Faction: %2", EDRP_player_status, EDRP_player_faction];

// Update action buttons based on current tab
private _actionButton1 = _dialog displayCtrl 3011;
private _actionButton2 = _dialog displayCtrl 3012;
private _actionButton3 = _dialog displayCtrl 3013;
private _actionButton4 = _dialog displayCtrl 3014;

switch (_currentTab) do {
	case "main": {
		_actionButton1 ctrlSetText "Give Money";
		_actionButton2 ctrlSetText "Check Time";
		_actionButton3 ctrlSetText "View Stats";
		_actionButton4 ctrlSetText "Settings";
	};
	
	case "inventory": {
		_actionButton1 ctrlSetText "Use Item";
		_actionButton2 ctrlSetText "Drop Item";
		_actionButton3 ctrlSetText "Give Item";
		_actionButton4 ctrlSetText "Process";
	};
	
	case "vehicle": {
		_actionButton1 ctrlSetText "Locate";
		_actionButton2 ctrlSetText "Retrieve";
		_actionButton3 ctrlSetText "Sell";
		_actionButton4 ctrlSetText "Impound";
	};
	
	case "phone": {
		_actionButton1 ctrlSetText "Call";
		_actionButton2 ctrlSetText "Message";
		_actionButton3 ctrlSetText "Add Contact";
		_actionButton4 ctrlSetText "Delete";
	};
	
	default {
		_actionButton1 ctrlSetText "Action 1";
		_actionButton2 ctrlSetText "Action 2";
		_actionButton3 ctrlSetText "Action 3";
		_actionButton4 ctrlSetText "Action 4";
	};
};
