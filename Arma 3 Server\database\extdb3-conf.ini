[Main]
Version = 1

Randomize Config File = false
;; Randomizes Config File after loading.
;;   Recommend to turn on, if you have enabled filepatching on arma.

Allow Reset = false
;; Allows 9:RESET, usefull for development work

Thread = 0
;; Option to force number of worker threads for extDB3.
;;   Auto = 0, Min = 2, Max = 6

[Log]
Flush = true
;; Flush logfile after each update.
;;    Option really only usefull if running DEBUG BUILD

[Database]
Type = MySQL
Name = Database

Host = 127.0.0.1
Port = 3306
Username = edenrp_user
Password = edenrp_secure_password_2024
Database = edenrp

Compress = true
Secure Auth = true

;; Connection Pool Settings
Min Connections = 1
Max Connections = 10
Connection Timeout = 30

;; MySQL Specific Settings
Auto Reconnect = true
Charset = utf8mb4

[Database2]
Type = MySQL
Name = Database2

Host = 127.0.0.1
Port = 3306
Username = edenrp_user
Password = edenrp_secure_password_2024
Database = edenrp

Compress = true
Secure Auth = true

;; Connection Pool Settings
Min Connections = 1
Max Connections = 5
Connection Timeout = 30

;; MySQL Specific Settings
Auto Reconnect = true
Charset = utf8mb4

[Logging]
Version = 1
Flush = true

;; Log Levels: 0=None, 1=Error, 2=Warning, 3=Info, 4=Debug
Log Level = 2

;; Log File Settings
Log File = logs/extdb3.log
Max Log Size = 10MB
Max Log Files = 5

[Performance]
;; Query Cache Settings
Enable Query Cache = true
Query Cache Size = 1000
Query Cache TTL = 300

;; Connection Pool Optimization
Pool Cleanup Interval = 60
Idle Connection Timeout = 300

;; Statement Preparation
Prepare Statements = true
Statement Cache Size = 100

[Security]
;; SQL Injection Protection
Enable SQL Filtering = true
Max Query Length = 8192
Blocked Keywords = DROP,DELETE,TRUNCATE,ALTER,CREATE,GRANT,REVOKE

;; Connection Security
SSL Mode = PREFERRED
SSL Cert = 
SSL Key = 
SSL CA = 

;; Access Control
Allowed IPs = 127.0.0.1,::1
Max Connections Per IP = 50

[EdenRP_Custom]
;; EdenRP Specific Settings
Server Name = EdenRP Altis Life
Server Version = 1.0.0
Database Prefix = edrp_

;; Backup Settings
Enable Auto Backup = true
Backup Interval = 3600
Backup Location = backups/
Max Backup Files = 24

;; Maintenance Settings
Enable Maintenance Mode = false
Maintenance Message = Server is currently under maintenance. Please try again later.

;; Performance Monitoring
Enable Performance Logging = true
Slow Query Threshold = 1000
Log Slow Queries = true

[SQL_CUSTOM]
;; Custom SQL Templates Directory
Template Directory = SQL_CUSTOM/

;; Template Settings
Enable Templates = true
Template Cache = true
Template Validation = true

;; Custom Query Limits
Max Results = 10000
Query Timeout = 30
Max Concurrent Queries = 100

[Backup]
;; Automated Backup Configuration
Enable = true
Schedule = 0 */6 * * *
;; Runs every 6 hours (cron format)

Backup Types = full,incremental
Retention Days = 7
Compression = gzip

;; Backup Locations
Local Path = backups/local/
Remote Path = 
Remote Type = 

;; Backup Notifications
Email Notifications = false
Email Recipients = 
SMTP Server = 
SMTP Port = 587
SMTP Username = 
SMTP Password = 

[Monitoring]
;; Health Check Settings
Enable Health Checks = true
Health Check Interval = 60
Health Check Timeout = 10

;; Metrics Collection
Enable Metrics = true
Metrics Port = 9090
Metrics Path = /metrics

;; Alerting
Enable Alerts = true
Alert Thresholds = connection_errors:10,slow_queries:50,high_cpu:80
Alert Cooldown = 300

;; External Monitoring
Webhook URL = 
Webhook Secret = 
Webhook Events = error,warning,critical

[Development]
;; Development Mode Settings
Debug Mode = false
Verbose Logging = false
Query Profiling = false

;; Testing Settings
Enable Test Mode = false
Test Database = edenrp_test
Mock Data = false

;; Development Tools
Enable SQL Console = false
Console Port = 3307
Console Password = dev_console_2024
