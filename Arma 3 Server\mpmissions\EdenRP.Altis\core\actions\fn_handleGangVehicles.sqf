//	File: fn_handleGangVehicles.sqf
//	Author: ikiled
//	Description: <PERSON><PERSON> opening of player gang garages via NPCs

if(eden_garageCooldown > time) exitWith {hint "Please do not spam your garage. It may take a bit to show your vehicles if the server is under heavy load.";};
if !((count eden_gang_data) > 0) exitWith {hint "You must be in a gang to access the gang garage."};
if ((eden_gang_data select 2) < 2) exitWith {hint "You must be gang rank 2 or higher to access your gang garage."};

params ["_type","_spawn"];

[[getPlayerUID player,playerSide,_type,player,true,(eden_gang_data select 0)],"EDENS_fnc_getVehicles",false,false] spawn EDEN_fnc_MP;
["Life_impound_menu"] call EDEN_fnc_createDialog;
disableSerialization;
ctrlSetText[2802,"Fetching Vehicles...."];
eden_garage_sp = _spawn;
eden_garage_type = _type;
eden_garageCooldown = time+5;